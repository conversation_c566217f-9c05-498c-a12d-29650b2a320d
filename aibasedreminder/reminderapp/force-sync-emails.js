#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const { google } = require('googleapis');

const prisma = new PrismaClient();

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI || process.env.NEXTAUTH_URL + '/api/auth/google/callback';

function createOAuth2Client() {
  return new google.auth.OAuth2(
    GOOGLE_CLIENT_ID,
    GOO<PERSON>LE_CLIENT_SECRET,
    REDIRECT_URI
  );
}

async function forceSyncEmails() {
  try {
    console.log('🚀 FORCE SYNCING EMAILS TO DATABASE...\n');

    // Get the user with valid token
    const user = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      },
      include: {
        oauthTokens: {
          where: { provider: 'google' }
        },
        telegramUsers: {
          where: { isActive: true }
        }
      }
    });

    if (!user || !user.oauthTokens.length) {
      console.error('❌ No user or tokens found');
      return;
    }

    console.log(`👤 Force syncing for: ${user.email}`);
    console.log(`📱 Active Telegram users: ${user.telegramUsers.length}`);

    const token = user.oauthTokens[0];

    // Set up OAuth client
    const oauth2Client = createOAuth2Client();
    oauth2Client.setCredentials({
      access_token: token.accessToken,
      refresh_token: token.refreshToken,
      expiry_date: token.expiresAt.getTime()
    });

    // Create Gmail service
    const gmail = google.gmail({ version: 'v1', auth: oauth2Client });

    // Get ALL unread emails
    console.log('\n📧 Getting unread emails from Gmail...');
    const unreadMessages = await gmail.users.messages.list({
      userId: 'me',
      q: 'is:unread',
      maxResults: 50
    });

    const messages = unreadMessages.data.messages || [];
    console.log(`📨 Found ${messages.length} unread emails in Gmail`);

    let newEmailsCreated = 0;
    let notificationsSent = 0;

    for (const message of messages) {
      try {
        console.log(`\n🔍 Processing email ID: ${message.id}`);

        // Check if email already exists in database
        const existingEmail = await prisma.email.findUnique({
          where: { gmailMessageId: message.id }
        });

        if (existingEmail) {
          console.log(`⏭️ Email already exists in database: ${existingEmail.subject}`);
          continue;
        }

        // Get full email details
        const emailDetail = await gmail.users.messages.get({
          userId: 'me',
          id: message.id
        });

        const headers = emailDetail.data.payload?.headers || [];
        const subject = headers.find(h => h.name === 'Subject')?.value || 'No Subject';
        const from = headers.find(h => h.name === 'From')?.value || 'Unknown Sender';
        const to = headers.find(h => h.name === 'To')?.value || '';
        const dateHeader = headers.find(h => h.name === 'Date')?.value;
        const receivedAt = dateHeader ? new Date(dateHeader) : new Date();
        
        let body = emailDetail.data.snippet || 'No content available';

        console.log(`📧 NEW EMAIL: "${subject}" from "${from}"`);

        // Create email in database
        const newEmail = await prisma.email.create({
          data: {
            userId: user.id,
            gmailMessageId: message.id,
            threadId: emailDetail.data.threadId || message.id,
            subject: subject,
            from: from,
            to: to,
            body: body.substring(0, 1000),
            receivedAt: receivedAt,
            isRead: false,
            isImportant: false,
            labels: JSON.stringify(emailDetail.data.labelIds || [])
          }
        });

        newEmailsCreated++;
        console.log(`✅ Email saved to database with ID: ${newEmail.id}`);

        // Send Telegram notification to all active users
        for (const telegramUser of user.telegramUsers) {
          const notificationMessage = `🚨 INSTANT EMAIL ALERT!\n\n` +
            `📝 Subject: ${subject}\n` +
            `👤 From: ${from}\n` +
            `📅 Received: ${receivedAt.toLocaleString()}\n` +
            `⚡ Status: UNREAD - Needs Response\n\n` +
            `📄 Preview:\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}\n\n` +
            `💬 Reply now or you'll get reminders every 5 minutes!`;

          const keyboard = {
            inline_keyboard: [
              [
                { text: "💬 Reply to this Email", callback_data: `reply_to_${newEmail.id}` },
                { text: "✅ Mark as Read", callback_data: `mark_read_${newEmail.id}` }
              ],
              [
                { text: "⭐ Star Email", callback_data: `star_email_${newEmail.id}` },
                { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${newEmail.id}` }
              ]
            ]
          };

          console.log(`📱 Sending notification to Telegram user ${telegramUser.telegramId}...`);

          try {
            const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                chat_id: telegramUser.telegramId,
                text: notificationMessage,
                reply_markup: keyboard,
                parse_mode: 'HTML'
              })
            });

            const result = await response.json();

            if (result.ok) {
              notificationsSent++;
              console.log(`📱 ✅ Notification sent successfully!`);
            } else {
              console.error(`📱 ❌ Failed to send notification:`, result);
            }
          } catch (notificationError) {
            console.error(`📱 ❌ Network error sending notification:`, notificationError);
          }
        }

      } catch (emailError) {
        console.error(`❌ Error processing email ${message.id}:`, emailError);
      }
    }

    console.log(`\n🎉 FORCE SYNC COMPLETE!`);
    console.log(`📊 New emails created: ${newEmailsCreated}`);
    console.log(`📱 Notifications sent: ${notificationsSent}`);

  } catch (error) {
    console.error('❌ Force sync failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

forceSyncEmails();
