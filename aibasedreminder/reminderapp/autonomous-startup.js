#!/usr/bin/env node

/**
 * Autonomous Telegram Bot Startup Script
 * This script ensures the Telegram bot runs autonomously without website dependency
 */

const https = require('https');
const { URL } = require('url');

// Configuration
const BASE_URL = process.env.NEXTAUTH_URL || 'https://aibasedreminder.vercel.app';
const STARTUP_ENDPOINT = `${BASE_URL}/api/telegram/autonomous-init`;

console.log('🚀 Starting Autonomous Telegram Bot System...');
console.log(`Base URL: ${BASE_URL}`);

// Function to make HTTP requests
function makeRequest(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'AutonomousBot/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve(result);
        } catch (error) {
          resolve({ statusCode: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Initialize autonomous system
async function initializeAutonomousSystem() {
  try {
    console.log('\n🔄 Initializing autonomous Telegram bot...');
    
    const result = await makeRequest(STARTUP_ENDPOINT, 'GET');
    
    if (result.success) {
      console.log('✅ Autonomous system started successfully!');
      console.log(`📱 Webhook: ${result.data.webhook.ok ? 'Active' : 'Failed'}`);
      console.log(`🔍 Monitoring: ${result.data.monitoring ? 'Active' : 'Inactive'}`);
      console.log(`⏰ Started at: ${result.data.timestamp}`);
      
      console.log('\n🎉 Telegram bot is now running autonomously!');
      console.log('📧 Email monitoring: Every 2 minutes');
      console.log('⏰ Reminders: Every 5 minutes for unread emails');
      console.log('💬 Bot responses: Immediate via webhook');
      
    } else {
      console.log('❌ Failed to start autonomous system:');
      console.log(result);
    }
    
  } catch (error) {
    console.error('❌ Error initializing autonomous system:', error.message);
  }
}

// Health check function
async function healthCheck() {
  try {
    console.log('\n🏥 Performing health check...');
    
    const healthUrl = `${BASE_URL}/api/health`;
    const result = await makeRequest(healthUrl);
    
    if (result.success) {
      console.log('✅ System health: OK');
    } else {
      console.log('⚠️ System health check failed');
    }
    
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
  }
}

// Main execution
async function main() {
  console.log('🤖 Autonomous Telegram Bot Startup');
  console.log('=====================================');
  
  // Wait a moment for system to be ready
  console.log('⏳ Waiting for system to be ready...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Perform health check
  await healthCheck();
  
  // Initialize autonomous system
  await initializeAutonomousSystem();
  
  console.log('\n📋 Next steps:');
  console.log('1. The bot is now running autonomously');
  console.log('2. Users will receive email notifications automatically');
  console.log('3. No website interaction required');
  console.log('4. Monitor logs for autonomous operation');
  
  console.log('\n🔧 To stop autonomous mode:');
  console.log(`POST ${BASE_URL}/api/telegram/autonomous-init`);
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Run the startup
main().catch(error => {
  console.error('❌ Startup failed:', error);
  process.exit(1);
});
