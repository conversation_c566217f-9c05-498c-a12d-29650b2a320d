#!/bin/bash

# 🚀 AI Reminder App - Vercel Deployment Script
# Run this script to deploy your app to Vercel

echo "🚀 Starting AI Reminder App deployment to Vercel..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the app root directory."
    exit 1
fi

# Install Vercel CLI locally if not installed globally
if ! command -v vercel &> /dev/null; then
    echo "📦 Installing Vercel CLI locally..."
    npx vercel --version
fi

echo "✅ Vercel CLI is available"

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Build the application
echo "🏗️  Building the application..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix the errors and try again."
    exit 1
fi

echo "✅ Build successful!"

# Deploy to Vercel
echo "🚀 Deploying to Vercel..."
npx vercel --prod

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Deployment successful!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Configure environment variables in Vercel dashboard"
    echo "2. Update Google OAuth redirect URI"
    echo "3. Update Telegram webhook URL"
    echo "4. Run database migration"
    echo ""
    echo "📖 See VERCEL_ENV_VARS.md for detailed instructions"
else
    echo "❌ Deployment failed. Please check the errors above."
    exit 1
fi
