# AI Reminder Backend

Backend API for the AI-based reminder web application that helps users avoid missing meetings and forgetting to respond to emails.

## Features

- 🔐 **Authentication & User Management** - OAuth2 with Google services
- 📅 **Google Calendar Integration** - Full sync with real-time updates
- 📧 **Gmail Integration** - Email monitoring and reply functionality
- 🤖 **AI-Powered Features** - Smart reply suggestions with OpenAI
- 📱 **Telegram Bot** - Notifications and interactive responses
- ⏰ **Smart Reminders** - Intelligent scheduling and notifications
- 🔒 **Security First** - Comprehensive validation and protection

## Tech Stack

- **Framework:** Next.js 15 with App Router (TypeScript)
- **Database:** PostgreSQL via Neon with Prisma ORM
- **APIs:** Google Calendar, Gmail, Telegram Bot, OpenAI
- **Authentication:** OAuth2 for Google services
- **Validation:** Zod for request validation
- **Security:** JWT tokens, rate limiting, input sanitization

## Quick Start

1. **Install dependencies:** `npm install`
2. **Setup environment:** Copy `.env.example` to `.env` and configure
3. **Setup database:** `npm run db:generate && npm run db:push`
4. **Start development:** `npm run dev`
5. **Visit:** `http://localhost:3001`

```

## Setup Instructions

### 1. Environment Configuration

Copy the example environment file and configure your variables:

```bash
cp .env.example .env
```

Fill in the required environment variables:
- `DATABASE_URL` - Your Neon PostgreSQL connection string
- `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET` - Google OAuth credentials
- `TELEGRAM_BOT_TOKEN` - Your Telegram bot token
- `OPENAI_API_KEY` - OpenAI API key for AI features
- `NEXTAUTH_SECRET` - Secret for session management

### 2. Database Setup

Generate Prisma client and push schema to database:

```bash
npm run db:generate
npm run db:push
```


## License

Private - All rights reserved
