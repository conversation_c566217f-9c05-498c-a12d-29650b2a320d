#!/bin/bash

# 🗄️ Production Database Migration Script
# Run this AFTER deploying to Vercel and setting up environment variables

echo "🗄️ Running production database migration..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the app root directory."
    exit 1
fi

# Pull environment variables from Vercel
echo "📥 Pulling environment variables from Vercel..."
npx vercel env pull .env.local

if [ $? -ne 0 ]; then
    echo "❌ Failed to pull environment variables. Make sure you're logged in to Vercel."
    echo "💡 Run: npx vercel login"
    exit 1
fi

# Generate Prisma client with production schema
echo "🔧 Generating Prisma client..."
npx prisma generate

# Deploy database migrations
echo "🚀 Deploying database migrations to production..."
npx prisma migrate deploy

if [ $? -eq 0 ]; then
    echo "✅ Database migration successful!"
    echo ""
    echo "🎉 Your AI Reminder App is now fully deployed and ready!"
    echo ""
    echo "📋 Final steps:"
    echo "1. Update Google OAuth redirect URI to your Vercel domain"
    echo "2. Update Telegram webhook to your Vercel domain"
    echo "3. Test the application"
else
    echo "❌ Database migration failed. Please check the errors above."
    echo ""
    echo "🔧 Troubleshooting:"
    echo "1. Verify DATABASE_URL is correctly set in Vercel"
    echo "2. Check Neon database is accessible"
    echo "3. Ensure all environment variables are configured"
    exit 1
fi
