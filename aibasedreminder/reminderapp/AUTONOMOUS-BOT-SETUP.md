# 🤖 Autonomous Telegram Bot Setup

## 🎯 Overview

This system makes your Telegram bot **completely autonomous** - it operates independently without requiring the website to be active. The bot will:

- ✅ **Respond to users immediately** via webhook
- ✅ **Monitor emails every 2 minutes** autonomously  
- ✅ **Send reminders every 5 minutes** for unread emails
- ✅ **Allow direct email replies** from Telegram
- ✅ **Work 24/7** without website dependency

## 🚀 Quick Start

### Option 1: Automatic (Recommended)
The system auto-starts via Vercel cron job every 10 minutes:
```
/api/telegram/autonomous-init (runs every 10 minutes)
```

### Option 2: Manual Start
```bash
# From the reminderapp directory
npm run autonomous

# Or directly
node autonomous-startup.js
```

### Option 3: API Call
```bash
curl https://your-app.vercel.app/api/telegram/autonomous-init
```

## 🔧 How It Works

### 1. **Autonomous Email Monitoring**
- Checks emails every **2 minutes** automatically
- No website interaction required
- Direct database and Gmail API access
- Immediate Telegram notifications with reply buttons

### 2. **Independent Bot Responses**
- Webhook processes all user interactions
- Asynchronous message handling for speed
- Real-time reply functionality
- No polling dependencies

### 3. **Smart Reminders**
- Recurring reminders every **5 minutes** for unread emails
- Automatic reminder stopping when emails are read
- Interactive buttons for quick actions

## 💬 Enhanced Reply System

### User Experience:
1. **Receive Email Notification** → Click "💬 Reply" button
2. **Bot Prompts** → "Simply type your reply message below"
3. **User Types** → "Thanks for your email!"
4. **Bot Sends** → Via Gmail API with proper threading
5. **Confirmation** → "✅ Reply sent successfully!"

### Commands:
- `/reply` - Show interactive email list
- `/cancel` - Cancel current reply
- `/status` - Check bot and reply status
- `/emails` - View recent emails

## 🔄 System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Vercel Cron   │───▶│  Autonomous Init │───▶│  Email Monitor  │
│  (Every 10min)  │    │   /api/telegram/ │    │  (Every 2min)   │
└─────────────────┘    │  autonomous-init │    └─────────────────┘
                       └──────────────────┘             │
                                │                       ▼
┌─────────────────┐             │              ┌─────────────────┐
│ Telegram Users  │◀────────────┼──────────────│ Gmail API Check │
│   (Instant)     │             │              │  New Emails     │
└─────────────────┘             ▼              └─────────────────┘
         ▲              ┌──────────────────┐             │
         │              │  Webhook Handler │             │
         │              │   (Immediate     │             ▼
         └──────────────│    Response)     │    ┌─────────────────┐
                        └──────────────────┘    │ Send Telegram   │
                                               │  Notifications  │
                                               └─────────────────┘
```

## 📱 Bot Features

### Immediate Responses:
- All commands work instantly
- No website refresh needed
- Real-time interaction

### Email Management:
- **New Email Alerts** with reply buttons
- **Recurring Reminders** every 5 minutes
- **Direct Reply** functionality
- **Mark as Read** from Telegram
- **Star Important** emails
- **Stop Reminders** option

### User State Management:
- Remembers reply conversations
- 10-minute reply timeout
- Cancel anytime with `/cancel`

## 🛠️ Configuration

### Environment Variables Required:
```bash
TELEGRAM_BOT_TOKEN=your_bot_token
NEXTAUTH_URL=https://your-app.vercel.app
DATABASE_URL=your_neon_database_url
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

### Vercel Cron Jobs:
```json
{
  "crons": [
    {
      "path": "/api/telegram/autonomous-init",
      "schedule": "*/10 * * * *"
    }
  ]
}
```

## 🔍 Monitoring & Health Checks

### Check Bot Status:
```bash
curl https://your-app.vercel.app/api/telegram/webhook
```

### Check Autonomous System:
```bash
curl https://your-app.vercel.app/api/telegram/autonomous-init
```

### Stop Autonomous Mode:
```bash
curl -X POST https://your-app.vercel.app/api/telegram/autonomous-init
```

## 🐛 Troubleshooting

### Bot Not Responding:
1. Check webhook status: `GET /api/telegram/webhook`
2. Verify bot token in environment variables
3. Check Vercel function logs

### No Email Notifications:
1. Verify Google OAuth tokens are valid
2. Check user has Telegram connected
3. Ensure autonomous system is running

### Reply Not Working:
1. User should click email button first
2. Check if user is in reply mode: `/status`
3. Use `/cancel` to reset reply state

## 🎉 Success Indicators

When working correctly, you'll see:
- ✅ **Immediate bot responses** to all commands
- ✅ **Email notifications within 2 minutes** of receiving emails
- ✅ **Recurring reminders every 5 minutes** for unread emails
- ✅ **Successful email replies** sent via Gmail API
- ✅ **No website dependency** - works even when site is closed

## 📞 Support Commands

For users:
- `/start` - Welcome and setup guide
- `/help` - Complete command list
- `/status` - Account and reply status
- `/reply` - Interactive email reply
- `/cancel` - Cancel current action
- `/disconnect` - Unlink account

The bot is now **truly autonomous** and provides a seamless email management experience directly from Telegram! 🚀
