# 🚀 Vercel Deployment Guide

## ✅ Prerequisites Completed

1. **✅ Neon PostgreSQL Database** - Already configured
2. **✅ Google OAuth2 Credentials** - Already configured
3. **✅ Telegram <PERSON><PERSON>** - Already configured
4. **✅ PostgreSQL Dependencies** - Already installed
5. **✅ Database Schema** - Updated for PostgreSQL

## 🎯 Your Database Connection String
```
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
```

## Step 2: Update Google OAuth2 Redirect URI

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services > Credentials
3. Edit your OAuth 2.0 Client ID
4. Add your Vercel domain to authorized redirect URIs:
   ```
   https://your-app-name.vercel.app/api/auth/google/callback
   ```

## Step 3: Deploy to Vercel

### Option A: Deploy via Vercel CLI
```bash
npm install -g vercel
vercel
```

### Option B: Deploy via GitHub
1. Push your code to GitHub
2. Connect your GitHub repo to Vercel
3. Vercel will auto-deploy

## Step 4: Configure Environment Variables in Vercel

Go to your Vercel project dashboard > Settings > Environment Variables and add:

### Required Variables:
```
DATABASE_URL=****************************************/database?sslmode=require
NEXTAUTH_URL=https://your-app-name.vercel.app
NEXTAUTH_SECRET=your-production-nextauth-secret-here
GOOGLE_CLIENT_ID=922566304374-2v00314vjkccfblvgrk20t3nig978ou0.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=https://your-app-name.vercel.app/api/auth/google/callback
TELEGRAM_BOT_TOKEN=**********************************************
JWT_SECRET=your-production-jwt-secret-here
NODE_ENV=production
FRONTEND_URL=https://your-app-name.vercel.app
```

### Optional Variables:
```
OPENAI_API_KEY=your-openai-api-key
OAUTH_CONSENT_SCREEN_TYPE=external
OAUTH_BYPASS_VERIFICATION=false
ENABLE_AI_FEATURES=true
ENABLE_TELEGRAM_BOT=true
ENABLE_EMAIL_SYNC=true
ENABLE_CALENDAR_SYNC=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
LOG_LEVEL=info
CORS_ORIGIN=https://your-app-name.vercel.app
COOKIE_SECURE=true
SESSION_TIMEOUT=86400000
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30000
DATABASE_POOL_SIZE=10
WEBHOOK_URL=https://your-app-name.vercel.app
WEBHOOK_SECRET=your-production-webhook-secret
HEALTH_CHECK_INTERVAL=60000
METRICS_ENABLED=true
DEBUG_MODE=false
VERBOSE_LOGGING=false
MOCK_EXTERNAL_APIS=false
```

## Step 5: Run Database Migration

After deployment, run the database migration:

1. Install Vercel CLI: `npm install -g vercel`
2. Link your project: `vercel link`
3. Run migration: `vercel env pull .env.local && npx prisma migrate deploy`

## Step 6: Update Telegram Webhook

Update your Telegram bot webhook to point to your Vercel domain:

```bash
curl -X POST "https://api.telegram.org/bot**********************************************/setWebhook" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://your-app-name.vercel.app/api/telegram/webhook"}'
```

## Step 7: Test Deployment

1. Visit your Vercel URL
2. Sign in with Google
3. Connect Telegram bot
4. Test email notifications

## Troubleshooting

### Database Connection Issues
- Ensure DATABASE_URL is correctly formatted
- Check Neon database is running
- Verify connection string includes `?sslmode=require`

### OAuth Issues
- Verify NEXTAUTH_URL matches your Vercel domain
- Check Google OAuth redirect URI is updated
- Ensure NEXTAUTH_SECRET is set

### Telegram Issues
- Update webhook URL to your Vercel domain
- Verify TELEGRAM_BOT_TOKEN is correct

## Production Checklist

- [ ] Neon PostgreSQL database created
- [ ] Google OAuth redirect URI updated
- [ ] All environment variables configured in Vercel
- [ ] Database migration completed
- [ ] Telegram webhook updated
- [ ] App tested and working

🎉 Your AI Reminder App is now live on Vercel!
