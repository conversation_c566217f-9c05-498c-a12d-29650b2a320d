# Production Environment Variables for Vercel Deployment
# Copy these to your Vercel Environment Variables dashboard

# Database (Neon PostgreSQL)
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"

# Frontend API URL (auto-detected in production, leave empty)
NEXT_PUBLIC_API_URL=""

# NextAuth.js
NEXTAUTH_URL="https://your-app-name.vercel.app"
NEXTAUTH_SECRET="ai-reminder-nextauth-secret-2024-production"

# Google OAuth2 (same as development)
GOOGLE_CLIENT_ID="922566304374-2v00314vjkccfblvgrk20t3nig978ou0.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
GOOGLE_REDIRECT_URI="https://your-app-name.vercel.app/api/auth/google/callback"

# OAuth Configuration
OAUTH_CONSENT_SCREEN_TYPE="external"
OAUTH_BYPASS_VERIFICATION="false"

# Email Configuration
ENABLE_EMAIL_AUTH="true"
ENABLE_MAGIC_LINKS="true"
DEFAULT_USER_EMAIL=""

# OpenAI API
OPENAI_API_KEY="your-openai-api-key"

# Telegram Bot
TELEGRAM_BOT_TOKEN="**********************************************"

# Frontend URL
FRONTEND_URL="https://your-app-name.vercel.app"

# JWT Secret
JWT_SECRET="your-production-jwt-secret-here"

# Environment
NODE_ENV="production"

# Features
ENABLE_AI_FEATURES="true"
ENABLE_TELEGRAM_BOT="true"
ENABLE_EMAIL_SYNC="true"
ENABLE_CALENDAR_SYNC="true"

# Rate Limiting
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX_REQUESTS="100"

# Logging
LOG_LEVEL="info"

# CORS
CORS_ORIGIN="https://your-app-name.vercel.app"
COOKIE_SECURE="true"
SESSION_TIMEOUT="86400000"

# Performance
MAX_CONCURRENT_REQUESTS="100"
REQUEST_TIMEOUT="30000"
DATABASE_POOL_SIZE="10"

# Webhook
WEBHOOK_URL="https://your-app-name.vercel.app"
WEBHOOK_SECRET="your-production-webhook-secret"

# Health Check
HEALTH_CHECK_INTERVAL="60000"
METRICS_ENABLED="true"

# Debug (disabled in production)
DEBUG_MODE="false"
VERBOSE_LOGGING="false"
MOCK_EXTERNAL_APIS="false"

# SMTP Configuration (optional)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
