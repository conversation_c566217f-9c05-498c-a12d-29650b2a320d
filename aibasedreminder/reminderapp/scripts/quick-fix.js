const { PrismaClient } = require('@prisma/client')

async function quickFix() {
  const prisma = new PrismaClient()
  
  try {
    console.log('🔄 Testing database connection...')
    
    // Test connection
    await prisma.$connect()
    console.log('✅ Database connected successfully')
    
    // Try to create tables if they don't exist
    console.log('🔄 Ensuring database schema...')
    await prisma.$executeRaw`SELECT 1`
    console.log('✅ Database schema ready')
    
    console.log('🎉 Quick fix completed successfully!')
    
  } catch (error) {
    console.error('❌ Quick fix failed:', error.message)
    
    if (error.code === 'P1001') {
      console.error('💡 Database connection failed. Check your DATABASE_URL')
    } else if (error.code === 'P1009') {
      console.error('💡 Database does not exist. Check your database name')
    } else {
      console.error('💡 Unknown database error:', error.code)
    }
  } finally {
    await prisma.$disconnect()
  }
}

quickFix()
