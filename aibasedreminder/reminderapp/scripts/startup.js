#!/usr/bin/env node

const { spawn } = require('child_process')

console.log('🚀 Starting ReminderAPP...')

// Start the Next.js development server
const nextProcess = spawn('npm', ['run', 'dev:next'], {
  stdio: 'inherit',
  shell: true
})

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...')
  nextProcess.kill('SIGINT')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down...')
  nextProcess.kill('SIGTERM')
  process.exit(0)
})

console.log('✅ Next.js server started on http://localhost:3001')
console.log('📝 Services will auto-initialize when the app loads')
console.log('🎉 ReminderAPP is ready!')
