#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const { google } = require('googleapis');

const prisma = new PrismaClient();

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI || process.env.NEXTAUTH_URL + '/api/auth/google/callback';

function createOAuth2Client() {
  return new google.auth.OAuth2(
    GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET,
    REDIRECT_URI
  );
}

async function fixDatabase() {
  try {
    console.log('🔧 FIXING DATABASE ISSUES...\n');

    // 1. REACTIVATE ALL TELEGRAM USERS
    console.log('📱 Reactivating all Telegram users...');
    const telegramUpdate = await prisma.telegramUser.updateMany({
      where: {
        isActive: false
      },
      data: {
        isActive: true,
        updatedAt: new Date()
      }
    });
    console.log(`✅ Reactivated ${telegramUpdate.count} Telegram users\n`);

    // 2. REFRESH EXPIRED GOOGLE TOKENS
    console.log('🔄 Refreshing expired Google tokens...');
    
    const expiredTokens = await prisma.oAuthToken.findMany({
      where: {
        provider: 'google',
        expiresAt: {
          lt: new Date()
        },
        refreshToken: {
          not: null
        }
      },
      include: {
        user: true
      }
    });

    console.log(`Found ${expiredTokens.length} expired tokens to refresh`);

    for (const tokenRecord of expiredTokens) {
      try {
        console.log(`🔄 Refreshing token for ${tokenRecord.user.email}...`);

        const oauth2Client = createOAuth2Client();
        oauth2Client.setCredentials({
          refresh_token: tokenRecord.refreshToken
        });

        const { credentials } = await oauth2Client.refreshAccessToken();

        await prisma.oAuthToken.update({
          where: { id: tokenRecord.id },
          data: {
            accessToken: credentials.access_token,
            expiresAt: new Date(credentials.expiry_date || Date.now() + 3600000),
            updatedAt: new Date()
          }
        });

        console.log(`✅ Successfully refreshed token for ${tokenRecord.user.email}`);

      } catch (error) {
        console.error(`❌ Failed to refresh token for ${tokenRecord.user.email}:`, error.message);
      }
    }

    // 3. VERIFY FINAL STATUS
    console.log('\n🔍 Verifying fixes...');
    
    const users = await prisma.user.findMany({
      where: {
        oauthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        },
        telegramUsers: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    });

    console.log(`📊 Users ready for email notifications: ${users.length}`);
    
    for (const user of users) {
      const validTokens = user.oauthTokens.filter(t => t.expiresAt > new Date());
      console.log(`✅ ${user.email} - Active Telegram: ${user.telegramUsers.length}, Valid Google tokens: ${validTokens.length}`);
    }

    console.log('\n🎉 DATABASE FIXES COMPLETED!');
    console.log('✅ All Telegram users reactivated');
    console.log('✅ Expired tokens refreshed');
    console.log('✅ Email notifications should now work');

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixDatabase();
