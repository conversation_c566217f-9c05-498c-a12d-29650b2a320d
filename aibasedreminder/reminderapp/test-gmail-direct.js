#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const { google } = require('googleapis');

const prisma = new PrismaClient();

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI || process.env.NEXTAUTH_URL + '/api/auth/google/callback';

function createOAuth2Client() {
  return new google.auth.OAuth2(
    GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET,
    REDIRECT_URI
  );
}

async function testGmailDirect() {
  try {
    console.log('🧪 TESTING GMAIL API DIRECTLY...\n');

    // Get the user with valid token
    const user = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      },
      include: {
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    });

    if (!user || !user.oauthTokens.length) {
      console.error('❌ No user or tokens found');
      return;
    }

    console.log(`👤 Testing Gmail for: ${user.email}`);
    const token = user.oauthTokens[0];
    console.log(`🔑 Token expires: ${token.expiresAt}`);
    console.log(`🔑 Token valid: ${token.expiresAt > new Date()}`);

    // Set up OAuth client
    const oauth2Client = createOAuth2Client();
    oauth2Client.setCredentials({
      access_token: token.accessToken,
      refresh_token: token.refreshToken,
      expiry_date: token.expiresAt.getTime()
    });

    // Create Gmail service
    const gmail = google.gmail({ version: 'v1', auth: oauth2Client });

    console.log('\n📧 Testing Gmail API...');

    // Test 1: Get user profile
    console.log('1️⃣ Getting Gmail profile...');
    const profile = await gmail.users.getProfile({ userId: 'me' });
    console.log(`✅ Gmail profile: ${profile.data.emailAddress}`);
    console.log(`📊 Total messages: ${profile.data.messagesTotal}`);
    console.log(`📊 Threads total: ${profile.data.threadsTotal}`);

    // Test 2: Get ALL messages (recent)
    console.log('\n2️⃣ Getting recent messages...');
    const allMessages = await gmail.users.messages.list({
      userId: 'me',
      maxResults: 10
    });
    console.log(`📨 Found ${allMessages.data.messages?.length || 0} recent messages`);

    // Test 3: Get UNREAD messages
    console.log('\n3️⃣ Getting unread messages...');
    const unreadMessages = await gmail.users.messages.list({
      userId: 'me',
      q: 'is:unread',
      maxResults: 20
    });
    console.log(`📨 Found ${unreadMessages.data.messages?.length || 0} unread messages`);

    // Test 4: Show details of unread messages
    if (unreadMessages.data.messages && unreadMessages.data.messages.length > 0) {
      console.log('\n4️⃣ Unread message details:');
      
      for (let i = 0; i < Math.min(3, unreadMessages.data.messages.length); i++) {
        const msg = unreadMessages.data.messages[i];
        console.log(`\n📧 Message ${i + 1}:`);
        console.log(`   ID: ${msg.id}`);
        
        try {
          const detail = await gmail.users.messages.get({
            userId: 'me',
            id: msg.id
          });
          
          const headers = detail.data.payload?.headers || [];
          const subject = headers.find(h => h.name === 'Subject')?.value || 'No Subject';
          const from = headers.find(h => h.name === 'From')?.value || 'Unknown';
          const date = headers.find(h => h.name === 'Date')?.value || 'No Date';
          
          console.log(`   Subject: ${subject}`);
          console.log(`   From: ${from}`);
          console.log(`   Date: ${date}`);
          console.log(`   Snippet: ${detail.data.snippet || 'No snippet'}`);
          
        } catch (detailError) {
          console.error(`   ❌ Error getting details: ${detailError.message}`);
        }
      }
    } else {
      console.log('📭 No unread messages found');
    }

    // Test 5: Check database emails
    console.log('\n5️⃣ Checking database emails...');
    const dbEmails = await prisma.email.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
      take: 5,
      select: {
        id: true,
        gmailMessageId: true,
        subject: true,
        from: true,
        isRead: true,
        createdAt: true
      }
    });
    console.log(`💾 Found ${dbEmails.length} emails in database`);
    
    if (dbEmails.length > 0) {
      console.log('📧 Recent database emails:');
      dbEmails.forEach((email, i) => {
        console.log(`   ${i + 1}. ${email.subject} (${email.isRead ? 'READ' : 'UNREAD'})`);
        console.log(`      Gmail ID: ${email.gmailMessageId}`);
        console.log(`      Created: ${email.createdAt}`);
      });
    }

    console.log('\n🎉 Gmail API test completed successfully!');

  } catch (error) {
    console.error('❌ Gmail API test failed:', error);
    
    if (error.code === 401) {
      console.error('🔑 Authentication error - token may be expired or invalid');
    } else if (error.code === 403) {
      console.error('🚫 Permission error - check Gmail API scopes');
    } else if (error.code === 429) {
      console.error('⏰ Rate limit error - too many requests');
    }
  } finally {
    await prisma.$disconnect();
  }
}

testGmailDirect();
