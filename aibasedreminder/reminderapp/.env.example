# Database
DATABASE_URL=""

# NextAuth.js
NEXTAUTH_URL="http://localhost:3001"
NEXTAUTH_SECRET=""

# Google OAuth2
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# OpenAI API
OPENAI_API_KEY=""

# Telegram Bot
TELEGRAM_BOT_TOKEN=":-"
TELEGRAM_WEBHOOK_URL="k"

# JWT
JWT_SECRET=""

# App Configuration
NODE_ENV=""
PORT=3001
FRONTEND_URL="https://reminder-frontend-rose.vercel.app"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (Optional - for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""






