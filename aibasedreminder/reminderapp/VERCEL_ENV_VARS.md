# 🚀 Vercel Environment Variables Setup

## Copy these to your Vercel Dashboard → Settings → Environment Variables

### 🔴 REQUIRED Variables (Production):

```bash

DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require


NEXTAUTH_URL=https://your-app-name.vercel.app
NEXTAUTH_SECRET=ai-reminder-nextauth-secret-2024-production


GOOGLE_CLIENT_ID=922566304374-2v00314vjkccfblvgrk20t3nig978ou0.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=https://your-app-name.vercel.app/api/auth/google/callback


TELEGRAM_BOT_TOKEN=**********************************************


JWT_SECRET=ai-reminder-super-secret-jwt-key-2024-production


NODE_ENV=production

FRONTEND_URL=https://your-app-name.vercel.app
```

### 🟡 OPTIONAL Variables:

```bash
OAUTH_CONSENT_SCREEN_TYPE=external
OAUTH_BYPASS_VERIFICATION=false

ENABLE_EMAIL_AUTH=true
ENABLE_MAGIC_LINKS=true

ENABLE_AI_FEATURES=true
ENABLE_TELEGRAM_BOT=true
ENABLE_EMAIL_SYNC=true
ENABLE_CALENDAR_SYNC=true

RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

LOG_LEVEL=info

CORS_ORIGIN=https://your-app-name.vercel.app
COOKIE_SECURE=true
SESSION_TIMEOUT=86400000

MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30000
DATABASE_POOL_SIZE=10

WEBHOOK_URL=https://your-app-name.vercel.app
WEBHOOK_SECRET=ai-reminder-webhook-secret-2024

HEALTH_CHECK_INTERVAL=60000
METRICS_ENABLED=true


DEBUG_MODE=false
VERBOSE_LOGGING=false
MOCK_EXTERNAL_APIS=false


SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
```

## 📝 Important Notes:

1. **Replace `your-app-name`** with your actual Vercel app name
2. **Set all variables** in Vercel dashboard before deployment
3. **Update Google OAuth redirect URI** to match your Vercel domain
4. **Update Telegram webhook** after deployment
