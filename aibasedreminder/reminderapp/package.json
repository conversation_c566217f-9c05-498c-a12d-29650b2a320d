{"name": "reminderapp", "version": "1.0.0", "private": true, "scripts": {"build": "prisma generate && next build", "dev": "node scripts/startup.js", "dev:next": "next dev -p 3001", "dev:simple": "next dev -p 3001", "autonomous": "node autonomous-startup.js", "bot:start": "node autonomous-startup.js", "lint": "next lint", "start": "next start -p 3001", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:studio": "prisma studio", "postinstall": "prisma generate"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/pg": "^8.15.4", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "cors": "^2.8.5", "date-fns": "^3.6.0", "embla-carousel-react": "8.5.1", "express-rate-limit": "^7.4.1", "googleapis": "^144.0.0", "helmet": "^8.0.0", "input-otp": "1.4.1", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "next": "^15.2.4", "next-themes": "^0.4.4", "node-fetch": "^2.7.0", "node-telegram-bot-api": "^0.66.0", "openai": "^4.67.3", "pg": "^8.16.3", "prisma": "^5.22.0", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-query": "^3.39.3", "react-resizable-panels": "^2.1.7", "react-snowfall": "^2.2.0", "recharts": "2.15.0", "sonner": "^1.7.1", "swr": "^2.3.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "vaul": "^0.9.6", "zod": "^3.24.1", "zustand": "^4.5.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22", "@types/node-telegram-bot-api": "^0.64.7", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^8", "eslint-config-next": "^15.2.4", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}, "description": "AI-based reminder web application with backend and frontend"}