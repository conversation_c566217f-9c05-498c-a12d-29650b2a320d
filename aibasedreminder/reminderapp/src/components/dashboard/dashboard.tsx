"use client"

import { useState, useEffect } from "react"
import {
  Calendar,
  Mail,
  Bell,
  BarChart3,
  Settings,
  Zap,
  RefreshCw,
  Plus,
  MessageSquare,
  Crown,
  TrendingUp,
  ArrowLeft,
  Home,
  LogOut
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useAuthStore } from "@/lib/stores/auth-store"
import { useCalendarStore } from "@/lib/stores/calendar-store"
import { useEmailStore } from "@/lib/stores/email-store"
import { CalendarView } from "./calendar-view"
import { EmailView } from "./email-view"
import { RemindersView } from "./reminders-view"
import { AnalyticsView } from "./analytics-view"
import { SettingsView } from "./settings-view"
import { AIFeaturesView } from "./ai-features-view"
import { TelegramView } from "./telegram-view"
import { ConnectionStatus } from "./connection-status"
import { QuickActions } from "./quick-actions"
import { toast } from "sonner"

interface DashboardProps {
  onClose?: () => void
}

export function Dashboard({ onClose }: DashboardProps = {}) {
  const [activeTab, setActiveTab] = useState("overview")
  const { user, userProfile, getCurrentUser, logout } = useAuthStore()
  const { getEvents, syncCalendar, isSyncing: calendarSyncing } = useCalendarStore()
  const { getEmails, syncEmails, isSyncing: emailSyncing } = useEmailStore()

  useEffect(() => {
    // Initialize dashboard data only once when user is available
    const initializeDashboard = async () => {
      try {
        if (!userProfile) {
          await getCurrentUser()
        }
        await Promise.all([
          getEvents({ sync: false }),
          getEmails({ sync: false })
        ])
      } catch (error) {
        console.error('Failed to initialize dashboard:', error)
      }
    }

    if (user && !userProfile) {
      initializeDashboard()
    }
  }, [user, userProfile, getCurrentUser, getEvents, getEmails])

  const handleSyncAll = async () => {
    try {
      toast.info("Syncing all data...")
      await Promise.all([
        syncCalendar({ createReminders: true }),
        syncEmails({ createReminders: true })
      ])
      toast.success("All data synced successfully!")
    } catch (error) {
      toast.error("Failed to sync data")
    }
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-amber-600 to-amber-700 mx-auto">
            <Zap className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-white">Loading Dashboard...</h2>
          <p className="text-gray-400">Please wait while we set up your workspace</p>
        </div>
      </div>
    )
  }

  const connections = userProfile?.connections
  const stats = userProfile?.stats

  return (
    <div className="min-h-screen bg-black">
      {/* Header */}
      <div className="border-b border-gray-800 bg-black/95 backdrop-blur supports-[backdrop-filter]:bg-black/80">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-4">
              {onClose && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="text-gray-400 hover:text-white hover:bg-gray-800"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Landing
                </Button>
              )}
              <div className="flex items-center space-x-2">
                <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-amber-600 to-amber-700">
                  <Zap className="w-4 h-4 text-white" />
                </div>
                <h1 className="text-xl font-bold text-white">ReminderAPP Dashboard</h1>
              </div>
              {user.isPro && (
                <Badge className="bg-gradient-to-r from-amber-600 to-amber-700 text-white">
                  <Crown className="w-3 h-3 mr-1" />
                  Pro
                </Badge>
              )}
            </div>

            <div className="flex items-center space-x-4">
              <ConnectionStatus connections={connections} />
              <Button
                onClick={handleSyncAll}
                disabled={calendarSyncing || emailSyncing}
                variant="outline"
                size="sm"
                className="border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${(calendarSyncing || emailSyncing) ? 'animate-spin' : ''}`} />
                Sync All
              </Button>
              <Button
                onClick={async () => {
                  await logout()
                  onClose?.()
                  toast.success("Logged out successfully")
                }}
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white hover:bg-gray-800"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-7 lg:grid-cols-8 bg-gray-900 border border-gray-800">
            <TabsTrigger value="overview" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
              <BarChart3 className="w-4 h-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="calendar" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
              <Calendar className="w-4 h-4 mr-2" />
              Calendar
            </TabsTrigger>
            <TabsTrigger value="emails" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
              <Mail className="w-4 h-4 mr-2" />
              Emails
            </TabsTrigger>
            <TabsTrigger value="reminders" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
              <Bell className="w-4 h-4 mr-2" />
              Reminders
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
              <TrendingUp className="w-4 h-4 mr-2" />
              Analytics
            </TabsTrigger>
            {user.isPro && (
              <TabsTrigger value="ai-features" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
                <Zap className="w-4 h-4 mr-2" />
                AI Features
              </TabsTrigger>
            )}
            <TabsTrigger value="telegram" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
              <MessageSquare className="w-4 h-4 mr-2" />
              Telegram
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {/* Quick Stats */}
              <Card className="bg-gray-900 border-gray-800">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-300">Total Reminders</CardTitle>
                  <Bell className="h-4 w-4 text-amber-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-white">{stats?.totalReminders || 0}</div>
                  <p className="text-xs text-gray-500">Active reminders</p>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-800">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-300">Calendar Events</CardTitle>
                  <Calendar className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-white">{stats?.totalEvents || 0}</div>
                  <p className="text-xs text-gray-500">Upcoming events</p>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-800">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-300">Emails</CardTitle>
                  <Mail className="h-4 w-4 text-red-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-white">{stats?.totalEmails || 0}</div>
                  <p className="text-xs text-gray-500">Total emails</p>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-800">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-300">Account Type</CardTitle>
                  <Crown className="h-4 w-4 text-amber-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-white">{user.isPro ? 'Pro' : 'Free'}</div>
                  <p className="text-xs text-gray-500">Current plan</p>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <QuickActions />

            {/* Recent Activity */}
            <div className="grid gap-6 md:grid-cols-2">
              <Card className="bg-gray-900 border-gray-800">
                <CardHeader>
                  <CardTitle className="text-white">Recent Calendar Events</CardTitle>
                  <CardDescription className="text-gray-400">
                    Your upcoming calendar events
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium text-white">Team Meeting</p>
                        <p className="text-xs text-gray-500">Today at 2:00 PM</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium text-white">Project Review</p>
                        <p className="text-xs text-gray-500">Tomorrow at 10:00 AM</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="w-2 h-2 bg-amber-500 rounded-full" />
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium text-white">Client Call</p>
                        <p className="text-xs text-gray-500">Friday at 3:00 PM</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-800">
                <CardHeader>
                  <CardTitle className="text-white">Recent Emails</CardTitle>
                  <CardDescription className="text-gray-400">
                    Your latest email activity
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-2 h-2 bg-red-500 rounded-full" />
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium text-white">Important: Project Update</p>
                        <p className="text-xs text-gray-500">From: <EMAIL></p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium text-white">Meeting Confirmation</p>
                        <p className="text-xs text-gray-500">From: <EMAIL></p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="w-2 h-2 bg-gray-500 rounded-full" />
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium text-white">Weekly Newsletter</p>
                        <p className="text-xs text-gray-500">From: <EMAIL></p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="calendar">
            <CalendarView />
          </TabsContent>

          <TabsContent value="emails">
            <EmailView />
          </TabsContent>

          <TabsContent value="reminders">
            <RemindersView />
          </TabsContent>

          <TabsContent value="analytics">
            <AnalyticsView />
          </TabsContent>

          {user.isPro && (
            <TabsContent value="ai-features">
              <AIFeaturesView />
            </TabsContent>
          )}

          <TabsContent value="telegram">
            <TelegramView />
          </TabsContent>

          <TabsContent value="settings">
            <SettingsView />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
