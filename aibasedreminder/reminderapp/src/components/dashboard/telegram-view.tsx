"use client"

import { useState, useEffect } from "react"
import { MessageSquare, Send, Link, Unlink, TestTube, Copy, RefreshCw, ExternalLink, Clock, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { apiClient } from "@/lib/api-client"
import { toast } from "sonner"

export function TelegramView() {
  const [testMessage, setTestMessage] = useState("")
  const [isSending, setIsSending] = useState(false)
  const [isGeneratingCode, setIsGeneratingCode] = useState(false)
  const [linkCode, setLinkCode] = useState("")
  const [codeExpiresAt, setCodeExpiresAt] = useState<Date | null>(null)
  const [telegramStatus, setTelegramStatus] = useState<any>(null)
  const [isLoadingStatus, setIsLoadingStatus] = useState(true)

  const telegramConnected = telegramStatus?.isLinked || false
  const telegramAccounts = telegramStatus?.telegramAccounts || []

  // Load Telegram status on component mount
  useEffect(() => {
    loadTelegramStatus()
  }, [])

  const loadTelegramStatus = async () => {
    try {
      setIsLoadingStatus(true)
      const status = await apiClient.getTelegramStatus()
      setTelegramStatus(status)
    } catch (error) {
      console.error('Failed to load Telegram status:', error)
    } finally {
      setIsLoadingStatus(false)
    }
  }

  const generateLinkCode = async () => {
    setIsGeneratingCode(true)
    try {
      const result = await apiClient.generateTelegramLinkCode()
      setLinkCode(result.linkCode)
      setCodeExpiresAt(new Date(result.expiresAt))
      toast.success("Link code generated! Follow the instructions below.")
    } catch (error) {
      toast.error("Failed to generate link code")
      console.error('Generate link code error:', error)
    } finally {
      setIsGeneratingCode(false)
    }
  }

  const handleUnlinkTelegram = async () => {
    try {
      await apiClient.unlinkTelegramAccount()
      toast.success("Telegram bot unlinked successfully")
      await loadTelegramStatus() // Refresh status
      setLinkCode("") // Clear any existing link code
    } catch (error) {
      toast.error("Failed to unlink Telegram bot")
      console.error('Unlink Telegram error:', error)
    }
  }

  const copyLinkCode = () => {
    if (linkCode) {
      navigator.clipboard.writeText(linkCode)
      toast.success("Link code copied to clipboard!")
    }
  }

  const openTelegramBot = () => {
    window.open('https://t.me/ReminderAPPBot', '_blank')
  }

  const handleSendTestMessage = async () => {
    if (!testMessage.trim()) {
      toast.error("Please enter a test message")
      return
    }

    setIsSending(true)
    try {
      await apiClient.sendTelegramMessage({
        message: testMessage,
        parseMode: 'HTML'
      })
      toast.success("Test message sent successfully!")
      setTestMessage("")
    } catch (error) {
      toast.error("Failed to send test message")
      console.error('Send test message error:', error)
    } finally {
      setIsSending(false)
    }
  }

  const getTimeRemaining = () => {
    if (!codeExpiresAt) return null

    try {
      const now = new Date()
      const remaining = codeExpiresAt.getTime() - now.getTime()
      if (remaining <= 0) return "Expired"

      const minutes = Math.floor(remaining / 60000)
      const seconds = Math.floor((remaining % 60000) / 1000)

      // Ensure seconds is a valid number
      const secondsStr = isNaN(seconds) ? '00' : seconds.toString().padStart(2, '0')
      const minutesStr = isNaN(minutes) ? '0' : minutes.toString()

      return `${minutesStr}:${secondsStr}`
    } catch (error) {
      console.error('Error calculating time remaining:', error)
      return "Error"
    }
  }



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center">
            <MessageSquare className="w-6 h-6 mr-2 text-blue-400" />
            Telegram Integration
            {isLoadingStatus && <RefreshCw className="w-4 h-4 ml-2 animate-spin" />}
          </h2>
          <p className="text-gray-400">Connect your Telegram bot for instant notifications</p>
        </div>
        <Badge variant={telegramConnected ? "default" : "secondary"} className={telegramConnected ? "bg-green-600" : ""}>
          {telegramConnected ? 'Connected' : 'Not Connected'}
        </Badge>
      </div>

      {/* Connection Status */}
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Link className="w-5 h-5 mr-2 text-blue-400" />
            Bot Connection
          </CardTitle>
          <CardDescription className="text-gray-400">
            Connect your Telegram account to receive smart notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!telegramConnected ? (
            <div className="space-y-4 p-4 bg-gray-800 rounded-lg">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="w-5 h-5 text-blue-500" />
                  <h3 className="text-lg font-medium text-white">Connect Telegram Bot</h3>
                </div>
                <p className="text-gray-400">
                  Get instant notifications and interact with your reminders via Telegram
                </p>
                
                {!linkCode ? (
                  <Button
                    onClick={generateLinkCode}
                    disabled={isGeneratingCode}
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white"
                  >
                    {isGeneratingCode ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Generating Code...
                      </>
                    ) : (
                      <>
                        <Link className="w-4 h-4 mr-2" />
                        Generate Link Code
                      </>
                    )}
                  </Button>
                ) : (
                  <div className="space-y-3 p-3 bg-blue-900/20 border border-blue-800 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-blue-400 font-medium">Link Code:</span>
                      <div className="flex items-center space-x-2">
                        <code className="px-2 py-1 bg-gray-800 rounded text-amber-400 font-mono text-lg">
                          {linkCode}
                        </code>
                        <Button
                          onClick={copyLinkCode}
                          size="sm"
                          variant="outline"
                          className="border-gray-600"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    
                    {codeExpiresAt && (
                      <div className="flex items-center space-x-2 text-sm text-gray-400">
                        <Clock className="w-3 h-3" />
                        <span>Expires in: {getTimeRemaining()}</span>
                      </div>
                    )}
                    
                    <div className="space-y-2 text-sm text-gray-300">
                      <p className="font-medium">Follow these steps:</p>
                      <ol className="list-decimal list-inside space-y-1 text-gray-400">
                        <li>Open Telegram and search for <strong>@ReminderAPPBot</strong></li>
                        <li>Send <code className="bg-gray-800 px-1 rounded">/start</code> to the bot</li>
                        <li>Send <code className="bg-gray-800 px-1 rounded">/link {linkCode}</code></li>
                        <li>Your account will be linked automatically!</li>
                      </ol>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        onClick={openTelegramBot}
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <ExternalLink className="w-3 h-3 mr-1" />
                        Open Bot
                      </Button>
                      <Button
                        onClick={generateLinkCode}
                        size="sm"
                        variant="outline"
                        className="border-gray-600"
                      >
                        <RefreshCw className="w-3 h-3 mr-1" />
                        New Code
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-green-900/20 border border-green-800 rounded-lg">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-green-400 font-medium">Telegram Bot Connected</span>
                </div>
                <Button
                  onClick={handleUnlinkTelegram}
                  variant="outline"
                  size="sm"
                  className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                >
                  <Unlink className="w-4 h-4 mr-2" />
                  Disconnect
                </Button>
              </div>
              
              {telegramAccounts.length > 0 && (
                <div className="p-3 bg-gray-800 rounded-lg">
                  <h4 className="text-white font-medium mb-2">Connected Account:</h4>
                  <div className="text-sm text-gray-300">
                    <p><strong>Name:</strong> {telegramAccounts[0].firstName} {telegramAccounts[0].lastName}</p>
                    {telegramAccounts[0].username && (
                      <p><strong>Username:</strong> @{telegramAccounts[0].username}</p>
                    )}
                    <p><strong>Connected:</strong> {new Date(telegramAccounts[0].createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Message */}
      {telegramConnected && (
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <TestTube className="w-5 h-5 mr-2 text-amber-400" />
              Test Notifications
            </CardTitle>
            <CardDescription className="text-gray-400">
              Send a test message to verify your Telegram connection
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Textarea
                placeholder="Enter your test message..."
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-500"
                rows={3}
              />
            </div>
            <Button
              onClick={handleSendTestMessage}
              disabled={isSending || !testMessage.trim()}
              className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white"
            >
              {isSending ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Send Test Message
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
