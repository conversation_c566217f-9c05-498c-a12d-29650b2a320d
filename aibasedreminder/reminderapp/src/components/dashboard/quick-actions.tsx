"use client"

import { useState } from "react"
import { 
  Plus, 
  Calendar, 
  Mail, 
  Bell, 
  Zap, 
  MessageSquare,
  RefreshCw,
  BarChart3,
  Set<PERSON><PERSON>
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuthStore } from "@/lib/stores/auth-store"
import { useCalendarStore } from "@/lib/stores/calendar-store"
import { useEmailStore } from "@/lib/stores/email-store"
import { toast } from "sonner"

export function QuickActions() {
  const { user } = useAuthStore()
  const { syncCalendar, isSyncing: calendarSyncing } = useCalendarStore()
  const { syncEmails, isSyncing: emailSyncing } = useEmailStore()
  const [isCreatingReminder, setIsCreatingReminder] = useState(false)

  const handleSyncCalendar = async () => {
    try {
      await syncCalendar({ createReminders: true })
      toast.success("Calendar synced successfully!")
    } catch (error) {
      toast.error("Failed to sync calendar")
    }
  }

  const handleSyncEmails = async () => {
    try {
      await syncEmails({ createReminders: true })
      toast.success("Emails synced successfully!")
    } catch (error) {
      toast.error("Failed to sync emails")
    }
  }

  const handleCreateReminder = async () => {
    setIsCreatingReminder(true)
    try {
      // This would open a create reminder dialog
      toast.info("Create reminder dialog would open here")
    } catch (error) {
      toast.error("Failed to create reminder")
    } finally {
      setIsCreatingReminder(false)
    }
  }

  const handleSendTestNotification = async () => {
    try {
      toast.info("Test notification sent!")
    } catch (error) {
      toast.error("Failed to send test notification")
    }
  }

  return (
    <Card className="bg-gray-900 border-gray-800">
      <CardHeader>
        <CardTitle className="text-white">Quick Actions</CardTitle>
        <CardDescription className="text-gray-400">
          Frequently used actions and shortcuts
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Create Reminder */}
          <Button
            onClick={handleCreateReminder}
            disabled={isCreatingReminder}
            className="h-20 flex-col space-y-2 bg-gradient-to-br from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white"
          >
            <Plus className="w-5 h-5" />
            <span className="text-sm font-medium">Create Reminder</span>
          </Button>

          {/* Sync Calendar */}
          <Button
            onClick={handleSyncCalendar}
            disabled={calendarSyncing}
            variant="outline"
            className="h-20 flex-col space-y-2 border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800"
          >
            <div className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <RefreshCw className={`w-3 h-3 ${calendarSyncing ? 'animate-spin' : ''}`} />
            </div>
            <span className="text-sm font-medium">
              {calendarSyncing ? 'Syncing...' : 'Sync Calendar'}
            </span>
          </Button>

          {/* Sync Emails */}
          <Button
            onClick={handleSyncEmails}
            disabled={emailSyncing}
            variant="outline"
            className="h-20 flex-col space-y-2 border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800"
          >
            <div className="flex items-center space-x-1">
              <Mail className="w-4 h-4" />
              <RefreshCw className={`w-3 h-3 ${emailSyncing ? 'animate-spin' : ''}`} />
            </div>
            <span className="text-sm font-medium">
              {emailSyncing ? 'Syncing...' : 'Sync Emails'}
            </span>
          </Button>

          {/* Test Notification */}
          <Button
            onClick={handleSendTestNotification}
            variant="outline"
            className="h-20 flex-col space-y-2 border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800"
          >
            <MessageSquare className="w-5 h-5" />
            <span className="text-sm font-medium">Test Notification</span>
          </Button>
        </div>

        {/* Pro Features */}
        {user?.isPro && (
          <div className="mt-6 pt-6 border-t border-gray-800">
            <h4 className="text-sm font-medium text-amber-400 mb-4 flex items-center">
              <Zap className="w-4 h-4 mr-2" />
              AI-Powered Actions
            </h4>
            <div className="grid gap-4 md:grid-cols-3">
              <Button
                variant="outline"
                className="h-16 flex-col space-y-1 border-amber-600/20 text-amber-400 hover:text-amber-300 hover:bg-amber-600/10"
              >
                <Zap className="w-4 h-4" />
                <span className="text-xs">Smart Scheduling</span>
              </Button>

              <Button
                variant="outline"
                className="h-16 flex-col space-y-1 border-amber-600/20 text-amber-400 hover:text-amber-300 hover:bg-amber-600/10"
              >
                <Mail className="w-4 h-4" />
                <span className="text-xs">AI Email Reply</span>
              </Button>

              <Button
                variant="outline"
                className="h-16 flex-col space-y-1 border-amber-600/20 text-amber-400 hover:text-amber-300 hover:bg-amber-600/10"
              >
                <BarChart3 className="w-4 h-4" />
                <span className="text-xs">Content Analysis</span>
              </Button>
            </div>
          </div>
        )}

        {/* Free User Upgrade Prompt */}
        {!user?.isPro && (
          <div className="mt-6 pt-6 border-t border-gray-800">
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-amber-600/10 to-amber-700/10 border border-amber-600/20 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-br from-amber-600 to-amber-700">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-white">Unlock AI Features</h4>
                  <p className="text-xs text-gray-400">
                    Get smart scheduling, AI replies, and advanced analytics
                  </p>
                </div>
              </div>
              <Button
                size="sm"
                className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white"
              >
                Upgrade to Pro
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
