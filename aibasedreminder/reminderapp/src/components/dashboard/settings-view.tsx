"use client"

import { useState } from "react"
import { Settings, User, Calendar, Mail, Bell, Shield, Trash2, Crown, RefreshCw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useAuthStore } from "@/lib/stores/auth-store"
import { toast } from "sonner"

export function SettingsView() {
  const { user, userProfile, updateProfile, disconnectService } = useAuthStore()
  const [isLoading, setIsLoading] = useState(false)
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || ''
  })

  const handleUpdateProfile = async () => {
    setIsLoading(true)
    try {
      await updateProfile({ name: profileData.name })
      toast.success("Profile updated successfully!")
    } catch (error) {
      toast.error("Failed to update profile")
    } finally {
      setIsLoading(false)
    }
  }

  const handleDisconnectService = async (service: 'google' | 'telegram') => {
    try {
      await disconnectService(service)
      toast.success(`${service} disconnected successfully`)
    } catch (error) {
      toast.error(`Failed to disconnect ${service}`)
    }
  }

  const handleReauthenticateGoogle = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/auth/google/reauth')
      const data = await response.json()

      if (data.success) {
        window.open(data.authUrl, '_blank')
        toast.success("Re-authentication URL opened. Please complete the process in the new tab.")
      } else {
        toast.error("Failed to generate re-authentication URL")
      }
    } catch (error) {
      toast.error("Failed to initiate re-authentication")
    } finally {
      setIsLoading(false)
    }
  }

  const connections = userProfile?.connections

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center">
            <Settings className="w-6 h-6 mr-2 text-gray-400" />
            Settings
          </h2>
          <p className="text-gray-400">Manage your account and application preferences</p>
        </div>
        {user?.isPro && (
          <Badge className="bg-gradient-to-r from-amber-600 to-amber-700 text-white">
            <Crown className="w-3 h-3 mr-1" />
            Pro Account
          </Badge>
        )}
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 bg-gray-900 border border-gray-800">
          <TabsTrigger value="profile" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            <User className="w-4 h-4 mr-2" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="connections" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            <Shield className="w-4 h-4 mr-2" />
            Connections
          </TabsTrigger>
          <TabsTrigger value="notifications" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            <Bell className="w-4 h-4 mr-2" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="calendar" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            <Calendar className="w-4 h-4 mr-2" />
            Calendar
          </TabsTrigger>
          <TabsTrigger value="email" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            <Mail className="w-4 h-4 mr-2" />
            Email
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white">Profile Information</CardTitle>
              <CardDescription className="text-gray-400">
                Update your personal information and account details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Full Name</label>
                <Input
                  value={profileData.name}
                  onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                  className="bg-gray-800 border-gray-700 text-white"
                  placeholder="Enter your full name"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Email Address</label>
                <Input
                  value={profileData.email}
                  disabled
                  className="bg-gray-800 border-gray-700 text-gray-400"
                />
                <p className="text-xs text-gray-500">Email cannot be changed</p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Account Type</label>
                <div className="flex items-center space-x-2">
                  <Badge className={user?.isPro ? "bg-amber-600 text-white" : "bg-gray-600 text-white"}>
                    {user?.isPro ? 'Pro' : 'Free'}
                  </Badge>
                  {!user?.isPro && (
                    <Button size="sm" className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white">
                      Upgrade to Pro
                    </Button>
                  )}
                </div>
              </div>

              <Button
                onClick={handleUpdateProfile}
                disabled={isLoading}
                className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white"
              >
                {isLoading ? 'Updating...' : 'Update Profile'}
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-red-400">Danger Zone</CardTitle>
              <CardDescription className="text-gray-400">
                Irreversible and destructive actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border border-red-600/20 bg-red-600/10 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-white">Delete Account</h4>
                    <p className="text-xs text-gray-400">
                      Permanently delete your account and all associated data
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Account
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="connections" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white">Service Connections</CardTitle>
              <CardDescription className="text-gray-400">
                Manage your connected services and integrations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Google Services */}
              <div className="flex items-center justify-between p-4 border border-gray-700 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5 text-blue-500" />
                    <Mail className="w-5 h-5 text-red-500" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-white">Google Services</h4>
                    <p className="text-xs text-gray-400">Calendar & Gmail integration</p>
                    {connections?.google.connectedAt && (
                      <p className="text-xs text-gray-500">
                        Connected on {new Date(connections.google.connectedAt).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {connections?.google.connected ? (
                    <>
                      <Badge className="bg-green-600 text-white">Connected</Badge>
                      <Button
                        onClick={handleReauthenticateGoogle}
                        disabled={isLoading}
                        variant="outline"
                        size="sm"
                        className="border-amber-600 text-amber-400 hover:bg-amber-600 hover:text-white"
                      >
                        <RefreshCw className={`w-3 h-3 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                        Re-auth
                      </Button>
                      <Button
                        onClick={() => handleDisconnectService('google')}
                        variant="outline"
                        size="sm"
                        className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                      >
                        Disconnect
                      </Button>
                    </>
                  ) : (
                    <>
                      <Badge className="bg-gray-600 text-white">Not Connected</Badge>
                      <Button
                        onClick={handleReauthenticateGoogle}
                        disabled={isLoading}
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <RefreshCw className={`w-3 h-3 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                        Connect
                      </Button>
                    </>
                  )}
                </div>
              </div>

              {/* Telegram */}
              <div className="flex items-center justify-between p-4 border border-gray-700 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                    <span className="text-white font-bold text-sm">T</span>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-white">Telegram Bot</h4>
                    <p className="text-xs text-gray-400">Instant notifications and commands</p>
                    {connections?.telegram.connectedAt && (
                      <p className="text-xs text-gray-500">
                        Connected as @{connections.telegram.username} on{' '}
                        {new Date(connections.telegram.connectedAt).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {connections?.telegram.connected ? (
                    <>
                      <Badge className="bg-green-600 text-white">Connected</Badge>
                      <Button
                        onClick={() => handleDisconnectService('telegram')}
                        variant="outline"
                        size="sm"
                        className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                      >
                        Disconnect
                      </Button>
                    </>
                  ) : (
                    <>
                      <Badge className="bg-gray-600 text-white">Not Connected</Badge>
                      <Button
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        Connect
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white">Notification Preferences</CardTitle>
              <CardDescription className="text-gray-400">
                Configure how and when you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-white">Email Notifications</h4>
                    <p className="text-xs text-gray-400">Receive notifications via email</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <Separator className="bg-gray-800" />

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-white">Telegram Notifications</h4>
                    <p className="text-xs text-gray-400">Receive notifications via Telegram</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <Separator className="bg-gray-800" />

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-white">Daily Digest</h4>
                    <p className="text-xs text-gray-400">Morning calendar and evening email summary</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <Separator className="bg-gray-800" />

                <div className="space-y-2">
                  <label className="text-sm font-medium text-white">Quiet Hours</label>
                  <div className="grid grid-cols-2 gap-2">
                    <Select defaultValue="22:00">
                      <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                        <SelectValue placeholder="Start time" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-700">
                        <SelectItem value="20:00">8:00 PM</SelectItem>
                        <SelectItem value="21:00">9:00 PM</SelectItem>
                        <SelectItem value="22:00">10:00 PM</SelectItem>
                        <SelectItem value="23:00">11:00 PM</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select defaultValue="07:00">
                      <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                        <SelectValue placeholder="End time" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-700">
                        <SelectItem value="06:00">6:00 AM</SelectItem>
                        <SelectItem value="07:00">7:00 AM</SelectItem>
                        <SelectItem value="08:00">8:00 AM</SelectItem>
                        <SelectItem value="09:00">9:00 AM</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <p className="text-xs text-gray-500">No notifications during these hours</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calendar" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white">Calendar Settings</CardTitle>
              <CardDescription className="text-gray-400">
                Configure calendar sync and reminder preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-white">Auto Sync</h4>
                    <p className="text-xs text-gray-400">Automatically sync calendar events</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <Separator className="bg-gray-800" />

                <div className="space-y-2">
                  <label className="text-sm font-medium text-white">Sync Interval</label>
                  <Select defaultValue="15">
                    <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="5">Every 5 minutes</SelectItem>
                      <SelectItem value="15">Every 15 minutes</SelectItem>
                      <SelectItem value="30">Every 30 minutes</SelectItem>
                      <SelectItem value="60">Every hour</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator className="bg-gray-800" />

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-white">Create Reminders</h4>
                    <p className="text-xs text-gray-400">Automatically create reminders for events</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <Separator className="bg-gray-800" />

                <div className="space-y-2">
                  <label className="text-sm font-medium text-white">Default Reminder Time</label>
                  <Select defaultValue="15">
                    <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="5">5 minutes before</SelectItem>
                      <SelectItem value="15">15 minutes before</SelectItem>
                      <SelectItem value="30">30 minutes before</SelectItem>
                      <SelectItem value="60">1 hour before</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="email" className="space-y-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white">Email Settings</CardTitle>
              <CardDescription className="text-gray-400">
                Configure email sync and AI features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-white">Auto Sync</h4>
                    <p className="text-xs text-gray-400">Automatically sync Gmail messages</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <Separator className="bg-gray-800" />

                <div className="space-y-2">
                  <label className="text-sm font-medium text-white">Sync Interval</label>
                  <Select defaultValue="30">
                    <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="15">Every 15 minutes</SelectItem>
                      <SelectItem value="30">Every 30 minutes</SelectItem>
                      <SelectItem value="60">Every hour</SelectItem>
                      <SelectItem value="120">Every 2 hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator className="bg-gray-800" />

                {user?.isPro && (
                  <>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-white">AI Reply Suggestions</h4>
                        <p className="text-xs text-gray-400">Enable AI-powered email reply suggestions</p>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <Separator className="bg-gray-800" />

                    <div className="space-y-2">
                      <label className="text-sm font-medium text-white">Default AI Tone</label>
                      <Select defaultValue="professional">
                        <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-gray-800 border-gray-700">
                          <SelectItem value="professional">Professional</SelectItem>
                          <SelectItem value="casual">Casual</SelectItem>
                          <SelectItem value="friendly">Friendly</SelectItem>
                          <SelectItem value="formal">Formal</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <Separator className="bg-gray-800" />
                  </>
                )}

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-white">Important Email Reminders</h4>
                    <p className="text-xs text-gray-400">Create reminders for important emails</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
