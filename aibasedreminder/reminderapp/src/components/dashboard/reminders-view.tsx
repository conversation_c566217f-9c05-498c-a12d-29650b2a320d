"use client"

import { useState, useEffect } from "react"
import { Bell, Plus, Clock, CheckCircle, XCircle, Pause } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { format } from "date-fns"
import { toast } from "sonner"

// Mock data for now - will be replaced with actual API calls
const mockReminders = [
  {
    id: "1",
    title: "Team Meeting Reminder",
    description: "Don't forget about the weekly team meeting",
    type: "MEETING",
    status: "PENDING",
    scheduledFor: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
    createdAt: new Date().toISOString(),
    event: {
      title: "Weekly Team Meeting",
      startTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString()
    }
  },
  {
    id: "2",
    title: "Email Follow-up",
    description: "Follow up on the project proposal email",
    type: "EMAIL",
    status: "SENT",
    scheduledFor: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    sentAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    email: {
      subject: "Project Proposal - Follow Up Required",
      from: "<EMAIL>"
    }
  },
  {
    id: "3",
    title: "Custom Reminder",
    description: "Review quarterly reports",
    type: "CUSTOM",
    status: "COMPLETED",
    scheduledFor: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    sentAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
  }
]

export function RemindersView() {
  const [reminders, setReminders] = useState(mockReminders)
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [isLoading, setIsLoading] = useState(false)

  const handleCompleteReminder = async (reminderId: string) => {
    try {
      setReminders(prev => 
        prev.map(r => 
          r.id === reminderId 
            ? { ...r, status: "COMPLETED" as const }
            : r
        )
      )
      toast.success("Reminder marked as completed!")
    } catch (error) {
      toast.error("Failed to complete reminder")
    }
  }

  const handleSnoozeReminder = async (reminderId: string, minutes: number) => {
    try {
      const newScheduledTime = new Date(Date.now() + minutes * 60 * 1000)
      setReminders(prev => 
        prev.map(r => 
          r.id === reminderId 
            ? { 
                ...r, 
                status: "SNOOZED" as const,
                scheduledFor: newScheduledTime.toISOString()
              }
            : r
        )
      )
      toast.success(`Reminder snoozed for ${minutes} minutes`)
    } catch (error) {
      toast.error("Failed to snooze reminder")
    }
  }

  const handleCancelReminder = async (reminderId: string) => {
    try {
      setReminders(prev => 
        prev.map(r => 
          r.id === reminderId 
            ? { ...r, status: "CANCELLED" as const }
            : r
        )
      )
      toast.success("Reminder cancelled")
    } catch (error) {
      toast.error("Failed to cancel reminder")
    }
  }

  const getRemindersByStatus = () => {
    switch (selectedStatus) {
      case 'pending':
        return reminders.filter(r => r.status === 'PENDING')
      case 'sent':
        return reminders.filter(r => r.status === 'SENT')
      case 'completed':
        return reminders.filter(r => r.status === 'COMPLETED')
      case 'snoozed':
        return reminders.filter(r => r.status === 'SNOOZED')
      case 'cancelled':
        return reminders.filter(r => r.status === 'CANCELLED')
      default:
        return reminders
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge className="bg-amber-600 text-white">Pending</Badge>
      case 'SENT':
        return <Badge className="bg-blue-600 text-white">Sent</Badge>
      case 'COMPLETED':
        return <Badge className="bg-green-600 text-white">Completed</Badge>
      case 'SNOOZED':
        return <Badge className="bg-purple-600 text-white">Snoozed</Badge>
      case 'CANCELLED':
        return <Badge className="bg-red-600 text-white">Cancelled</Badge>
      case 'FAILED':
        return <Badge className="bg-red-700 text-white">Failed</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'MEETING':
        return '📅'
      case 'EMAIL':
        return '📧'
      case 'CUSTOM':
        return '⏰'
      default:
        return '🔔'
    }
  }

  const displayReminders = getRemindersByStatus()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Reminders</h2>
          <p className="text-gray-400">Manage your smart reminders and notifications</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Reminder
          </Button>
        </div>
      </div>

      {/* Reminder Status Tabs */}
      <Tabs value={selectedStatus} onValueChange={setSelectedStatus}>
        <TabsList className="grid w-full grid-cols-6 bg-gray-900 border border-gray-800">
          <TabsTrigger value="all" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            All ({reminders.length})
          </TabsTrigger>
          <TabsTrigger value="pending" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            Pending ({reminders.filter(r => r.status === 'PENDING').length})
          </TabsTrigger>
          <TabsTrigger value="sent" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
            Sent ({reminders.filter(r => r.status === 'SENT').length})
          </TabsTrigger>
          <TabsTrigger value="completed" className="data-[state=active]:bg-green-600 data-[state=active]:text-white">
            Completed ({reminders.filter(r => r.status === 'COMPLETED').length})
          </TabsTrigger>
          <TabsTrigger value="snoozed" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
            Snoozed ({reminders.filter(r => r.status === 'SNOOZED').length})
          </TabsTrigger>
          <TabsTrigger value="cancelled" className="data-[state=active]:bg-red-600 data-[state=active]:text-white">
            Cancelled ({reminders.filter(r => r.status === 'CANCELLED').length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={selectedStatus} className="space-y-4 mt-6">
          {/* Reminders List */}
          {displayReminders.length === 0 ? (
            <Card className="bg-gray-900 border-gray-800">
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Bell className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-white mb-2">No reminders found</h3>
                  <p className="text-gray-400 mb-4">
                    {selectedStatus === 'all' 
                      ? 'Create your first reminder to get started.' 
                      : `No ${selectedStatus} reminders found.`
                    }
                  </p>
                  <Button
                    className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create Reminder
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            displayReminders.map((reminder) => (
              <Card key={reminder.id} className="bg-gray-900 border-gray-800 hover:bg-gray-800/50 transition-colors">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{getTypeIcon(reminder.type)}</span>
                        <h3 className="text-lg font-medium text-white">{reminder.title}</h3>
                        {getStatusBadge(reminder.status)}
                      </div>
                      
                      {reminder.description && (
                        <p className="text-gray-400 text-sm">{reminder.description}</p>
                      )}
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>
                          🕐 Scheduled: {format(new Date(reminder.scheduledFor), 'MMM dd, yyyy h:mm a')}
                        </span>
                        {reminder.sentAt && (
                          <span>
                            ✅ Sent: {format(new Date(reminder.sentAt), 'MMM dd, yyyy h:mm a')}
                          </span>
                        )}
                      </div>
                      
                      {/* Related Event/Email Info */}
                      {reminder.event && (
                        <div className="flex items-center space-x-2 p-2 bg-gray-800 rounded-lg">
                          <span className="text-xs text-gray-400">Related Event:</span>
                          <span className="text-xs text-white">{reminder.event.title}</span>
                        </div>
                      )}
                      
                      {reminder.email && (
                        <div className="flex items-center space-x-2 p-2 bg-gray-800 rounded-lg">
                          <span className="text-xs text-gray-400">Related Email:</span>
                          <span className="text-xs text-white">{reminder.email.subject}</span>
                        </div>
                      )}
                    </div>
                    
                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2">
                      {reminder.status === 'PENDING' && (
                        <>
                          <Button
                            onClick={() => handleCompleteReminder(reminder.id)}
                            variant="ghost"
                            size="sm"
                            className="text-green-400 hover:text-green-300"
                            title="Mark as completed"
                          >
                            <CheckCircle className="w-4 h-4" />
                          </Button>
                          <Button
                            onClick={() => handleSnoozeReminder(reminder.id, 15)}
                            variant="ghost"
                            size="sm"
                            className="text-purple-400 hover:text-purple-300"
                            title="Snooze for 15 minutes"
                          >
                            <Pause className="w-4 h-4" />
                          </Button>
                          <Button
                            onClick={() => handleCancelReminder(reminder.id)}
                            variant="ghost"
                            size="sm"
                            className="text-red-400 hover:text-red-300"
                            title="Cancel reminder"
                          >
                            <XCircle className="w-4 h-4" />
                          </Button>
                        </>
                      )}
                      
                      {reminder.status === 'SENT' && (
                        <Button
                          onClick={() => handleCompleteReminder(reminder.id)}
                          variant="ghost"
                          size="sm"
                          className="text-green-400 hover:text-green-300"
                          title="Mark as completed"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </Button>
                      )}
                      
                      {reminder.status === 'SNOOZED' && (
                        <>
                          <Button
                            onClick={() => handleCompleteReminder(reminder.id)}
                            variant="ghost"
                            size="sm"
                            className="text-green-400 hover:text-green-300"
                            title="Mark as completed"
                          >
                            <CheckCircle className="w-4 h-4" />
                          </Button>
                          <Button
                            onClick={() => handleCancelReminder(reminder.id)}
                            variant="ghost"
                            size="sm"
                            className="text-red-400 hover:text-red-300"
                            title="Cancel reminder"
                          >
                            <XCircle className="w-4 h-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>

      {/* Stats */}
      {reminders.length > 0 && (
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white">Reminder Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{reminders.length}</div>
                <div className="text-sm text-gray-400">Total Reminders</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {reminders.filter(r => r.status === 'PENDING').length}
                </div>
                <div className="text-sm text-gray-400">Pending</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {reminders.filter(r => r.status === 'COMPLETED').length}
                </div>
                <div className="text-sm text-gray-400">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {Math.round((reminders.filter(r => r.status === 'COMPLETED').length / reminders.length) * 100) || 0}%
                </div>
                <div className="text-sm text-gray-400">Completion Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
