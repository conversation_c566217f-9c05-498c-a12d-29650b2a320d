"use client"

import { Calendar, Mail, MessageSquare, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import type { UserProfile } from "@/lib/types"

interface ConnectionStatusProps {
  connections?: UserProfile['connections']
}

export function ConnectionStatus({ connections }: ConnectionStatusProps) {
  if (!connections) {
    return (
      <Badge variant="outline" className="border-gray-700 text-gray-400">
        <AlertCircle className="w-3 h-3 mr-1" />
        Loading...
      </Badge>
    )
  }

  const googleConnected = connections.google.connected
  const telegramConnected = connections.telegram.connected
  const totalConnections = (googleConnected ? 1 : 0) + (telegramConnected ? 1 : 0)

  const getStatusColor = () => {
    if (totalConnections === 2) return "bg-green-600"
    if (totalConnections === 1) return "bg-amber-600"
    return "bg-red-600"
  }

  const getStatusText = () => {
    if (totalConnections === 2) return "All Connected"
    if (totalConnections === 1) return "Partially Connected"
    return "Not Connected"
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800">
          <div className={`w-2 h-2 rounded-full mr-2 ${getStatusColor()}`} />
          {getStatusText()}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 bg-black border-gray-800" align="end">
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-white">Service Connections</h4>
            <p className="text-sm text-gray-400">
              Manage your connected services and integrations
            </p>
          </div>

          <Separator className="bg-gray-800" />

          <div className="space-y-3">
            {/* Google Services */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-blue-500" />
                  <Mail className="w-4 h-4 text-red-500" />
                </div>
                <div>
                  <p className="text-sm font-medium text-white">Google Services</p>
                  <p className="text-xs text-gray-500">Calendar & Gmail</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {googleConnected ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-500" />
                )}
                <span className="text-xs text-gray-400">
                  {googleConnected ? 'Connected' : 'Not Connected'}
                </span>
              </div>
            </div>

            {googleConnected && connections.google.connectedAt && (
              <div className="ml-10 text-xs text-gray-500">
                Connected on {new Date(connections.google.connectedAt).toLocaleDateString()}
              </div>
            )}

            {/* Telegram */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <MessageSquare className="w-4 h-4 text-blue-400" />
                <div>
                  <p className="text-sm font-medium text-white">Telegram Bot</p>
                  <p className="text-xs text-gray-500">Notifications & Commands</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {telegramConnected ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-500" />
                )}
                <span className="text-xs text-gray-400">
                  {telegramConnected ? 'Connected' : 'Not Connected'}
                </span>
              </div>
            </div>

            {telegramConnected && connections.telegram.connectedAt && (
              <div className="ml-10 text-xs text-gray-500">
                Connected as @{connections.telegram.username || 'Unknown'} on{' '}
                {new Date(connections.telegram.connectedAt).toLocaleDateString()}
              </div>
            )}
          </div>

          <Separator className="bg-gray-800" />

          <div className="space-y-2">
            {!googleConnected && (
              <Button 
                size="sm" 
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                onClick={() => {
                  // Handle Google connection
                  console.log('Connect Google services')
                }}
              >
                <Calendar className="w-4 h-4 mr-2" />
                Connect Google Services
              </Button>
            )}

            {!telegramConnected && (
              <Button 
                size="sm" 
                variant="outline"
                className="w-full border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800"
                onClick={() => {
                  // Handle Telegram connection
                  console.log('Connect Telegram')
                }}
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Connect Telegram Bot
              </Button>
            )}

            {(googleConnected || telegramConnected) && (
              <p className="text-xs text-center text-gray-500">
                Go to Settings to manage connections
              </p>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
