"use client"

import { useState, useEffect } from "react"
import { Mail, Plus, RefreshCw, Search, Filter, MoreHorizontal, Star, Archive, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useEmailStore } from "@/lib/stores/email-store"
import { useAuthStore } from "@/lib/stores/auth-store"
import { format } from "date-fns"
import { toast } from "sonner"

export function EmailView() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const { user } = useAuthStore()
  const { 
    emails, 
    categories,
    isLoading, 
    isSyncing, 
    getEmails, 
    syncEmails,
    searchEmails,
    updateEmail,
    generateAIReply
  } = useEmailStore()

  useEffect(() => {
    // Load emails when component mounts
    getEmails()
  }, [getEmails])

  const handleSync = async () => {
    try {
      await syncEmails({ createReminders: true })
      toast.success("Emails synced successfully!")
    } catch (error) {
      toast.error("Failed to sync emails")
    }
  }

  const handleSearch = async () => {
    if (searchQuery.trim()) {
      try {
        await searchEmails(searchQuery)
      } catch (error) {
        toast.error("Failed to search emails")
      }
    } else {
      getEmails()
    }
  }

  const handleMarkAsRead = async (emailId: string, isRead: boolean) => {
    try {
      await updateEmail(emailId, { isRead })
      toast.success(`Email marked as ${isRead ? 'read' : 'unread'}`)
    } catch (error) {
      toast.error("Failed to update email")
    }
  }

  const handleMarkAsImportant = async (emailId: string, isImportant: boolean) => {
    try {
      await updateEmail(emailId, { isImportant })
      toast.success(`Email ${isImportant ? 'marked as important' : 'unmarked'}`)
    } catch (error) {
      toast.error("Failed to update email")
    }
  }

  const handleGenerateAIReply = async (emailId: string) => {
    if (!user?.isPro) {
      toast.error("AI features are only available for Pro users")
      return
    }

    try {
      const reply = await generateAIReply(emailId)
      toast.success("AI reply generated successfully!")
      // Here you would typically open a compose dialog with the generated reply
    } catch (error) {
      toast.error("Failed to generate AI reply")
    }
  }

  const getEmailsByCategory = () => {
    if (!categories) return emails

    switch (selectedCategory) {
      case 'urgent':
        return categories.urgent
      case 'important':
        return categories.important
      case 'normal':
        return categories.normal
      case 'low':
        return categories.lowPriority
      default:
        return emails
    }
  }

  const displayEmails = getEmailsByCategory()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Email Management</h2>
          <p className="text-gray-400">Manage your Gmail messages and AI-powered replies</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleSync}
            disabled={isSyncing}
            variant="outline"
            size="sm"
            className="border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
            {isSyncing ? 'Syncing...' : 'Sync'}
          </Button>
          <Button
            size="sm"
            className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Compose
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="bg-gray-900 border-gray-800">
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search emails..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10 bg-gray-800 border-gray-700 text-white placeholder-gray-500"
              />
            </div>
            <Button
              onClick={handleSearch}
              variant="outline"
              size="sm"
              className="border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800"
            >
              <Search className="w-4 h-4 mr-2" />
              Search
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Email Categories */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-5 bg-gray-900 border border-gray-800">
          <TabsTrigger value="all" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            All ({emails.length})
          </TabsTrigger>
          <TabsTrigger value="urgent" className="data-[state=active]:bg-red-600 data-[state=active]:text-white">
            Urgent ({categories?.urgent.length || 0})
          </TabsTrigger>
          <TabsTrigger value="important" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            Important ({categories?.important.length || 0})
          </TabsTrigger>
          <TabsTrigger value="normal" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
            Normal ({categories?.normal.length || 0})
          </TabsTrigger>
          <TabsTrigger value="low" className="data-[state=active]:bg-gray-600 data-[state=active]:text-white">
            Low Priority ({categories?.lowPriority.length || 0})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={selectedCategory} className="space-y-4 mt-6">
          {/* Emails List */}
          {isLoading ? (
            <Card className="bg-gray-900 border-gray-800">
              <CardContent className="pt-6">
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="w-6 h-6 animate-spin text-amber-600 mr-2" />
                  <span className="text-gray-400">Loading emails...</span>
                </div>
              </CardContent>
            </Card>
          ) : displayEmails.length === 0 ? (
            <Card className="bg-gray-900 border-gray-800">
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Mail className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-white mb-2">No emails found</h3>
                  <p className="text-gray-400 mb-4">
                    {searchQuery ? 'No emails match your search criteria.' : 'Connect your Gmail to see emails here.'}
                  </p>
                  <Button
                    onClick={() => searchQuery ? (setSearchQuery(''), getEmails()) : handleSync()}
                    className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white"
                  >
                    {searchQuery ? 'Clear Search' : 'Sync Emails'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            displayEmails.map((email) => (
              <Card key={email.id} className={`bg-gray-900 border-gray-800 hover:bg-gray-800/50 transition-colors ${!email.isRead ? 'border-l-4 border-l-amber-600' : ''}`}>
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center space-x-3">
                        <h3 className={`text-lg font-medium ${!email.isRead ? 'text-white font-semibold' : 'text-gray-300'}`}>
                          {email.subject}
                        </h3>
                        {!email.isRead && (
                          <Badge className="bg-blue-600 text-white text-xs">Unread</Badge>
                        )}
                        {email.isImportant && (
                          <Badge className="bg-amber-600 text-white text-xs">Important</Badge>
                        )}
                        {email.hasAttachments && (
                          <Badge variant="outline" className="border-gray-600 text-gray-400 text-xs">📎</Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>From: {email.from}</span>
                        <span>📅 {format(new Date(email.receivedAt), 'MMM dd, yyyy h:mm a')}</span>
                      </div>
                      
                      <p className="text-gray-400 text-sm line-clamp-2">
                        {email.body.substring(0, 200)}...
                      </p>
                      
                      {email.reminders && email.reminders.length > 0 && (
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="border-amber-600 text-amber-400 text-xs">
                            {email.reminders.length} reminder{email.reminders.length > 1 ? 's' : ''}
                          </Badge>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        onClick={() => handleMarkAsImportant(email.id, !email.isImportant)}
                        variant="ghost"
                        size="sm"
                        className={`${email.isImportant ? 'text-amber-400' : 'text-gray-400'} hover:text-amber-300`}
                      >
                        <Star className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        onClick={() => handleMarkAsRead(email.id, !email.isRead)}
                        variant="ghost"
                        size="sm"
                        className="text-gray-400 hover:text-white"
                      >
                        <Mail className="w-4 h-4" />
                      </Button>
                      
                      {user?.isPro && (
                        <Button
                          onClick={() => handleGenerateAIReply(email.id)}
                          variant="ghost"
                          size="sm"
                          className="text-amber-400 hover:text-amber-300"
                          title="Generate AI Reply (Pro)"
                        >
                          ✨
                        </Button>
                      )}
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-400 hover:text-white"
                      >
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>

      {/* Stats */}
      {emails.length > 0 && (
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white">Email Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{emails.length}</div>
                <div className="text-sm text-gray-400">Total Emails</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {emails.filter(e => !e.isRead).length}
                </div>
                <div className="text-sm text-gray-400">Unread</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {emails.filter(e => e.isImportant).length}
                </div>
                <div className="text-sm text-gray-400">Important</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {emails.filter(e => e.reminders && e.reminders.length > 0).length}
                </div>
                <div className="text-sm text-gray-400">With Reminders</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
