"use client"

import { useState, useEffect } from "react"
import { Calendar, Plus, RefreshC<PERSON>, Search, Filter, MoreHorizontal } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useCalendarStore } from "@/lib/stores/calendar-store"
import { format, isToday, isTomorrow, isThisWeek } from "date-fns"
import { toast } from "sonner"

export function CalendarView() {
  const [searchQuery, setSearchQuery] = useState("")
  const { 
    events, 
    isLoading, 
    isSyncing, 
    getEvents, 
    syncCalendar,
    searchEvents 
  } = useCalendarStore()

  useEffect(() => {
    // Load events when component mounts
    getEvents()
  }, [getEvents])

  const handleSync = async () => {
    try {
      await syncCalendar({ createReminders: true })
      toast.success("Calendar synced successfully!")
    } catch (error) {
      toast.error("Failed to sync calendar")
    }
  }

  const handleSearch = async () => {
    if (searchQuery.trim()) {
      try {
        await searchEvents(searchQuery)
      } catch (error) {
        toast.error("Failed to search events")
      }
    } else {
      getEvents()
    }
  }

  const getEventBadgeColor = (event: any) => {
    const eventDate = new Date(event.startTime)
    if (isToday(eventDate)) return "bg-red-600"
    if (isTomorrow(eventDate)) return "bg-amber-600"
    if (isThisWeek(eventDate)) return "bg-blue-600"
    return "bg-gray-600"
  }

  const getEventBadgeText = (event: any) => {
    const eventDate = new Date(event.startTime)
    if (isToday(eventDate)) return "Today"
    if (isTomorrow(eventDate)) return "Tomorrow"
    if (isThisWeek(eventDate)) return "This Week"
    return "Later"
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Calendar Events</h2>
          <p className="text-gray-400">Manage your Google Calendar events and reminders</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleSync}
            disabled={isSyncing}
            variant="outline"
            size="sm"
            className="border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
            {isSyncing ? 'Syncing...' : 'Sync'}
          </Button>
          <Button
            size="sm"
            className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Event
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="bg-gray-900 border-gray-800">
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10 bg-gray-800 border-gray-700 text-white placeholder-gray-500"
              />
            </div>
            <Button
              onClick={handleSearch}
              variant="outline"
              size="sm"
              className="border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800"
            >
              <Search className="w-4 h-4 mr-2" />
              Search
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Events List */}
      <div className="space-y-4">
        {isLoading ? (
          <Card className="bg-gray-900 border-gray-800">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="w-6 h-6 animate-spin text-amber-600 mr-2" />
                <span className="text-gray-400">Loading events...</span>
              </div>
            </CardContent>
          </Card>
        ) : events.length === 0 ? (
          <Card className="bg-gray-900 border-gray-800">
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No events found</h3>
                <p className="text-gray-400 mb-4">
                  {searchQuery ? 'No events match your search criteria.' : 'Connect your Google Calendar to see events here.'}
                </p>
                <Button
                  onClick={() => searchQuery ? (setSearchQuery(''), getEvents()) : handleSync()}
                  className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white"
                >
                  {searchQuery ? 'Clear Search' : 'Sync Calendar'}
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          events.map((event) => (
            <Card key={event.id} className="bg-gray-900 border-gray-800 hover:bg-gray-800/50 transition-colors">
              <CardContent className="pt-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium text-white">{event.title}</h3>
                      <Badge className={`${getEventBadgeColor(event)} text-white text-xs`}>
                        {getEventBadgeText(event)}
                      </Badge>
                    </div>
                    
                    {event.description && (
                      <p className="text-gray-400 text-sm">{event.description}</p>
                    )}
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>
                        📅 {format(new Date(event.startTime), 'MMM dd, yyyy')}
                      </span>
                      <span>
                        🕐 {format(new Date(event.startTime), 'h:mm a')} - {format(new Date(event.endTime), 'h:mm a')}
                      </span>
                      {event.location && (
                        <span>📍 {event.location}</span>
                      )}
                    </div>
                    
                    {event.attendees && event.attendees.length > 0 && (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">👥 {event.attendees.length} attendees</span>
                      </div>
                    )}
                    
                    {event.reminders && event.reminders.length > 0 && (
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="border-amber-600 text-amber-400 text-xs">
                          {event.reminders.length} reminder{event.reminders.length > 1 ? 's' : ''}
                        </Badge>
                      </div>
                    )}
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-400 hover:text-white"
                  >
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Stats */}
      {events.length > 0 && (
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white">Calendar Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{events.length}</div>
                <div className="text-sm text-gray-400">Total Events</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {events.filter(e => isToday(new Date(e.startTime))).length}
                </div>
                <div className="text-sm text-gray-400">Today</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {events.filter(e => isThisWeek(new Date(e.startTime))).length}
                </div>
                <div className="text-sm text-gray-400">This Week</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {events.filter(e => e.reminders && e.reminders.length > 0).length}
                </div>
                <div className="text-sm text-gray-400">With Reminders</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
