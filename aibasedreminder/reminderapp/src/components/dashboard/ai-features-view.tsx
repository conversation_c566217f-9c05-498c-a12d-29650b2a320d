"use client"

import { useState } from "react"
import { Zap, Mail, Calendar, Brain, Sparkles, MessageSquare, BarChart3 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useAuthStore } from "@/lib/stores/auth-store"
import { toast } from "sonner"

export function AIFeaturesView() {
  const { user } = useAuthStore()
  const [isGenerating, setIsGenerating] = useState(false)
  const [selectedTone, setSelectedTone] = useState("professional")
  const [selectedLength, setSelectedLength] = useState("medium")
  const [customInstructions, setCustomInstructions] = useState("")

  if (!user?.isPro) {
    return (
      <div className="space-y-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <div className="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-amber-600 to-amber-700 mx-auto mb-6">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">AI Features - Pro Only</h3>
              <p className="text-gray-400 mb-6 max-w-md mx-auto">
                Unlock powerful AI capabilities including smart email replies, content analysis, 
                and intelligent scheduling with a Pro subscription.
              </p>
              <Button className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white">
                Upgrade to Pro
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const handleGenerateEmailReply = async () => {
    setIsGenerating(true)
    try {
      // Mock AI reply generation
      await new Promise(resolve => setTimeout(resolve, 2000))
      toast.success("AI email reply generated successfully!")
    } catch (error) {
      toast.error("Failed to generate AI reply")
    } finally {
      setIsGenerating(false)
    }
  }

  const handleAnalyzeContent = async () => {
    setIsGenerating(true)
    try {
      // Mock content analysis
      await new Promise(resolve => setTimeout(resolve, 1500))
      toast.success("Content analysis completed!")
    } catch (error) {
      toast.error("Failed to analyze content")
    } finally {
      setIsGenerating(false)
    }
  }

  const handleSmartScheduling = async () => {
    setIsGenerating(true)
    try {
      // Mock smart scheduling
      await new Promise(resolve => setTimeout(resolve, 2000))
      toast.success("Smart scheduling suggestions generated!")
    } catch (error) {
      toast.error("Failed to generate scheduling suggestions")
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center">
            <Zap className="w-6 h-6 mr-2 text-amber-600" />
            AI Features
          </h2>
          <p className="text-gray-400">Powerful AI-driven productivity tools</p>
        </div>
        <Badge className="bg-gradient-to-r from-amber-600 to-amber-700 text-white">
          Pro Feature
        </Badge>
      </div>

      {/* AI Features Tabs */}
      <Tabs defaultValue="email-replies" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-gray-900 border border-gray-800">
          <TabsTrigger value="email-replies" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            <Mail className="w-4 h-4 mr-2" />
            Email Replies
          </TabsTrigger>
          <TabsTrigger value="content-analysis" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            <Brain className="w-4 h-4 mr-2" />
            Content Analysis
          </TabsTrigger>
          <TabsTrigger value="smart-scheduling" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            <Calendar className="w-4 h-4 mr-2" />
            Smart Scheduling
          </TabsTrigger>
          <TabsTrigger value="smart-reminders" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            <Sparkles className="w-4 h-4 mr-2" />
            Smart Reminders
          </TabsTrigger>
        </TabsList>

        <TabsContent value="email-replies" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Mail className="w-5 h-5 mr-2 text-blue-500" />
                  AI Email Reply Generator
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Generate contextual email replies with customizable tone and length
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Tone</label>
                  <Select value={selectedTone} onValueChange={setSelectedTone}>
                    <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="casual">Casual</SelectItem>
                      <SelectItem value="friendly">Friendly</SelectItem>
                      <SelectItem value="formal">Formal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Length</label>
                  <Select value={selectedLength} onValueChange={setSelectedLength}>
                    <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="short">Short</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="long">Long</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Custom Instructions</label>
                  <Textarea
                    placeholder="Add any specific instructions for the AI..."
                    value={customInstructions}
                    onChange={(e) => setCustomInstructions(e.target.value)}
                    className="bg-gray-800 border-gray-700 text-white placeholder-gray-500"
                    rows={3}
                  />
                </div>

                <Button
                  onClick={handleGenerateEmailReply}
                  disabled={isGenerating}
                  className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white"
                >
                  {isGenerating ? (
                    <>
                      <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                      Generating Reply...
                    </>
                  ) : (
                    <>
                      <Mail className="w-4 h-4 mr-2" />
                      Generate AI Reply
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Recent AI Replies</CardTitle>
                <CardDescription className="text-gray-400">
                  Your recently generated email replies
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white">Project Update Response</span>
                      <Badge className="bg-blue-600 text-white text-xs">Professional</Badge>
                    </div>
                    <p className="text-xs text-gray-400">
                      Thank you for the project update. I've reviewed the progress and have a few questions...
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-500">2 hours ago</span>
                      <span className="text-xs text-green-400">95% confidence</span>
                    </div>
                  </div>

                  <div className="p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white">Meeting Confirmation</span>
                      <Badge className="bg-green-600 text-white text-xs">Friendly</Badge>
                    </div>
                    <p className="text-xs text-gray-400">
                      Hi! Thanks for scheduling the meeting. I'm looking forward to discussing...
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-500">1 day ago</span>
                      <span className="text-xs text-green-400">92% confidence</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content-analysis" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Brain className="w-5 h-5 mr-2 text-purple-500" />
                  AI Content Analysis
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Analyze emails and calendar events for insights and action items
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <Button
                    onClick={handleAnalyzeContent}
                    disabled={isGenerating}
                    className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white"
                  >
                    {isGenerating ? (
                      <>
                        <Brain className="w-4 h-4 mr-2 animate-pulse" />
                        Analyzing Content...
                      </>
                    ) : (
                      <>
                        <Brain className="w-4 h-4 mr-2" />
                        Analyze Selected Content
                      </>
                    )}
                  </Button>

                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800">
                      <Mail className="w-4 h-4 mr-2" />
                      Analyze Emails
                    </Button>
                    <Button variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:text-white hover:bg-gray-800">
                      <Calendar className="w-4 h-4 mr-2" />
                      Analyze Events
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Analysis Results</CardTitle>
                <CardDescription className="text-gray-400">
                  Latest content analysis insights
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white">Project Email Analysis</span>
                      <Badge className="bg-red-600 text-white text-xs">High Urgency</Badge>
                    </div>
                    <p className="text-xs text-gray-400 mb-2">
                      Detected 3 action items and 1 deadline. Sentiment: Neutral
                    </p>
                    <div className="text-xs text-gray-500">
                      • Review project timeline
                      • Schedule follow-up meeting
                      • Update stakeholders
                    </div>
                  </div>

                  <div className="p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white">Meeting Analysis</span>
                      <Badge className="bg-amber-600 text-white text-xs">Medium Priority</Badge>
                    </div>
                    <p className="text-xs text-gray-400 mb-2">
                      Preparation required. 5 attendees. Duration: 1 hour
                    </p>
                    <div className="text-xs text-gray-500">
                      • Prepare presentation slides
                      • Review agenda items
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="smart-scheduling" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Calendar className="w-5 h-5 mr-2 text-green-500" />
                  Intelligent Scheduling
                </CardTitle>
                <CardDescription className="text-gray-400">
                  AI-powered optimal time slot suggestions based on your patterns
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={handleSmartScheduling}
                  disabled={isGenerating}
                  className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white"
                >
                  {isGenerating ? (
                    <>
                      <Calendar className="w-4 h-4 mr-2 animate-pulse" />
                      Finding Optimal Times...
                    </>
                  ) : (
                    <>
                      <Calendar className="w-4 h-4 mr-2" />
                      Generate Smart Schedule
                    </>
                  )}
                </Button>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-300">Scheduling Preferences</h4>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="p-2 bg-gray-800 rounded">
                      <span className="text-gray-400">Preferred Hours:</span>
                      <span className="text-white ml-1">9 AM - 5 PM</span>
                    </div>
                    <div className="p-2 bg-gray-800 rounded">
                      <span className="text-gray-400">Buffer Time:</span>
                      <span className="text-white ml-1">15 minutes</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Optimal Time Slots</CardTitle>
                <CardDescription className="text-gray-400">
                  AI-recommended meeting times
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white">Tomorrow 10:00 AM</span>
                      <Badge className="bg-green-600 text-white text-xs">95% optimal</Badge>
                    </div>
                    <p className="text-xs text-gray-400">
                      Perfect time based on your energy levels and calendar gaps
                    </p>
                  </div>

                  <div className="p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white">Friday 2:00 PM</span>
                      <Badge className="bg-amber-600 text-white text-xs">87% optimal</Badge>
                    </div>
                    <p className="text-xs text-gray-400">
                      Good alternative with minimal conflicts
                    </p>
                  </div>

                  <div className="p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white">Monday 3:30 PM</span>
                      <Badge className="bg-blue-600 text-white text-xs">82% optimal</Badge>
                    </div>
                    <p className="text-xs text-gray-400">
                      Available slot with adequate preparation time
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="smart-reminders" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Sparkles className="w-5 h-5 mr-2 text-amber-500" />
                  Smart Reminder Generation
                </CardTitle>
                <CardDescription className="text-gray-400">
                  AI-powered reminder suggestions with optimal timing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="p-3 bg-gray-800 rounded-lg">
                    <h4 className="text-sm font-medium text-white mb-2">AI Capabilities</h4>
                    <ul className="text-xs text-gray-400 space-y-1">
                      <li>• Context-aware timing suggestions</li>
                      <li>• Personalized message generation</li>
                      <li>• Historical pattern analysis</li>
                      <li>• Adaptive reminder frequency</li>
                    </ul>
                  </div>

                  <Button
                    className="w-full bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white"
                  >
                    <Sparkles className="w-4 h-4 mr-2" />
                    Generate Smart Reminders
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">AI Insights</CardTitle>
                <CardDescription className="text-gray-400">
                  Smart recommendations and patterns
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white">Optimal Reminder Time</span>
                      <Badge className="bg-amber-600 text-white text-xs">15 min before</Badge>
                    </div>
                    <p className="text-xs text-gray-400">
                      Based on your response patterns and effectiveness data
                    </p>
                  </div>

                  <div className="p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white">Best Notification Channel</span>
                      <Badge className="bg-blue-600 text-white text-xs">Telegram</Badge>
                    </div>
                    <p className="text-xs text-gray-400">
                      Highest engagement rate for your reminder notifications
                    </p>
                  </div>

                  <div className="p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white">Productivity Peak</span>
                      <Badge className="bg-green-600 text-white text-xs">10 AM - 12 PM</Badge>
                    </div>
                    <p className="text-xs text-gray-400">
                      Your most productive hours for important tasks
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
