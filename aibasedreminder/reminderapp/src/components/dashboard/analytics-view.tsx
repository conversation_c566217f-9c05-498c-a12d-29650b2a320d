"use client"

import { useState, useEffect } from "react"
import { BarChart3, TrendingUp, Calendar, Mail, Bell, Users } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"

export function AnalyticsView() {
  const [selectedPeriod, setSelectedPeriod] = useState("week")
  const [isLoading, setIsLoading] = useState(false)

  // Mock analytics data - will be replaced with actual API calls
  const mockAnalytics = {
    calendar: {
      totalEvents: 24,
      thisWeek: 8,
      averageDuration: 45,
      busyDays: 5,
      mostBusyDay: "Wednesday",
      mostBusyHour: 14
    },
    email: {
      totalEmails: 156,
      unreadEmails: 12,
      importantEmails: 8,
      responseRate: 85,
      averageResponseTime: 2.5,
      topSenders: [
        { sender: "<EMAIL>", count: 15 },
        { sender: "<EMAIL>", count: 12 },
        { sender: "<EMAIL>", count: 10 }
      ]
    },
    reminders: {
      totalReminders: 45,
      completedReminders: 38,
      completionRate: 84,
      averageSnoozeTime: 15,
      mostEffectiveTime: "15 minutes before"
    },
    productivity: {
      score: 87,
      trend: "+5%",
      focusTime: 6.5,
      meetingTime: 2.5,
      emailTime: 1.2
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Analytics Dashboard</h2>
          <p className="text-gray-400">Insights into your productivity and usage patterns</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32 bg-gray-900 border-gray-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-900 border-gray-700">
              <SelectItem value="day">Today</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Productivity Score</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{mockAnalytics.productivity.score}%</div>
            <p className="text-xs text-green-600">
              {mockAnalytics.productivity.trend} from last {selectedPeriod}
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Events This Week</CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{mockAnalytics.calendar.thisWeek}</div>
            <p className="text-xs text-gray-500">
              {mockAnalytics.calendar.totalEvents} total events
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Email Response Rate</CardTitle>
            <Mail className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{mockAnalytics.email.responseRate}%</div>
            <p className="text-xs text-gray-500">
              Avg {mockAnalytics.email.averageResponseTime}h response time
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-300">Reminder Completion</CardTitle>
            <Bell className="h-4 w-4 text-amber-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{mockAnalytics.reminders.completionRate}%</div>
            <p className="text-xs text-gray-500">
              {mockAnalytics.reminders.completedReminders}/{mockAnalytics.reminders.totalReminders} completed
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="calendar" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-gray-900 border border-gray-800">
          <TabsTrigger value="calendar" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            Calendar
          </TabsTrigger>
          <TabsTrigger value="email" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            Email
          </TabsTrigger>
          <TabsTrigger value="reminders" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            Reminders
          </TabsTrigger>
          <TabsTrigger value="productivity" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
            Productivity
          </TabsTrigger>
        </TabsList>

        <TabsContent value="calendar" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Calendar Insights</CardTitle>
                <CardDescription className="text-gray-400">
                  Your meeting and event patterns
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Total Events</span>
                  <Badge className="bg-blue-600 text-white">{mockAnalytics.calendar.totalEvents}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Average Duration</span>
                  <span className="text-white">{mockAnalytics.calendar.averageDuration} min</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Busiest Day</span>
                  <span className="text-white">{mockAnalytics.calendar.mostBusyDay}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Peak Hour</span>
                  <span className="text-white">{mockAnalytics.calendar.mostBusyHour}:00</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Weekly Distribution</CardTitle>
                <CardDescription className="text-gray-400">
                  Events by day of the week
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'].map((day, index) => (
                    <div key={day} className="flex items-center space-x-3">
                      <span className="text-sm text-gray-300 w-20">{day}</span>
                      <div className="flex-1 bg-gray-800 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-amber-600 to-amber-700 h-2 rounded-full"
                          style={{ width: `${Math.random() * 80 + 20}%` }}
                        />
                      </div>
                      <span className="text-sm text-white w-8">{Math.floor(Math.random() * 8) + 1}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="email" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Email Statistics</CardTitle>
                <CardDescription className="text-gray-400">
                  Your email activity and performance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Total Emails</span>
                  <Badge className="bg-red-600 text-white">{mockAnalytics.email.totalEmails}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Unread</span>
                  <span className="text-white">{mockAnalytics.email.unreadEmails}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Important</span>
                  <span className="text-white">{mockAnalytics.email.importantEmails}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Response Rate</span>
                  <span className="text-white">{mockAnalytics.email.responseRate}%</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Top Senders</CardTitle>
                <CardDescription className="text-gray-400">
                  Most frequent email contacts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {mockAnalytics.email.topSenders.map((sender, index) => (
                    <div key={sender.sender} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-amber-600 to-amber-700 flex items-center justify-center">
                          <span className="text-white text-xs font-medium">
                            {sender.sender.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <span className="text-gray-300 text-sm">{sender.sender}</span>
                      </div>
                      <Badge variant="outline" className="border-gray-600 text-gray-400">
                        {sender.count}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="reminders" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Reminder Performance</CardTitle>
                <CardDescription className="text-gray-400">
                  How effective are your reminders?
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Total Reminders</span>
                  <Badge className="bg-amber-600 text-white">{mockAnalytics.reminders.totalReminders}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Completed</span>
                  <span className="text-white">{mockAnalytics.reminders.completedReminders}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Completion Rate</span>
                  <span className="text-white">{mockAnalytics.reminders.completionRate}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Most Effective Timing</span>
                  <span className="text-white">{mockAnalytics.reminders.mostEffectiveTime}</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Reminder Types</CardTitle>
                <CardDescription className="text-gray-400">
                  Distribution by reminder type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span>📅</span>
                      <span className="text-gray-300">Meeting Reminders</span>
                    </div>
                    <span className="text-white">18</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span>📧</span>
                      <span className="text-gray-300">Email Reminders</span>
                    </div>
                    <span className="text-white">15</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span>⏰</span>
                      <span className="text-gray-300">Custom Reminders</span>
                    </div>
                    <span className="text-white">12</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="productivity" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Time Distribution</CardTitle>
                <CardDescription className="text-gray-400">
                  How you spend your time daily
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Focus Time</span>
                  <span className="text-white">{mockAnalytics.productivity.focusTime}h</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Meeting Time</span>
                  <span className="text-white">{mockAnalytics.productivity.meetingTime}h</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Email Time</span>
                  <span className="text-white">{mockAnalytics.productivity.emailTime}h</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Productivity Score</span>
                  <Badge className="bg-green-600 text-white">{mockAnalytics.productivity.score}%</Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">Productivity Trends</CardTitle>
                <CardDescription className="text-gray-400">
                  Your productivity over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">This Week</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-white">87%</span>
                      <TrendingUp className="w-4 h-4 text-green-500" />
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Last Week</span>
                    <span className="text-gray-400">82%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">This Month</span>
                    <span className="text-white">85%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Improvement</span>
                    <Badge className="bg-green-600 text-white">+5%</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
