"use client"

import { UserPlus, <PERSON>tings, MessageSquare, ArrowRight, CheckCircle } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { SnowAnimation } from "./snow-animation"

const steps = [
  {
    number: 1,
    icon: UserPlus,
    title: "Sign in & Connect Accounts",
    description:
      "Securely connect your Google Calendar, Outlook, Gmail, and other accounts with just a few clicks. No complex setup required.",
    gradient: "from-amber-600 to-amber-700",
  },
  {
    number: 2,
    icon: Settings,
    title: "Set Reminders Automatically",
    description:
      "Our AI analyzes your calendar and emails to create intelligent reminders. Customize notification timing and preferences to fit your workflow.",
    gradient: "from-amber-700 to-orange-600",
  },
  {
    number: 3,
    icon: MessageSquare,
    title: "Get Alerts in Telegram & Respond Instantly",
    description:
      "Receive smart notifications in Telegram with context and quick actions. Reply to emails, join meetings, or snooze reminders without leaving the chat.",
    gradient: "from-green-600 to-emerald-600",
  },
]

export function HowItWorks() {
  return (
    <section id="how-it-works" className="py-20 bg-gradient-to-b from-gray-900 to-black relative overflow-hidden">
      <SnowAnimation />

      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-1/2 left-1/4 w-64 h-64 bg-amber-600/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-amber-700/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-amber-600/20 to-amber-700/20 border border-amber-600/30 mb-6">
            <CheckCircle className="w-4 h-4 text-amber-500 mr-2" />
            <span className="text-sm font-medium text-amber-500">Simple Process</span>
          </div>

          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4">
            How It{" "}
            <span className="bg-gradient-to-r from-amber-500 to-amber-700 bg-clip-text text-transparent">Works</span>
          </h2>

          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Get started in minutes with our simple 3-step process. No technical knowledge required.
          </p>
        </div>

        {/* Desktop Horizontal Stepper */}
        <div className="hidden lg:block">
          <div className="relative">
            {/* Connection lines */}
            <div className="absolute top-16 left-0 right-0 flex items-center justify-center">
              <div className="flex items-center w-full max-w-4xl mx-auto px-32">
                <div className="flex-1 h-0.5 bg-gradient-to-r from-amber-600/30 to-amber-700/30"></div>
                <div className="flex-1 h-0.5 bg-gradient-to-r from-amber-700/30 to-green-600/30 ml-8"></div>
              </div>
            </div>

            {/* Steps */}
            <div className="grid grid-cols-3 gap-8 max-w-6xl mx-auto">
              {steps.map((step, index) => {
                const Icon = step.icon
                return (
                  <div key={index} className="text-center group">
                    {/* Step number and icon */}
                    <div className="relative mb-6">
                      <div
                        className={`inline-flex items-center justify-center w-32 h-32 rounded-3xl bg-gradient-to-br ${step.gradient} shadow-xl group-hover:shadow-2xl transition-all duration-300 group-hover:scale-105`}
                      >
                        <Icon className="w-12 h-12 text-white" />
                      </div>

                      {/* Step number badge */}
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-black rounded-full shadow-lg flex items-center justify-center border-2 border-gray-700">
                        <span className="text-sm font-bold text-amber-500">{step.number}</span>
                      </div>

                      {/* Glow effect */}
                      <div
                        className={`absolute inset-0 w-32 h-32 mx-auto rounded-3xl bg-gradient-to-br ${step.gradient} opacity-20 blur-2xl group-hover:opacity-30 transition-opacity duration-300`}
                      ></div>
                    </div>

                    {/* Content */}
                    <Card className="border border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 bg-gray-800/80 backdrop-blur-sm hover:border-amber-600/30">
                      <CardContent className="p-6">
                        <h3 className="text-xl font-semibold text-white mb-3">{step.title}</h3>
                        <p className="text-gray-300 leading-relaxed">{step.description}</p>
                      </CardContent>
                    </Card>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Mobile/Tablet Vertical Timeline */}
        <div className="lg:hidden space-y-8">
          {steps.map((step, index) => {
            const Icon = step.icon
            const isLast = index === steps.length - 1

            return (
              <div key={index} className="relative">
                {/* Vertical line */}
                {!isLast && (
                  <div className="absolute left-8 top-20 w-0.5 h-16 bg-gradient-to-b from-gray-600 to-gray-700"></div>
                )}

                <div className="flex items-start gap-6">
                  {/* Icon and number */}
                  <div className="relative flex-shrink-0">
                    <div
                      className={`flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${step.gradient} shadow-lg`}
                    >
                      <Icon className="w-8 h-8 text-white" />
                    </div>

                    {/* Step number badge */}
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-black rounded-full shadow-md flex items-center justify-center border border-gray-700">
                      <span className="text-xs font-bold text-amber-500">{step.number}</span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 pb-8">
                    <Card className="border border-gray-700 shadow-lg bg-gray-800/80 backdrop-blur-sm">
                      <CardContent className="p-6">
                        <h3 className="text-lg font-semibold text-white mb-2">{step.title}</h3>
                        <p className="text-gray-300 leading-relaxed text-sm">{step.description}</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-amber-600/20 to-amber-700/20 border border-amber-600/30">
            <span className="text-sm font-medium text-gray-300">Ready to get started?</span>
            <ArrowRight className="w-4 h-4 text-amber-500" />
          </div>
        </div>
      </div>
    </section>
  )
}
