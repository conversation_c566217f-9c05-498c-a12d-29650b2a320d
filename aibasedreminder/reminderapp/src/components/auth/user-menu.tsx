"use client"

import { useState } from "react"
import { 
  User, 
  Settings, 
  LogOut, 
  Crown, 
  Calendar, 
  Mail, 
  MessageSquare,
  BarChart3,
  Zap
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { useAuthStore } from "@/lib/stores/auth-store"
import { toast } from "sonner"

interface UserMenuProps {
  onOpenDashboard?: () => void
  onOpenSettings?: () => void
}

export function UserMenu({ onOpenDashboard, onOpenSettings }: UserMenuProps) {
  const { user, userProfile, logout, isLoading } = useAuthStore()
  const [isLoggingOut, setIsLoggingOut] = useState(false)

  if (!user) return null

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true)
      await logout()
      toast.success("Successfully logged out")
    } catch (error) {
      toast.error("Failed to logout")
    } finally {
      setIsLoggingOut(false)
    }
  }

  const getInitials = (name?: string, email?: string) => {
    if (name) {
      return name.split(' ').map(n => n[0]).join('').toUpperCase()
    }
    if (email) {
      return email.substring(0, 2).toUpperCase()
    }
    return 'U'
  }

  const connections = userProfile?.connections
  const stats = userProfile?.stats

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-10 w-10 rounded-full">
          <Avatar className="h-10 w-10 border-2 border-amber-600/20">
            <AvatarImage src={user.avatar} alt={user.name || user.email} />
            <AvatarFallback className="bg-gradient-to-br from-amber-600 to-amber-700 text-white font-medium">
              {getInitials(user.name, user.email)}
            </AvatarFallback>
          </Avatar>
          {user.isPro && (
            <div className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-gradient-to-br from-amber-500 to-amber-600 flex items-center justify-center">
              <Crown className="h-2.5 w-2.5 text-white" />
            </div>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80 bg-black border-gray-800" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium leading-none text-white">
                {user.name || 'User'}
              </p>
              {user.isPro && (
                <Badge className="bg-gradient-to-r from-amber-600 to-amber-700 text-white text-xs">
                  <Crown className="w-3 h-3 mr-1" />
                  Pro
                </Badge>
              )}
            </div>
            <p className="text-xs leading-none text-gray-400">
              {user.email}
            </p>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator className="bg-gray-800" />

        {/* Connection Status */}
        {connections && (
          <>
            <DropdownMenuLabel className="text-xs text-gray-500 uppercase tracking-wide">
              Connections
            </DropdownMenuLabel>
            <div className="px-2 py-1 space-y-1">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-3 h-3 text-blue-500" />
                  <span className="text-gray-300">Google Calendar</span>
                </div>
                <div className={`w-2 h-2 rounded-full ${connections.google.connected ? 'bg-green-500' : 'bg-gray-500'}`} />
              </div>
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2">
                  <Mail className="w-3 h-3 text-red-500" />
                  <span className="text-gray-300">Gmail</span>
                </div>
                <div className={`w-2 h-2 rounded-full ${connections.google.connected ? 'bg-green-500' : 'bg-gray-500'}`} />
              </div>
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="w-3 h-3 text-blue-400" />
                  <span className="text-gray-300">Telegram</span>
                </div>
                <div className={`w-2 h-2 rounded-full ${connections.telegram.connected ? 'bg-green-500' : 'bg-gray-500'}`} />
              </div>
            </div>
            <DropdownMenuSeparator className="bg-gray-800" />
          </>
        )}

        {/* Stats */}
        {stats && (
          <>
            <DropdownMenuLabel className="text-xs text-gray-500 uppercase tracking-wide">
              Quick Stats
            </DropdownMenuLabel>
            <div className="px-2 py-1 space-y-1">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-300">Reminders</span>
                <span className="text-white font-medium">{stats.totalReminders}</span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-300">Events</span>
                <span className="text-white font-medium">{stats.totalEvents}</span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-300">Emails</span>
                <span className="text-white font-medium">{stats.totalEmails}</span>
              </div>
            </div>
            <DropdownMenuSeparator className="bg-gray-800" />
          </>
        )}

        {/* Menu Items */}
        <DropdownMenuItem 
          onClick={onOpenDashboard}
          className="text-gray-300 hover:text-white hover:bg-gray-800 cursor-pointer"
        >
          <BarChart3 className="mr-2 h-4 w-4" />
          <span>Dashboard</span>
        </DropdownMenuItem>

        <DropdownMenuItem 
          onClick={onOpenSettings}
          className="text-gray-300 hover:text-white hover:bg-gray-800 cursor-pointer"
        >
          <Settings className="mr-2 h-4 w-4" />
          <span>Settings</span>
        </DropdownMenuItem>

        {!user.isPro && (
          <DropdownMenuItem className="text-amber-400 hover:text-amber-300 hover:bg-gray-800 cursor-pointer">
            <Crown className="mr-2 h-4 w-4" />
            <span>Upgrade to Pro</span>
          </DropdownMenuItem>
        )}

        <DropdownMenuSeparator className="bg-gray-800" />

        <DropdownMenuItem 
          onClick={handleLogout}
          disabled={isLoggingOut}
          className="text-red-400 hover:text-red-300 hover:bg-gray-800 cursor-pointer"
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>{isLoggingOut ? 'Signing out...' : 'Sign out'}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
