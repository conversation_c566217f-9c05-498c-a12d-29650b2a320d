"use client"

import { Calendar, Mail, ArrowRight, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SnowAnimation } from "./snow-animation"

export function CTASection() {
  return (
    <section className="py-20 bg-gradient-to-br from-black via-gray-900 to-black relative overflow-hidden">
      <SnowAnimation />

      {/* Background decorations */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-amber-600/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-amber-700/10 rounded-full blur-3xl"></div>

        {/* Floating elements */}
        <div className="absolute top-20 right-20 opacity-20 animate-bounce delay-300">
          <Calendar className="w-8 h-8 text-amber-600" />
        </div>
        <div className="absolute bottom-32 left-32 opacity-20 animate-bounce delay-700">
          <Mail className="w-6 h-6 text-amber-700" />
        </div>
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-4xl mx-auto">
          {/* Main headline */}
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
            Stay on Top of{" "}
            <span className="bg-gradient-to-r from-amber-500 to-amber-700 bg-clip-text text-transparent">
              What Matters
            </span>
          </h2>

          {/* Supporting text */}
          <p className="text-lg sm:text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
            Join thousands of professionals who never miss important meetings or emails. Get started in less than 2
            minutes.
          </p>

          {/* Benefits list */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-10 text-gray-300">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-sm font-medium">Free to start</span>
            </div>
            <div className="hidden sm:block w-1 h-1 bg-gray-600 rounded-full"></div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-sm font-medium">No credit card required</span>
            </div>
            <div className="hidden sm:block w-1 h-1 bg-gray-600 rounded-full"></div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-sm font-medium">Cancel anytime</span>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 text-lg group"
            >
              <Calendar className="w-5 h-5 mr-3" />
              Connect Calendar & Email Now
              <ArrowRight className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform duration-200" />
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="border-2 border-gray-600 text-gray-300 hover:border-amber-500 hover:text-amber-500 font-medium px-8 py-4 rounded-xl transition-all duration-300 hover:bg-amber-500/5 text-lg backdrop-blur-sm"
            >
              Watch 2-min Demo
            </Button>
          </div>

          {/* Trust indicator */}
          <div className="mt-8 text-gray-400 text-sm">
            <p>Trusted by 10,000+ professionals worldwide</p>
          </div>
        </div>
      </div>
    </section>
  )
}
