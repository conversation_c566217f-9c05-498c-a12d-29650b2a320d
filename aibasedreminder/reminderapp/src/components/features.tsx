"use client"

import { <PERSON>, <PERSON>, Send, <PERSON>, Zap, Timer } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { SnowAnimation } from "./snow-animation"

const features = [
  {
    icon: Clock,
    title: "Connect Your Calendar & Email",
    description:
      "Seamlessly sync with Google Calendar, Outlook, Gmail, and other popular platforms in just a few clicks.",
    gradient: "from-amber-600 to-amber-700",
  },
  {
    icon: Bell,
    title: "Get Smart Reminders in Telegram",
    description:
      "Receive intelligent notifications directly in Telegram with context and suggested actions for each reminder.",
    gradient: "from-amber-700 to-orange-600",
  },
  {
    icon: Send,
    title: "Reply to Emails Instantly",
    description: "Respond to important emails directly from your reminders without switching between multiple apps.",
    gradient: "from-green-600 to-emerald-600",
  },
  {
    icon: Lock,
    title: "Privacy First & Secure",
    description: "Your data is encrypted end-to-end. We never store your emails or calendar content on our servers.",
    gradient: "from-blue-600 to-cyan-600",
  },
]

export function Features() {
  return (
    <section id="features" className="py-20 bg-gray-900 relative overflow-hidden">
      <SnowAnimation />

      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/50 to-gray-900"></div>
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-full max-w-4xl h-px bg-gradient-to-r from-transparent via-gray-700 to-transparent"></div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-amber-600/20 to-amber-700/20 border border-amber-600/30 mb-6">
            <Zap className="w-4 h-4 text-amber-500 mr-2" />
            <span className="text-sm font-medium text-amber-500">Powerful Features</span>
          </div>

          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4">
            Everything You Need to{" "}
            <span className="bg-gradient-to-r from-amber-500 to-amber-700 bg-clip-text text-transparent">
              Stay Organized
            </span>
          </h2>

          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Our AI-powered platform integrates with your existing tools to provide intelligent reminders and seamless
            workflow management.
          </p>
        </div>

        {/* Features grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <Card
                key={index}
                className="group relative overflow-hidden border border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 bg-gray-800/80 backdrop-blur-sm hover:border-amber-600/30"
              >
                <CardContent className="p-6 text-center">
                  {/* Icon container */}
                  <div className="relative mb-6">
                    <div
                      className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.gradient} shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110`}
                    >
                      <Icon className="w-8 h-8 text-white" />
                    </div>

                    {/* Floating background effect */}
                    <div
                      className={`absolute inset-0 w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br ${feature.gradient} opacity-20 blur-xl group-hover:opacity-30 transition-opacity duration-300`}
                    ></div>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-amber-500 transition-colors duration-200">
                    {feature.title}
                  </h3>

                  <p className="text-gray-300 leading-relaxed text-sm group-hover:text-gray-200 transition-colors duration-200">
                    {feature.description}
                  </p>

                  {/* Hover gradient border effect */}
                  <div
                    className={`absolute inset-0 rounded-lg bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}
                  ></div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center gap-2 text-sm text-gray-400">
            <Timer className="w-4 h-4" />
            <span>Setup takes less than 2 minutes</span>
          </div>
        </div>
      </div>
    </section>
  )
}
