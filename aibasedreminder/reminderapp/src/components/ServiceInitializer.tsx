'use client'

import { useEffect, useState } from 'react'

export function ServiceInitializer() {
  const [initialized, setInitialized] = useState(true) // Disabled auto-initialization
  const [status, setStatus] = useState<string>('')

  useEffect(() => {
    // DISABLED: Auto-initialization removed to prevent interference with autonomous bot
    // The bot now runs completely autonomously without website interference
    console.log('🤖 Bot is running autonomously - no website initialization needed')
    setStatus('Bot running autonomously')

    // Hide status after 2 seconds
    setTimeout(() => {
      setStatus('')
    }, 2000)
  }, [])

  if (!status) return null

  return (
    <div className="fixed top-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg z-50">
      <div className="flex items-center space-x-2">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        <span className="text-sm">{status}</span>
      </div>
    </div>
  )
}
