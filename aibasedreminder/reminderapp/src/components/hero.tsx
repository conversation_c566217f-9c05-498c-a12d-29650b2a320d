"use client"

import { <PERSON>, <PERSON>, Zap, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SnowAnimation } from "./snow-animation"

export function Hero() {
  return (
    <section
      id="home"
      className="relative min-h-[90vh] flex items-center justify-center overflow-hidden bg-gradient-to-br from-black via-gray-900 to-black"
    >
      <SnowAnimation />

      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient orbs */}
        <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-amber-600/10 to-amber-700/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-amber-700/10 to-amber-600/10 rounded-full blur-3xl animate-pulse delay-1000"></div>

        {/* Floating icons */}
        <div className="absolute top-20 left-20 opacity-20 animate-bounce delay-300">
          <Calendar className="w-8 h-8 text-amber-600" />
        </div>
        <div className="absolute top-40 right-32 opacity-20 animate-bounce delay-700">
          <Mail className="w-6 h-6 text-amber-700" />
        </div>
        <div className="absolute bottom-32 left-32 opacity-20 animate-bounce delay-500">
          <Zap className="w-7 h-7 text-amber-600" />
        </div>
        <div className="absolute bottom-20 right-20 opacity-20 animate-bounce delay-1000">
          <Calendar className="w-5 h-5 text-amber-700" />
        </div>
      </div>

      {/* Main content */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-4xl mx-auto">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-amber-600/20 to-amber-700/20 border border-amber-600/30 mb-8 animate-fade-in">
            <Zap className="w-4 h-4 text-amber-500 mr-2" />
            <span className="text-sm font-medium text-amber-500">AI-Powered Smart Reminders</span>
          </div>

          {/* Main headline */}
          <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-6 leading-tight animate-fade-in-up">
            Never Miss a{" "}
            <span className="bg-gradient-to-r from-amber-500 via-amber-600 to-amber-700 bg-clip-text text-transparent">
              Meeting
            </span>{" "}
            or{" "}
            <span className="bg-gradient-to-r from-amber-700 via-amber-500 to-amber-600 bg-clip-text text-transparent">
              Email
            </span>{" "}
            Again
          </h1>

          {/* Supporting subheadline */}
          <p className="text-lg sm:text-xl lg:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed animate-fade-in-up delay-200">
            Connect your calendar and email accounts to receive intelligent, timely reminders. Get notified via Telegram
            or manage everything from our clean, distraction-free dashboard.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 animate-fade-in-up delay-400">
            <Button
              size="lg"
              className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 text-lg group"
            >
              <Calendar className="w-5 h-5 mr-3" />
              Get Started — Connect Your Calendar
              <ArrowRight className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform duration-200" />
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="border-2 border-gray-600 text-gray-300 hover:border-amber-500 hover:text-amber-500 font-medium px-8 py-4 rounded-xl transition-all duration-300 hover:bg-amber-500/5 text-lg"
            >
              Watch Demo
            </Button>
          </div>

          {/* Trust indicators */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-sm text-gray-400 animate-fade-in-up delay-600">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Free to start</span>
            </div>
            <div className="hidden sm:block w-1 h-1 bg-gray-600 rounded-full"></div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-amber-600 rounded-full animate-pulse delay-300"></div>
              <span>No credit card required</span>
            </div>
            <div className="hidden sm:block w-1 h-1 bg-gray-600 rounded-full"></div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-amber-700 rounded-full animate-pulse delay-600"></div>
              <span>Setup in 2 minutes</span>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom gradient fade */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-900 to-transparent"></div>
    </section>
  )
}
