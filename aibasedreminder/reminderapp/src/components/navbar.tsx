"use client"

import { useState } from "react"
import Link from "next/link"
import { Menu, Calendar, Zap } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger, SheetClose } from "@/components/ui/sheet"
import { AuthDialog } from "@/components/auth/auth-dialog"
import { UserMenu } from "@/components/auth/user-menu"
import { useAuthStore } from "@/lib/stores/auth-store"

interface NavbarProps {
  onOpenDashboard?: () => void
}

export function Navbar({ onOpenDashboard }: NavbarProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [authDialogOpen, setAuthDialogOpen] = useState(false)
  const { user, isAuthenticated } = useAuthStore()

  const navLinks = [
    { href: "#home", label: "Home" },
    { href: "#features", label: "Features" },
    { href: "#how-it-works", label: "How It Works" },
    { href: "#contact", label: "Contact" },
  ]

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
    }
  }

  return (
    <nav className="sticky top-0 z-50 w-full border-b border-gray-800 bg-black/95 backdrop-blur supports-[backdrop-filter]:bg-black/80">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-amber-600 to-amber-700 group-hover:from-amber-500 group-hover:to-amber-600 transition-all duration-200">
              <Zap className="w-4 h-4 text-black" />
            </div>
            <span className="text-xl font-bold text-white group-hover:text-amber-500 transition-all duration-200">
              ReminderAPP
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => (
              <button
                key={link.href}
                onClick={() => scrollToSection(link.href)}
                className="text-sm font-medium text-gray-300 hover:text-white transition-colors duration-200 relative group cursor-pointer"
              >
                {link.label}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-amber-600 to-amber-700 group-hover:w-full transition-all duration-200"></span>
              </button>
            ))}
          </div>

          {/* Desktop CTA Button / User Menu */}
          <div className="hidden md:flex">
            {isAuthenticated && user ? (
              <UserMenu onOpenDashboard={onOpenDashboard} />
            ) : (
              <Button
                onClick={() => setAuthDialogOpen(true)}
                className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white font-medium px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Calendar className="w-4 h-4 mr-2" />
                Get Started
              </Button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="p-2 text-gray-300 hover:text-white">
                  <Menu className="w-5 h-5" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] sm:w-[400px] bg-black border-gray-800">
                <div className="flex flex-col space-y-6 mt-6">
                  {/* Mobile Logo */}
                  <div className="flex items-center space-x-2 px-2">
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-amber-600 to-amber-700">
                      <Zap className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-xl font-bold text-white">ReminderAPP</span>
                  </div>

                  {/* Mobile Navigation Links */}
                  <div className="flex flex-col space-y-4">
                    {navLinks.map((link) => (
                      <SheetClose asChild key={link.href}>
                        <button
                          onClick={() => scrollToSection(link.href)}
                          className="text-lg font-medium text-gray-300 hover:text-white transition-colors duration-200 px-2 py-2 rounded-lg hover:bg-gray-800 text-left"
                        >
                          {link.label}
                        </button>
                      </SheetClose>
                    ))}
                  </div>

                  {/* Mobile CTA Button / User Menu */}
                  <div className="pt-4 border-t border-gray-800">
                    {isAuthenticated && user ? (
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 px-2">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-amber-600 to-amber-700 flex items-center justify-center">
                            <span className="text-white text-sm font-medium">
                              {user.name?.charAt(0) || user.email?.charAt(0) || 'U'}
                            </span>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-white">{user.name || 'User'}</p>
                            <p className="text-xs text-gray-400">{user.email}</p>
                          </div>
                        </div>
                        <SheetClose asChild>
                          <Button
                            onClick={onOpenDashboard}
                            className="w-full bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white font-medium py-3 rounded-lg transition-all duration-200 shadow-lg"
                          >
                            <Calendar className="w-4 h-4 mr-2" />
                            Open Dashboard
                          </Button>
                        </SheetClose>
                      </div>
                    ) : (
                      <SheetClose asChild>
                        <Button
                          onClick={() => setAuthDialogOpen(true)}
                          className="w-full bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white font-medium py-3 rounded-lg transition-all duration-200 shadow-lg"
                        >
                          <Calendar className="w-4 h-4 mr-2" />
                          Get Started
                        </Button>
                      </SheetClose>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>

      {/* Auth Dialog */}
      <AuthDialog open={authDialogOpen} onOpenChange={setAuthDialogOpen} />
    </nav>
  )
}
