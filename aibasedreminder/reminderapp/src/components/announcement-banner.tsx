"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>R<PERSON> } from "lucide-react"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { SnowAnimation } from "./snow-animation"

export function AnnouncementBanner() {
  const [isVisible, setIsVisible] = useState(true)

  if (!isVisible) return null

  return (
    <div className="py-3 bg-gradient-to-r from-gray-900 via-black to-gray-900 border-y border-gray-800 relative">
      <SnowAnimation />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <Alert className="border-0 bg-gray-800/60 backdrop-blur-sm shadow-sm">
          <Sparkles className="h-4 w-4 text-amber-500" />
          <AlertDescription className="flex items-center justify-between">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 flex-1">
              <span className="text-sm font-medium text-gray-200">
                <span className="font-semibold text-amber-500">New:</span> Check out our Pro plan for advanced AI
                features and unlimited reminders!
              </span>
              <Button
                variant="outline"
                size="sm"
                className="self-start sm:self-auto border-amber-500/30 text-amber-500 hover:bg-amber-500/10 hover:border-amber-500/50 text-xs px-3 py-1 w-fit"
              >
                Learn more
                <ArrowRight className="w-3 h-3 ml-1" />
              </Button>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="p-1 h-auto text-gray-400 hover:text-gray-200 ml-2 flex-shrink-0"
            >
              <X className="w-4 h-4" />
              <span className="sr-only">Dismiss</span>
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
