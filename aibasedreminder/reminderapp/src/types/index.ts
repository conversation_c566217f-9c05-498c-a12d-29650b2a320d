// User Types
export interface User {
  id: string
  email: string
  name?: string
  avatar?: string
  isPro: boolean
  createdAt: Date
  updatedAt: Date
}

// OAuth Token Types
export interface OAuthToken {
  id: string
  userId: string
  provider: 'google'
  accessToken: string
  refreshToken?: string
  expiresAt: Date
  scope: string[]
  createdAt: Date
  updatedAt: Date
}

// Calendar Event Types
export interface CalendarEvent {
  id: string
  userId: string
  googleEventId: string
  title: string
  description?: string
  startTime: Date
  endTime: Date
  location?: string
  attendees?: string[]
  reminderSent: boolean
  createdAt: Date
  updatedAt: Date
}

// Email Types
export interface Email {
  id: string
  userId: string
  gmailMessageId: string
  threadId: string
  subject: string
  from: string
  to: string[]
  cc?: string[]
  body: string
  isRead: boolean
  isImportant: boolean
  hasAttachments: boolean
  receivedAt: Date
  reminderSent: boolean
  createdAt: Date
  updatedAt: Date
}

// Reminder Types
export interface Reminder {
  id: string
  userId: string
  type: 'meeting' | 'email' | 'custom'
  title: string
  description?: string
  scheduledFor: Date
  status: 'pending' | 'sent' | 'snoozed' | 'completed' | 'cancelled'
  eventId?: string
  emailId?: string
  telegramMessageId?: string
  snoozeUntil?: Date
  createdAt: Date
  updatedAt: Date
}

// Telegram Types
export interface TelegramUser {
  id: string
  userId: string
  telegramId: string
  username?: string
  firstName?: string
  lastName?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Request Types
export interface AuthRequest {
  email: string
  password?: string
}

export interface ReminderRequest {
  type: 'meeting' | 'email' | 'custom'
  title: string
  description?: string
  scheduledFor: string
  eventId?: string
  emailId?: string
}

// Google API Types
export interface GoogleCalendarEvent {
  id: string
  summary: string
  description?: string
  start: {
    dateTime: string
    timeZone?: string
  }
  end: {
    dateTime: string
    timeZone?: string
  }
  location?: string
  attendees?: Array<{
    email: string
    displayName?: string
    responseStatus?: string
  }>
}

export interface GmailMessage {
  id: string
  threadId: string
  labelIds: string[]
  snippet: string
  payload: {
    headers: Array<{
      name: string
      value: string
    }>
    body?: {
      data?: string
    }
    parts?: Array<{
      mimeType: string
      body?: {
        data?: string
      }
    }>
  }
  internalDate: string
}

// Telegram Bot Types
export interface TelegramUpdate {
  update_id: number
  message?: {
    message_id: number
    from: {
      id: number
      is_bot: boolean
      first_name: string
      last_name?: string
      username?: string
    }
    chat: {
      id: number
      type: string
    }
    date: number
    text?: string
  }
  callback_query?: {
    id: string
    from: {
      id: number
      first_name: string
      last_name?: string
      username?: string
    }
    message?: {
      message_id: number
      chat: {
        id: number
      }
    }
    data?: string
  }
}
