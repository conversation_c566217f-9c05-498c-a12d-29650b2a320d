import { User } from '@/types'
import { generateToken, verifyToken, getUserFromToken } from './auth'

export interface SessionData {
  user: User
  token: string
  expiresAt: Date
  isValid: boolean
}

export class SessionManager {
  private static instance: SessionManager
  private sessions: Map<string, SessionData> = new Map()

  private constructor() {}

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager()
    }
    return SessionManager.instance
  }

  async createSession(user: User): Promise<string> {
    const token = generateToken(user)
    const payload = verifyToken(token)
    
    if (!payload) {
      throw new Error('Failed to create session')
    }

    const sessionData: SessionData = {
      user,
      token,
      expiresAt: new Date(payload.exp! * 1000),
      isValid: true
    }

    this.sessions.set(token, sessionData)
    
    this.cleanupExpiredSessions()
    
    return token
  }

  async getSession(token: string): Promise<SessionData | null> {
    const session = this.sessions.get(token)
    
    if (!session) {
      return null
    }

    if (session.expiresAt < new Date() || !session.isValid) {
      this.sessions.delete(token)
      return null
    }

    return session
  }

  async validateSession(token: string): Promise<User | null> {
    const session = await this.getSession(token)
    
    if (!session) {
      return await getUserFromToken(token)
    }

    return session.user
  }

  async invalidateSession(token: string): Promise<boolean> {
    const session = this.sessions.get(token)
    
    if (session) {
      session.isValid = false
      this.sessions.delete(token)
      return true
    }
    
    return false
  }

  async invalidateUserSessions(userId: string): Promise<number> {
    let count = 0
    
    for (const [token, session] of this.sessions.entries()) {
      if (session.user.id === userId) {
        session.isValid = false
        this.sessions.delete(token)
        count++
      }
    }
    
    return count
  }

  private cleanupExpiredSessions(): void {
    const now = new Date()
    
    for (const [token, session] of this.sessions.entries()) {
      if (session.expiresAt < now || !session.isValid) {
        this.sessions.delete(token)
      }
    }
  }

  getSessionStats(): {
    totalSessions: number
    activeSessions: number
    expiredSessions: number
  } {
    const now = new Date()
    let activeSessions = 0
    let expiredSessions = 0

    for (const session of this.sessions.values()) {
      if (session.isValid && session.expiresAt > now) {
        activeSessions++
      } else {
        expiredSessions++
      }
    }

    return {
      totalSessions: this.sessions.size,
      activeSessions,
      expiredSessions
    }
  }
}

export const sessionManager = SessionManager.getInstance()

export async function createUserSession(user: User): Promise<string> {
  return await sessionManager.createSession(user)
}

export async function validateUserSession(token: string): Promise<User | null> {
  return await sessionManager.validateSession(token)
}

export async function invalidateUserSession(token: string): Promise<boolean> {
  return await sessionManager.invalidateSession(token)
}

export async function logoutAllUserSessions(userId: string): Promise<number> {
  return await sessionManager.invalidateUserSessions(userId)
}
