import { create } from 'zustand'
import { apiClient } from '../api-client'
import type { Email, EmailCategories, EmailSyncStats, EmailAnalytics, EmailSettings, AIEmailReply } from '../types'

interface EmailState {
  emails: Email[]
  categories: EmailCategories | null
  selectedEmail: Email | null
  analytics: EmailAnalytics | null
  settings: EmailSettings | null
  syncStats: EmailSyncStats | null
  lastSyncTime: string | null
  aiReplies: AIEmailReply[]
  isLoading: boolean
  isSyncing: boolean
  error: string | null

  // Actions
  getEmails: (params?: { q?: string; sync?: boolean; includeRead?: boolean }) => Promise<void>
  getEmail: (emailId: string, fresh?: boolean) => Promise<Email>
  updateEmail: (emailId: string, data: { isRead?: boolean; isImportant?: boolean }) => Promise<void>
  deleteEmail: (emailId: string) => Promise<void>
  selectEmail: (email: Email | null) => void
  
  // Send/Reply
  sendEmail: (data: any) => Promise<void>
  sendReply: (data: any) => Promise<void>
  
  // Sync
  syncEmails: (options?: { fullSync?: boolean; createReminders?: boolean }) => Promise<void>
  getSyncStatus: () => Promise<void>
  
  // Analytics
  getAnalytics: (period?: string, includeReminders?: boolean) => Promise<void>
  
  // Settings
  getSettings: () => Promise<void>
  updateSettings: (settings: Partial<EmailSettings>) => Promise<void>
  resetSettings: () => Promise<void>
  
  // Search
  searchEmails: (query: string, filters?: any) => Promise<Email[]>
  
  // AI Features (Pro)
  generateAIReply: (emailId: string, options?: any) => Promise<AIEmailReply>
  getAIReplies: () => Promise<void>
  
  // Utility
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useEmailStore = create<EmailState>((set, get) => ({
  emails: [],
  categories: null,
  selectedEmail: null,
  analytics: null,
  settings: null,
  syncStats: null,
  lastSyncTime: null,
  aiReplies: [],
  isLoading: false,
  isSyncing: false,
  error: null,

  getEmails: async (params) => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.getEmails(params)
      set({ 
        emails: result.emails,
        categories: result.categories,
        isLoading: false 
      })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch emails', 
        isLoading: false 
      })
      throw error
    }
  },

  getEmail: async (emailId: string, fresh?: boolean) => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.getEmail(emailId, fresh)
      set({ isLoading: false })
      return result.email
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch email', 
        isLoading: false 
      })
      throw error
    }
  },

  updateEmail: async (emailId: string, data: { isRead?: boolean; isImportant?: boolean }) => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.updateEmail(emailId, data)
      set(state => ({ 
        emails: state.emails.map(e => e.id === emailId ? result.email : e),
        selectedEmail: state.selectedEmail?.id === emailId ? result.email : state.selectedEmail,
        isLoading: false 
      }))
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update email', 
        isLoading: false 
      })
      throw error
    }
  },

  deleteEmail: async (emailId: string) => {
    try {
      set({ isLoading: true, error: null })
      await apiClient.deleteEmail(emailId)
      set(state => ({ 
        emails: state.emails.filter(e => e.id !== emailId),
        selectedEmail: state.selectedEmail?.id === emailId ? null : state.selectedEmail,
        isLoading: false 
      }))
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to delete email', 
        isLoading: false 
      })
      throw error
    }
  },

  selectEmail: (email: Email | null) => {
    set({ selectedEmail: email })
  },

  sendEmail: async (data: any) => {
    try {
      set({ isLoading: true, error: null })
      await apiClient.sendEmail(data)
      set({ isLoading: false })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to send email', 
        isLoading: false 
      })
      throw error
    }
  },

  sendReply: async (data: any) => {
    try {
      set({ isLoading: true, error: null })
      await apiClient.sendEmailReply(data)
      set({ isLoading: false })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to send reply', 
        isLoading: false 
      })
      throw error
    }
  },

  syncEmails: async (options) => {
    try {
      set({ isSyncing: true, error: null })
      const result = await apiClient.syncEmails(options)
      set({ 
        syncStats: result.syncStats,
        lastSyncTime: new Date().toISOString(),
        isSyncing: false 
      })
      // Refresh emails after sync
      await get().getEmails()
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to sync emails', 
        isSyncing: false 
      })
      throw error
    }
  },

  getSyncStatus: async () => {
    try {
      const result = await apiClient.getEmailSyncStatus(true)
      set({ 
        lastSyncTime: result.lastSyncTime || null,
        syncStats: result.stats || null
      })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to get sync status' })
    }
  },

  getAnalytics: async (period, includeReminders) => {
    try {
      set({ isLoading: true, error: null })
      const analytics = await apiClient.getEmailAnalytics(period, includeReminders)
      set({ analytics, isLoading: false })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to get analytics', 
        isLoading: false 
      })
      throw error
    }
  },

  getSettings: async () => {
    try {
      const result = await apiClient.getEmailSettings()
      set({ settings: result.settings })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to get settings' })
      throw error
    }
  },

  updateSettings: async (settings: Partial<EmailSettings>) => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.updateEmailSettings(settings)
      set({ settings: result.settings, isLoading: false })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update settings', 
        isLoading: false 
      })
      throw error
    }
  },

  resetSettings: async () => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.resetEmailSettings()
      set({ settings: result.settings, isLoading: false })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to reset settings', 
        isLoading: false 
      })
      throw error
    }
  },

  searchEmails: async (query: string, filters?: any) => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.searchEmails({ q: query, ...filters })
      set({ isLoading: false })
      return result.items
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to search emails', 
        isLoading: false 
      })
      throw error
    }
  },

  generateAIReply: async (emailId: string, options?: any) => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.generateEmailReply({ emailId, ...options })
      set(state => ({ 
        aiReplies: [...state.aiReplies, result.suggestion],
        isLoading: false 
      }))
      return result.suggestion
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to generate AI reply', 
        isLoading: false 
      })
      throw error
    }
  },

  getAIReplies: async () => {
    try {
      const result = await apiClient.getAIEmailReplies()
      set({ aiReplies: result.suggestions })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to get AI replies' })
      throw error
    }
  },

  clearError: () => set({ error: null }),
  setLoading: (loading: boolean) => set({ isLoading: loading }),
}))
