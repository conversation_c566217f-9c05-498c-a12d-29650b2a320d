import { create } from 'zustand'
import { apiClient } from '../api-client'
import type { CalendarEvent, CalendarEventInput, CalendarSyncStats, CalendarAnalytics, CalendarSettings } from '../types'

interface CalendarState {
  events: CalendarEvent[]
  selectedEvent: CalendarEvent | null
  analytics: CalendarAnalytics | null
  settings: CalendarSettings | null
  syncStats: CalendarSyncStats | null
  lastSyncTime: string | null
  isLoading: boolean
  isSyncing: boolean
  error: string | null

  // Actions
  getEvents: (params?: { timeMin?: string; timeMax?: string; sync?: boolean }) => Promise<void>
  createEvent: (event: CalendarEventInput) => Promise<CalendarEvent>
  updateEvent: (eventId: string, event: Partial<CalendarEventInput>) => Promise<void>
  deleteEvent: (eventId: string) => Promise<void>
  selectEvent: (event: CalendarEvent | null) => void
  
  // Sync
  syncCalendar: (options?: { fullSync?: boolean; createReminders?: boolean }) => Promise<void>
  getSyncStatus: () => Promise<void>
  
  // Analytics
  getAnalytics: (period?: string, includeReminders?: boolean) => Promise<void>
  
  // Settings
  getSettings: () => Promise<void>
  updateSettings: (settings: Partial<CalendarSettings>) => Promise<void>
  resetSettings: () => Promise<void>
  
  // Search
  searchEvents: (query: string, filters?: any) => Promise<CalendarEvent[]>
  
  // Utility
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useCalendarStore = create<CalendarState>((set, get) => ({
  events: [],
  selectedEvent: null,
  analytics: null,
  settings: null,
  syncStats: null,
  lastSyncTime: null,
  isLoading: false,
  isSyncing: false,
  error: null,

  getEvents: async (params) => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.getCalendarEvents(params)
      set({ 
        events: result.events, 
        isLoading: false,
        ...(result.synced && { lastSyncTime: new Date().toISOString() })
      })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch events', 
        isLoading: false 
      })
      throw error
    }
  },

  createEvent: async (event: CalendarEventInput) => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.createCalendarEvent(event)
      set(state => ({ 
        events: [...state.events, result.event],
        isLoading: false 
      }))
      return result.event
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to create event', 
        isLoading: false 
      })
      throw error
    }
  },

  updateEvent: async (eventId: string, event: Partial<CalendarEventInput>) => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.updateCalendarEvent(eventId, event)
      set(state => ({ 
        events: state.events.map(e => e.id === eventId ? result.event : e),
        selectedEvent: state.selectedEvent?.id === eventId ? result.event : state.selectedEvent,
        isLoading: false 
      }))
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update event', 
        isLoading: false 
      })
      throw error
    }
  },

  deleteEvent: async (eventId: string) => {
    try {
      set({ isLoading: true, error: null })
      await apiClient.deleteCalendarEvent(eventId)
      set(state => ({ 
        events: state.events.filter(e => e.id !== eventId),
        selectedEvent: state.selectedEvent?.id === eventId ? null : state.selectedEvent,
        isLoading: false 
      }))
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to delete event', 
        isLoading: false 
      })
      throw error
    }
  },

  selectEvent: (event: CalendarEvent | null) => {
    set({ selectedEvent: event })
  },

  syncCalendar: async (options) => {
    try {
      set({ isSyncing: true, error: null })
      const result = await apiClient.syncCalendar(options)
      set({ 
        syncStats: result.syncStats,
        lastSyncTime: new Date().toISOString(),
        isSyncing: false 
      })
      // Refresh events after sync
      await get().getEvents()
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to sync calendar', 
        isSyncing: false 
      })
      throw error
    }
  },

  getSyncStatus: async () => {
    try {
      const result = await apiClient.getCalendarSyncStatus(true)
      set({ 
        lastSyncTime: result.lastSyncTime || null,
        syncStats: result.stats || null
      })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to get sync status' })
    }
  },

  getAnalytics: async (period, includeReminders) => {
    try {
      set({ isLoading: true, error: null })
      const analytics = await apiClient.getCalendarAnalytics(period, includeReminders)
      set({ analytics, isLoading: false })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to get analytics', 
        isLoading: false 
      })
      throw error
    }
  },

  getSettings: async () => {
    try {
      const result = await apiClient.getCalendarSettings()
      set({ settings: result.settings })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to get settings' })
      throw error
    }
  },

  updateSettings: async (settings: Partial<CalendarSettings>) => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.updateCalendarSettings(settings)
      set({ settings: result.settings, isLoading: false })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update settings', 
        isLoading: false 
      })
      throw error
    }
  },

  resetSettings: async () => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.resetCalendarSettings()
      set({ settings: result.settings, isLoading: false })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to reset settings', 
        isLoading: false 
      })
      throw error
    }
  },

  searchEvents: async (query: string, filters?: any) => {
    try {
      set({ isLoading: true, error: null })
      const result = await apiClient.searchCalendarEvents({ q: query, ...filters })
      set({ isLoading: false })
      return result.items
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to search events', 
        isLoading: false 
      })
      throw error
    }
  },

  clearError: () => set({ error: null }),
  setLoading: (loading: boolean) => set({ isLoading: loading }),
}))
