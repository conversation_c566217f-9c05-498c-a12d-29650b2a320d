import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { apiClient } from '../api-client'
import type { User, UserProfile } from '../types'

interface AuthState {
  user: User | null
  userProfile: UserProfile | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  
  // Actions
  login: (email: string, password?: string) => Promise<void>
  register: (email: string, name: string) => Promise<void>
  logout: () => Promise<void>
  getCurrentUser: () => Promise<void>
  updateProfile: (data: { name?: string; avatar?: string }) => Promise<void>
  refreshToken: () => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
  
  // Google OAuth
  getGoogleAuthUrl: () => Promise<string>
  handleGoogleCallback: (code: string) => Promise<void>
  
  // Connections
  getUserConnections: () => Promise<void>
  disconnectService: (provider: 'google' | 'telegram') => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      userProfile: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (email: string, password?: string) => {
        try {
          set({ isLoading: true, error: null })
          const result = await apiClient.login(email, password)
          set({ 
            user: result.user, 
            isAuthenticated: true, 
            isLoading: false 
          })
          // Get full user profile
          await get().getCurrentUser()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Login failed', 
            isLoading: false 
          })
          throw error
        }
      },

      register: async (email: string, name: string) => {
        try {
          set({ isLoading: true, error: null })
          const result = await apiClient.register(email, name)
          set({ 
            user: result.user, 
            isAuthenticated: true, 
            isLoading: false 
          })
          // Get full user profile
          await get().getCurrentUser()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Registration failed', 
            isLoading: false 
          })
          throw error
        }
      },

      logout: async () => {
        try {
          set({ isLoading: true })
          await apiClient.logout()
          set({ 
            user: null, 
            userProfile: null, 
            isAuthenticated: false, 
            isLoading: false,
            error: null
          })
        } catch (error) {
          // Even if logout fails on server, clear local state
          set({ 
            user: null, 
            userProfile: null, 
            isAuthenticated: false, 
            isLoading: false,
            error: null
          })
        }
      },

      getCurrentUser: async () => {
        try {
          set({ isLoading: true, error: null })
          const userProfile = await apiClient.getCurrentUser()
          set({
            user: userProfile,
            userProfile,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          console.log('getCurrentUser failed, clearing auth state:', error)
          set({
            error: error instanceof Error ? error.message : 'Failed to get user profile',
            isLoading: false,
            isAuthenticated: false,
            user: null,
            userProfile: null
          })
          throw error
        }
      },

      updateProfile: async (data: { name?: string; avatar?: string }) => {
        try {
          set({ isLoading: true, error: null })
          const updatedUser = await apiClient.updateProfile(data)
          set(state => ({ 
            user: updatedUser,
            userProfile: state.userProfile ? { ...state.userProfile, ...updatedUser } : null,
            isLoading: false 
          }))
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update profile', 
            isLoading: false 
          })
          throw error
        }
      },

      refreshToken: async () => {
        try {
          const result = await apiClient.refreshToken()
          set({ user: result.user, isAuthenticated: true })
        } catch (error) {
          set({ 
            user: null, 
            userProfile: null, 
            isAuthenticated: false,
            error: 'Session expired'
          })
          throw error
        }
      },

      getGoogleAuthUrl: async () => {
        try {
          set({ isLoading: true, error: null })
          const result = await apiClient.getGoogleAuthUrl()
          set({ isLoading: false })
          return result.authUrl
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to get Google auth URL', 
            isLoading: false 
          })
          throw error
        }
      },

      handleGoogleCallback: async (code: string) => {
        try {
          console.log('🔄 Auth store: Starting Google callback...')
          set({ isLoading: true, error: null })

          console.log('📡 Auth store: Calling API client...')
          const result = await apiClient.handleGoogleCallback(code)
          console.log('✅ Auth store: API response:', result)

          set({
            user: result.user,
            isAuthenticated: true,
            isLoading: false
          })

          console.log('👤 Auth store: Getting current user...')
          // Get full user profile
          await get().getCurrentUser()
          console.log('✅ Auth store: Authentication complete!')

          return result
        } catch (error) {
          console.error('❌ Auth store error:', error)
          set({
            error: error instanceof Error ? error.message : 'Google authentication failed',
            isLoading: false
          })
          throw error
        }
      },

      getUserConnections: async () => {
        try {
          const connections = await apiClient.getUserConnections()
          set(state => ({
            userProfile: state.userProfile ? { ...state.userProfile, connections } : null
          }))
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to get connections' })
          throw error
        }
      },

      disconnectService: async (provider: 'google' | 'telegram') => {
        try {
          set({ isLoading: true, error: null })
          await apiClient.disconnectService(provider)
          // Refresh user connections
          await get().getUserConnections()
          set({ isLoading: false })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : `Failed to disconnect ${provider}`, 
            isLoading: false 
          })
          throw error
        }
      },

      clearError: () => set({ error: null }),
      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        userProfile: state.userProfile,
        isAuthenticated: state.isAuthenticated
      }),
      onRehydrateStorage: () => (state) => {
        // When the store is rehydrated, validate the authentication
        if (state?.isAuthenticated && state?.user) {
          // Validate the user session asynchronously
          state.getCurrentUser().catch(() => {
            // If validation fails, clear the auth state
            console.log('Auth validation failed on rehydration, clearing state')
            state.logout()
          })
        }
      },
    }
  )
)
