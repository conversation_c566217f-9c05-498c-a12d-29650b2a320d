import TelegramBot from 'node-telegram-bot-api'
import { prisma } from './prisma'
import { telegramBotManager } from './telegram-bot-manager'

// Helper function to escape HTML entities for Telegram
function escapeHtml(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN
const WEBHOOK_URL = process.env.TELEGRAM_WEBHOOK_URL

export async function initializeTelegramBot(): Promise<TelegramBot> {
  if (!TELEGRAM_BOT_TOKEN) {
    throw new Error('TELEGRAM_BOT_TOKEN is not configured')
  }

  try {
    // Initialize bot using the manager
    const bot = await telegramBotManager.initialize()

    // Set up message handlers
    await telegramBotManager.setupMessageHandlers()

    // Start polling for local development
    const usePolling = !WEBHOOK_URL || WEBHOOK_URL.includes('localhost') || process.env.NODE_ENV === 'development'

    if (usePolling) {
      await telegramBotManager.startPolling()
      console.log('🤖 Telegram bot initialized with polling mode for local development')
    } else {
      console.log('🤖 Telegram bot initialized with webhook mode for production')
    }

    return bot
  } catch (error) {
    console.error('❌ Failed to initialize Telegram bot:', error)
    throw error
  }
}

export async function restartTelegramBot(): Promise<TelegramBot> {
  console.log('🔄 Restarting Telegram bot...')

  try {
    await telegramBotManager.stop()
    return await initializeTelegramBot()
  } catch (error) {
    console.error('❌ Error restarting Telegram bot:', error)
    throw error
  }
}

export function getTelegramBot(): TelegramBot | null {
  return telegramBotManager.getBot()
}

export async function getTelegramBotStatus() {
  return await telegramBotManager.getStatus()
}

export async function setWebhook(): Promise<boolean> {
  try {
    const telegramBot = await initializeTelegramBot()

    if (!WEBHOOK_URL) {
      console.warn('TELEGRAM_WEBHOOK_URL not configured, skipping webhook setup')
      return false
    }

    await telegramBot.setWebHook(WEBHOOK_URL)
    console.log('Telegram webhook set successfully')
    return true
  } catch (error) {
    console.error('Error setting Telegram webhook:', error)
    return false
  }
}



export async function sendMessage(chatId: string, message: string, options?: {
  replyMarkup?: any
  parseMode?: 'HTML' | 'Markdown'
}): Promise<string | null> {
  const maxRetries = 3
  let lastError: any = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`📤 Sending message to ${chatId} (attempt ${attempt}/${maxRetries})`)

      if (!TELEGRAM_BOT_TOKEN) {
        throw new Error('TELEGRAM_BOT_TOKEN is not configured')
      }

      // Use direct HTTP API call with better error handling
      const payload = {
        chat_id: chatId,
        text: message,
        parse_mode: options?.parseMode || 'HTML',
        ...(options?.replyMarkup && { reply_markup: options.replyMarkup })
      }

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 15000) // 15 second timeout

      const response = await fetch(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const result = await response.json()

      if (result.ok) {
        console.log(`✅ Message sent successfully to ${chatId}`)
        return result.result.message_id.toString()
      } else {
        throw new Error(`Telegram API error: ${result.description}`)
      }

    } catch (error: any) {
      lastError = error
      console.error(`❌ Attempt ${attempt} failed:`, error.message)

      if (attempt < maxRetries) {
        const delay = attempt * 2000 // Exponential backoff: 2s, 4s, 6s
        console.log(`⏳ Retrying in ${delay}ms...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }

  // All attempts failed
  console.error(`❌ Failed to send message to ${chatId} after ${maxRetries} attempts`)
  console.log(`📝 Would have sent to ${chatId}: ${message}`)
  return null
}


export async function sendReminderNotification(userId: string, reminder: {
  id: string
  title: string
  description?: string
  type: 'meeting' | 'email' | 'custom'
  scheduledFor: Date
}): Promise<boolean> {
  try {
    const telegramUser = await prisma.telegramUser.findFirst({
      where: { userId, isActive: true }
    })

    if (!telegramUser) {
      console.log(`No active Telegram user found for user ${userId}`)
      return false
    }

    const message = formatReminderMessage(reminder)
    const keyboard = createReminderKeyboard(reminder.id)

    const messageId = await sendMessage(telegramUser.telegramId, message, {
      replyMarkup: keyboard,
      parseMode: 'HTML'
    })

    if (messageId) {
      // Only update reminder if it's a real reminder ID (not email notification)
      if (!reminder.id.startsWith('email-')) {
        try {
          await prisma.reminder.update({
            where: { id: reminder.id },
            data: { telegramMessageId: messageId }
          })
        } catch (error) {
          console.log(`Could not update reminder ${reminder.id} - might be an email notification`)
        }
      }
      return true
    }

    return false
  } catch (error) {
    console.error('Error sending reminder notification:', error)
    return false
  }
}

// Enhanced function for email notifications with categorization
export async function sendEmailNotification(userId: string, email: {
  id: string
  subject: string
  from: string
  body: string
  isImportant: boolean
  receivedAt: Date
  labels?: string[]
}): Promise<boolean> {
  try {
    const telegramUser = await prisma.telegramUser.findFirst({
      where: { userId, isActive: true }
    })

    if (!telegramUser) {
      console.log(`No active Telegram user found for user ${userId}`)
      return false
    }

    // Determine email category and icon
    let categoryIcon = '📧'
    let categoryText = 'New Email'

    if (email.labels) {
      if (email.labels.includes('SPAM')) {
        categoryIcon = '🚫'
        categoryText = 'Spam Email'
      } else if (email.labels.includes('STARRED')) {
        categoryIcon = '⭐'
        categoryText = 'Starred Email'
      } else if (email.labels.includes('IMPORTANT') || email.isImportant) {
        categoryIcon = '🚨'
        categoryText = 'Important Email'
      } else if (email.labels.includes('SNOOZED')) {
        categoryIcon = '⏰'
        categoryText = 'Snoozed Email'
      } else if (email.labels.includes('DRAFT')) {
        categoryIcon = '📝'
        categoryText = 'Draft Email'
      } else if (email.labels.includes('SENT')) {
        categoryIcon = '📤'
        categoryText = 'Sent Email'
      }
    } else if (email.isImportant) {
      categoryIcon = '🚨'
      categoryText = 'Important Email'
    }

    const title = `${categoryIcon} ${categoryText}: ${escapeHtml(email.subject)}`
    const description = `From: ${escapeHtml(email.from)}\n\n${escapeHtml(email.body.substring(0, 200))}${email.body.length > 200 ? '...' : ''}`

    // Add labels information if available
    let labelsText = ''
    if (email.labels && email.labels.length > 0) {
      const displayLabels = email.labels.filter(label =>
        !['INBOX', 'UNREAD', 'CATEGORY_PERSONAL', 'CATEGORY_SOCIAL', 'CATEGORY_PROMOTIONS', 'CATEGORY_UPDATES', 'CATEGORY_FORUMS'].includes(label)
      )
      if (displayLabels.length > 0) {
        labelsText = `\n\n🏷️ Labels: ${escapeHtml(displayLabels.join(', '))}`
      }
    }

    const message = `<b>${title}</b>\n\n${description}${labelsText}\n\n<i>Received: ${escapeHtml(email.receivedAt.toLocaleString())}</i>`

    // Create action buttons for email
    const keyboard = {
      inline_keyboard: [
        [
          { text: '📧 Open Gmail', url: 'https://gmail.com' },
          { text: '✅ Mark as Read', callback_data: `mark_read_${email.id}` }
        ],
        [
          { text: '⭐ Star Email', callback_data: `star_email_${email.id}` },
          { text: '🔕 Stop Reminders', callback_data: `stop_reminders_${email.id}` }
        ]
      ]
    }

    const messageId = await sendMessage(telegramUser.telegramId, message, {
      parseMode: 'HTML',
      replyMarkup: keyboard
    })

    if (messageId) {
      console.log(`✅ Email notification sent successfully to Telegram: ${email.subject}`)
      return true
    }

    console.log(`❌ Failed to send email notification to Telegram: ${email.subject}`)
    return false
  } catch (error) {
    console.error('Error sending email notification:', error)
    return false
  }
}

function formatReminderMessage(reminder: {
  title: string
  description?: string
  type: 'meeting' | 'email' | 'custom'
  scheduledFor: Date
}): string {
  const typeEmoji = {
    meeting: '📅',
    email: '📧',
    custom: '⏰'
  }

  const emoji = typeEmoji[reminder.type]
  const timeStr = reminder.scheduledFor.toLocaleString()

  let message = `${emoji} <b>Reminder</b>\n\n`
  message += `<b>${escapeHtml(reminder.title)}</b>\n`

  if (reminder.description) {
    message += `${escapeHtml(reminder.description)}\n\n`
  }

  message += `⏰ Scheduled for: ${escapeHtml(timeStr)}`

  return message
}

function createReminderKeyboard(reminderId: string) {
  return {
    inline_keyboard: [
      [
        { text: '✅ Mark Complete', callback_data: `complete_${reminderId}` },
        { text: '⏰ Snooze 15min', callback_data: `snooze_15_${reminderId}` }
      ],
      [
        { text: '⏰ Snooze 1hr', callback_data: `snooze_60_${reminderId}` },
        { text: '❌ Dismiss', callback_data: `dismiss_${reminderId}` }
      ]
    ]
  }
}

export async function handleTelegramUpdate(update: any): Promise<void> {
  try {
    console.log('🔄 Processing Telegram update:', update.update_id)

    if (update.message) {
      console.log('💬 Processing message from:', update.message.from?.username || update.message.from?.first_name)
      await handleMessage(update.message)
    } else if (update.callback_query) {
      console.log('🔘 Processing callback query')
      await handleCallbackQuery(update.callback_query)
    } else {
      console.log('❓ Unknown update type:', Object.keys(update))
    }
  } catch (error) {
    console.error('❌ Error handling Telegram update:', error)
    throw error
  }
}

async function handleMessage(message: any): Promise<void> {
  const chatId = message.chat.id.toString()
  const text = message.text?.trim()
  const username = message.from?.username
  const firstName = message.from?.first_name
  const lastName = message.from?.last_name

  if (text === '/start') {
    await sendMessage(chatId,
      '🤖 Welcome to ReminderAPP Bot!\n\n' +
      '📱 To link your account:\n' +
      '1. Open ReminderAPP web app\n' +
      '2. Go to Settings → Telegram\n' +
      '3. Click "Connect Telegram Bot"\n' +
      '4. Use the generated link code with /link command\n\n' +
      '💡 Example: /link ABC123XY\n\n' +
      'Once connected, you\'ll receive smart reminders for meetings and emails!'
    )
  } else if (text === '/help') {
    await sendMessage(chatId,
      '🤖 ReminderAPP Bot Commands:\n\n' +
      '🚀 /start - Start the bot\n' +
      '🔗 /link <code> - Link your account with code from web app\n' +
      '📊 /status - Check your connection status\n' +
      '🔌 /disconnect - Disconnect your account from this bot\n' +
      '❓ /help - Show this help message\n\n' +
      '💬 You can also interact with reminder notifications using the buttons provided.'
    )
  } else if (text?.startsWith('/link ')) {
    const linkCode = text.replace('/link ', '').trim().toUpperCase()
    await handleLinkCommand(chatId, linkCode, { username, firstName, lastName })
  } else if (text === '/status') {
    const telegramUser = await prisma.telegramUser.findFirst({
      where: { telegramId: chatId, isActive: true },
      include: { user: true }
    })

    if (telegramUser) {
      await sendMessage(chatId,
        `✅ Your account is linked!\n\n` +
        `📧 Account: ${telegramUser.user.email}\n` +
        `📅 Linked since: ${telegramUser.createdAt.toLocaleDateString()}\n\n` +
        `🔔 You're all set to receive smart reminders!`
      )
    } else {
      await sendMessage(chatId,
        `❌ Your account is not linked.\n\n` +
        `🔗 To link your account:\n` +
        `1. Visit ReminderAPP web app\n` +
        `2. Go to Settings → Telegram\n` +
        `3. Generate a link code\n` +
        `4. Send: /link <your-code>`
      )
    }
  } else if (text === '/disconnect') {
    console.log(`🔌 Disconnect command received from ${chatId}`)
    await handleDisconnectCommand(chatId)
  } else if (text && !text.startsWith('/')) {
    // Handle regular messages
    await sendMessage(chatId,
      '💬 I received your message! Use /help to see available commands.\n\n' +
      '🔗 If you haven\'t linked your account yet, use /start to get started.'
    )
  }
}

export async function handleDisconnectCommand(chatId: string): Promise<void> {
  try {
    console.log(`🔍 Processing disconnect command for chat ${chatId}`)
    // Find the active Telegram user connection
    const telegramUser = await prisma.telegramUser.findFirst({
      where: { telegramId: chatId, isActive: true },
      include: { user: true }
    })
    console.log(`📊 Found telegram user: ${telegramUser ? telegramUser.user.email : 'none'}`)

    if (!telegramUser) {
      await sendMessage(chatId,
        '❌ No active connection found.\n\n' +
        '🔗 Use /start to connect your account.'
      )
      return
    }

    // Show confirmation message with user details
    const keyboard = {
      inline_keyboard: [
        [
          { text: '✅ Yes, Disconnect', callback_data: 'confirm_disconnect' },
          { text: '❌ Cancel', callback_data: 'cancel_disconnect' }
        ]
      ]
    }

    await sendMessage(chatId,
      '⚠️ Are you sure you want to disconnect?\n\n' +
      `📧 Connected Account: ${telegramUser.user.email}\n` +
      `👤 Name: ${telegramUser.user.name || 'Not set'}\n\n` +
      '🔔 You will stop receiving email notifications and reminders.\n' +
      '🔗 You can reconnect anytime using /start and a new link code.',
      { replyMarkup: keyboard }
    )

  } catch (error) {
    console.error('❌ Error handling disconnect command:', error)
    await sendMessage(chatId,
      '❌ An error occurred while processing your request. Please try again later.'
    )
  }
}

async function handleLinkCommand(chatId: string, linkCode: string, userInfo: {
  username?: string
  firstName?: string
  lastName?: string
}): Promise<void> {
  try {
    console.log(`🔗 Processing link command for ${chatId} with code: ${linkCode}`)

    if (!linkCode || linkCode.length !== 8) {
      console.log(`❌ Invalid link code format: ${linkCode}`)
      const messageSent = await sendMessage(chatId,
        '❌ Invalid link code format.\n\n' +
        '💡 Link codes are 8 characters long.\n' +
        '📱 Get your code from ReminderAPP web app → Settings → Telegram'
      )
      if (!messageSent) {
        console.log('⚠️ Could not send error message to user, but validation failed')
      }
      return
    }

    // Find the link code in database
    const linkCodeRecord = await prisma.telegramLinkCode.findFirst({
      where: {
        code: linkCode,
        expiresAt: { gt: new Date() },
        isUsed: false
      },
      include: { user: true }
    })

    if (!linkCodeRecord) {
      await sendMessage(chatId,
        '❌ Invalid or expired link code.\n\n' +
        '🔄 Please generate a new code from the web app.\n' +
        '⏰ Link codes expire after 10 minutes.'
      )
      return
    }

    // Check if this Telegram account is already linked to another user
    const existingLink = await prisma.telegramUser.findFirst({
      where: { telegramId: chatId, isActive: true }
    })

    if (existingLink && existingLink.userId !== linkCodeRecord.userId) {
      await sendMessage(chatId,
        '⚠️ This Telegram account is already linked to another ReminderAPP account.\n\n' +
        '🔄 Please unlink first or use a different Telegram account.'
      )
      return
    }

    // Create or update the Telegram user link
    const telegramUser = await prisma.telegramUser.upsert({
      where: { telegramId: chatId },
      update: {
        userId: linkCodeRecord.userId,
        username: userInfo.username,
        firstName: userInfo.firstName || 'Unknown',
        lastName: userInfo.lastName,
        isActive: true,
        updatedAt: new Date()
      },
      create: {
        userId: linkCodeRecord.userId,
        telegramId: chatId,
        username: userInfo.username,
        firstName: userInfo.firstName || 'Unknown',
        lastName: userInfo.lastName,
        isActive: true
      }
    })

    // Mark the link code as used
    await prisma.telegramLinkCode.update({
      where: { id: linkCodeRecord.id },
      data: { isUsed: true, updatedAt: new Date() }
    })

    console.log(`✅ Telegram account linked: ${chatId} → ${linkCodeRecord.user.email}`)

    // Send success message
    const successMessageSent = await sendMessage(chatId,
      '🎉 Successfully linked to your ReminderAPP account!\n\n' +
      `📧 Account: ${linkCodeRecord.user.email}\n` +
      `👤 Name: ${linkCodeRecord.user.name || 'Not set'}\n\n` +
      '🔔 You\'ll now receive smart reminders and notifications here.\n\n' +
      '💡 Use /help to see available commands.'
    )

    if (!successMessageSent) {
      console.log('⚠️ Account linked successfully but could not send confirmation message to user')
      // Account is still linked even if message fails
    } else {
      console.log(`📱 Success message sent to ${chatId}`)
    }

    console.log(`Telegram account linked: ${chatId} → ${linkCodeRecord.user.email}`)

  } catch (error) {
    console.error('❌ Error handling link command:', error)

    // Try to send error message, but don't fail if it doesn't work
    const errorMessageSent = await sendMessage(chatId,
      '❌ An error occurred while linking your account.\n\n' +
      '🔄 Please try again or contact support if the issue persists.'
    )

    if (!errorMessageSent) {
      console.log('⚠️ Could not send error message to user, but error was logged')
    }
  }
}

async function handleCallbackQuery(callbackQuery: any): Promise<void> {
  const chatId = callbackQuery.message.chat.id.toString()
  const data = callbackQuery.data
  const messageId = callbackQuery.message.message_id

  if (data.startsWith('complete_')) {
    const reminderId = data.replace('complete_', '')
    await completeReminder(reminderId, chatId, messageId)
  } else if (data.startsWith('snooze_')) {
    const [, minutes, reminderId] = data.split('_')
    await snoozeReminder(reminderId, parseInt(minutes), chatId, messageId)
  } else if (data.startsWith('dismiss_')) {
    const reminderId = data.replace('dismiss_', '')
    await dismissReminder(reminderId, chatId, messageId)
  } else if (data === 'confirm_disconnect') {
    await confirmDisconnect(chatId, messageId)
  } else if (data === 'cancel_disconnect') {
    await cancelDisconnect(chatId, messageId)
  }
}

async function completeReminder(reminderId: string, chatId: string, messageId: number): Promise<void> {
  try {
    await prisma.reminder.update({
      where: { id: reminderId },
      data: { status: 'COMPLETED' }
    })

    const telegramBot = initializeTelegramBot()
    await telegramBot.editMessageText('✅ Reminder marked as complete!', {
      chat_id: chatId,
      message_id: messageId
    })
  } catch (error) {
    console.error('Error completing reminder:', error)
  }
}

async function snoozeReminder(reminderId: string, minutes: number, chatId: string, messageId: number): Promise<void> {
  try {
    const snoozeUntil = new Date(Date.now() + minutes * 60 * 1000)
    
    await prisma.reminder.update({
      where: { id: reminderId },
      data: { 
        status: 'SNOOZED',
        snoozeUntil
      }
    })

    const telegramBot = initializeTelegramBot()
    await telegramBot.editMessageText(
      `⏰ Reminder snoozed for ${minutes} minutes!\nWill remind you again at ${snoozeUntil.toLocaleTimeString()}`, 
      {
        chat_id: chatId,
        message_id: messageId
      }
    )
  } catch (error) {
    console.error('Error snoozing reminder:', error)
  }
}

async function dismissReminder(reminderId: string, chatId: string, messageId: number): Promise<void> {
  try {
    await prisma.reminder.update({
      where: { id: reminderId },
      data: { status: 'CANCELLED' }
    })

    const telegramBot = initializeTelegramBot()
    await telegramBot.editMessageText('❌ Reminder dismissed.', {
      chat_id: chatId,
      message_id: messageId
    })
  } catch (error) {
    console.error('Error dismissing reminder:', error)
  }
}

async function confirmDisconnect(chatId: string, messageId: number): Promise<void> {
  try {
    // Find and deactivate the Telegram user connection
    const telegramUser = await prisma.telegramUser.findFirst({
      where: { telegramId: chatId, isActive: true },
      include: { user: true }
    })

    if (!telegramUser) {
      const telegramBot = initializeTelegramBot()
      await telegramBot.editMessageText('❌ No active connection found.', {
        chat_id: chatId,
        message_id: messageId
      })
      return
    }

    // Deactivate the connection
    await prisma.telegramUser.update({
      where: { id: telegramUser.id },
      data: { isActive: false }
    })

    const telegramBot = initializeTelegramBot()
    await telegramBot.editMessageText(
      '✅ Successfully disconnected!\n\n' +
      `📧 Account: ${telegramUser.user.email}\n\n` +
      '🔔 You will no longer receive notifications.\n' +
      '🔗 Use /start to reconnect anytime.',
      {
        chat_id: chatId,
        message_id: messageId
      }
    )

    console.log(`🔌 User ${telegramUser.user.email} disconnected from Telegram bot`)

  } catch (error) {
    console.error('❌ Error confirming disconnect:', error)
    const telegramBot = initializeTelegramBot()
    await telegramBot.editMessageText('❌ An error occurred while disconnecting. Please try again.', {
      chat_id: chatId,
      message_id: messageId
    })
  }
}

async function cancelDisconnect(chatId: string, messageId: number): Promise<void> {
  try {
    const telegramBot = initializeTelegramBot()
    await telegramBot.editMessageText(
      '✅ Disconnect cancelled.\n\n' +
      '🔔 Your connection remains active.\n' +
      '💡 Use /help to see available commands.',
      {
        chat_id: chatId,
        message_id: messageId
      }
    )
  } catch (error) {
    console.error('❌ Error cancelling disconnect:', error)
  }
}

export async function sendReminderWithActions(userId: string, reminder: {
  id: string
  title: string
  description?: string
  type: 'meeting' | 'email' | 'custom'
  scheduledFor: Date
  actionButtons?: Array<{text: string, action: string}>
}): Promise<boolean> {
  try {
    const telegramUser = await prisma.telegramUser.findFirst({
      where: { userId, isActive: true }
    })

    if (!telegramUser) {
      return false
    }

    const message = formatAdvancedReminderMessage(reminder)
    const keyboard = createAdvancedReminderKeyboard(reminder.id, reminder.actionButtons)

    const messageId = await sendMessage(telegramUser.telegramId, message, {
      replyMarkup: keyboard,
      parseMode: 'HTML'
    })

    if (messageId) {
      await prisma.reminder.update({
        where: { id: reminder.id },
        data: { telegramMessageId: messageId }
      })
      return true
    }

    return false
  } catch (error) {
    console.error('Error sending advanced reminder notification:', error)
    return false
  }
}

export async function sendEmailSummary(userId: string, summary: {
  unreadCount: number
  importantCount: number
  urgentEmails: any[]
}): Promise<boolean> {
  try {
    const telegramUser = await prisma.telegramUser.findFirst({
      where: { userId, isActive: true }
    })

    if (!telegramUser) {
      return false
    }

    let message = `📧 <b>Email Summary</b>\n\n`
    message += `📬 Unread emails: ${summary.unreadCount}\n`
    message += `⭐ Important emails: ${summary.importantCount}\n\n`

    if (summary.urgentEmails.length > 0) {
      message += `🚨 <b>Urgent emails:</b>\n`
      summary.urgentEmails.slice(0, 3).forEach((email, index) => {
        message += `${index + 1}. <b>${email.subject}</b>\n`
        message += `   From: ${email.from}\n\n`
      })
    }

    const keyboard = {
      inline_keyboard: [
        [
          { text: '📧 Open Gmail', url: 'https://gmail.com' },
          { text: '📱 Open App', callback_data: 'open_app' }
        ]
      ]
    }

    await sendMessage(telegramUser.telegramId, message, {
      replyMarkup: keyboard,
      parseMode: 'HTML'
    })

    return true
  } catch (error) {
    console.error('Error sending email summary:', error)
    return false
  }
}

export async function sendCalendarDigest(userId: string, digest: {
  todayEvents: any[]
  tomorrowEvents: any[]
  upcomingImportant: any[]
}): Promise<boolean> {
  try {
    const telegramUser = await prisma.telegramUser.findFirst({
      where: { userId, isActive: true }
    })

    if (!telegramUser) {
      return false
    }

    let message = `📅 <b>Calendar Digest</b>\n\n`

    if (digest.todayEvents.length > 0) {
      message += `<b>Today (${digest.todayEvents.length} events):</b>\n`
      digest.todayEvents.forEach(event => {
        const time = new Date(event.startTime).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        })
        message += `• ${time} - ${event.title}\n`
      })
      message += '\n'
    }

    if (digest.tomorrowEvents.length > 0) {
      message += `<b>Tomorrow (${digest.tomorrowEvents.length} events):</b>\n`
      digest.tomorrowEvents.slice(0, 3).forEach(event => {
        const time = new Date(event.startTime).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        })
        message += `• ${time} - ${event.title}\n`
      })
    }

    const keyboard = {
      inline_keyboard: [
        [
          { text: '📅 Open Calendar', url: 'https://calendar.google.com' },
          { text: '📱 Open App', callback_data: 'open_app' }
        ]
      ]
    }

    await sendMessage(telegramUser.telegramId, message, {
      replyMarkup: keyboard,
      parseMode: 'HTML'
    })

    return true
  } catch (error) {
    console.error('Error sending calendar digest:', error)
    return false
  }
}

function formatAdvancedReminderMessage(reminder: {
  title: string
  description?: string
  type: 'meeting' | 'email' | 'custom'
  scheduledFor: Date
}): string {
  const typeEmoji = {
    meeting: '📅',
    email: '📧',
    custom: '⏰'
  }

  const emoji = typeEmoji[reminder.type]
  const timeStr = reminder.scheduledFor.toLocaleString()

  let message = `${emoji} <b>Smart Reminder</b>\n\n`
  message += `<b>${reminder.title}</b>\n`

  if (reminder.description) {
    message += `${reminder.description}\n\n`
  }

  message += `⏰ Scheduled for: ${timeStr}\n`
  message += `🤖 Powered by AI Reminder`

  return message
}

function createAdvancedReminderKeyboard(reminderId: string, actionButtons?: Array<{text: string, action: string}>) {
  const keyboard = {
    inline_keyboard: [
      [
        { text: '✅ Complete', callback_data: `complete_${reminderId}` },
        { text: '⏰ Snooze 15min', callback_data: `snooze_15_${reminderId}` }
      ],
      [
        { text: '⏰ Snooze 1hr', callback_data: `snooze_60_${reminderId}` },
        { text: '❌ Dismiss', callback_data: `dismiss_${reminderId}` }
      ]
    ]
  }

  if (actionButtons && actionButtons.length > 0) {
    const customRow = actionButtons.map(button => ({
      text: button.text,
      callback_data: `custom_${button.action}_${reminderId}`
    }))
    keyboard.inline_keyboard.unshift(customRow)
  }

  return keyboard
}

export async function setBotCommands(): Promise<boolean> {
  try {
    if (!TELEGRAM_BOT_TOKEN) {
      console.log('❌ Telegram bot token not configured')
      return false
    }

    const telegramBot = initializeTelegramBot()

    const commands = [
      { command: 'start', description: 'Start the bot and get setup instructions' },
      { command: 'help', description: 'Show available commands' },
      { command: 'status', description: 'Check your connection status' },
      { command: 'disconnect', description: 'Disconnect your account from this bot' }
    ]

    await telegramBot.setMyCommands(commands)
    console.log('✅ Bot commands registered successfully')
    return true
  } catch (error) {
    console.error('❌ Failed to set bot commands:', error)
    return false
  }
}

export async function checkTelegramConnection(): Promise<boolean> {
  try {
    if (!TELEGRAM_BOT_TOKEN) {
      return false
    }
    const telegramBot = initializeTelegramBot()
    await telegramBot.getMe()

    // Also set the bot commands
    await setBotCommands()

    return true
  } catch (error) {
    console.error('Telegram connection error:', error)
    return false
  }
}
