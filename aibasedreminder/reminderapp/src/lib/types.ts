// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// User Types
export interface User {
  id: string
  email: string
  name?: string
  avatar?: string
  isPro: boolean
  createdAt: string
  updatedAt: string
}

export interface UserProfile extends User {
  connections: {
    google: {
      connected: boolean
      scopes: string[]
      connectedAt?: string
    }
    telegram: {
      connected: boolean
      username?: string
      connectedAt?: string
    }
  }
  stats: {
    totalReminders: number
    totalEvents: number
    totalEmails: number
  }
}

// Authentication Types
export interface AuthTokens {
  token: string
  user: User
  googleConnected: boolean
}

export interface GoogleAuthUrl {
  authUrl: string
  scopes: string[]
}

// Calendar Types
export interface CalendarEvent {
  id: string
  googleEventId?: string
  title: string
  description?: string
  startTime: string
  endTime: string
  location?: string
  attendees: string[]
  reminders?: Reminder[]
  createdAt: string
  updatedAt: string
}

export interface CalendarEventInput {
  title: string
  description?: string
  startTime: string
  endTime: string
  location?: string
  attendees?: string[]
}

export interface CalendarSyncStats {
  totalGoogleEvents: number
  created: number
  updated: number
  skipped: number
  remindersCreated: number
  errors: Array<{ eventId: string; error: string }>
}

export interface CalendarAnalytics {
  period: {
    type: string
    startDate: string
    endDate: string
    label: string
  }
  summary: {
    totalEvents: number
    totalHours: number
    averageEventDuration: number
    busyDays: number
    freeDays: number
  }
  breakdown: {
    byDay: Array<{
      date: string
      dayName: string
      eventCount: number
      totalHours: number
    }>
    byHour: Array<{
      hour: number
      hourLabel: string
      eventCount: number
    }>
    byDuration: {
      short: number
      medium: number
      long: number
    }
  }
  patterns: {
    mostBusyDay: string
    mostBusyHour: number
    averageEventsPerDay: number
    longestEvent: CalendarEvent
    shortestEvent: CalendarEvent
  }
}

// Email Types
export interface Email {
  id: string
  gmailMessageId: string
  threadId: string
  subject: string
  from: string
  to: string[]
  cc: string[]
  body: string
  isRead: boolean
  isImportant: boolean
  hasAttachments: boolean
  receivedAt: string
  reminders?: Reminder[]
  createdAt: string
  updatedAt: string
}

export interface EmailCategories {
  urgent: Email[]
  important: Email[]
  normal: Email[]
  lowPriority: Email[]
}

export interface EmailSyncStats {
  totalGmailMessages: number
  created: number
  updated: number
  skipped: number
  remindersCreated: number
  errors: Array<{ messageId: string; error: string }>
}

export interface EmailAnalytics {
  period: {
    type: string
    startDate: string
    endDate: string
    label: string
  }
  summary: {
    totalEmails: number
    unreadEmails: number
    importantEmails: number
    emailsWithAttachments: number
    averageEmailsPerDay: number
    responseRate: number
  }
  breakdown: {
    byDay: Array<{
      date: string
      dayName: string
      emailCount: number
      unreadCount: number
      importantCount: number
    }>
    bySender: Array<{
      sender: string
      count: number
    }>
    byHour: Array<{
      hour: number
      hourLabel: string
      emailCount: number
    }>
  }
  patterns: {
    busiestDay: string
    busiestHour: number
    topSenders: Array<{ sender: string; count: number }>
    longestUnreadEmail: Email
    oldestUnreadEmail: Email
  }
}

// Reminder Types
export type ReminderType = 'MEETING' | 'EMAIL' | 'CUSTOM'
export type ReminderStatus = 'PENDING' | 'SENT' | 'COMPLETED' | 'CANCELLED' | 'SNOOZED' | 'FAILED'

export interface Reminder {
  id: string
  userId: string
  eventId?: string
  emailId?: string
  type: ReminderType
  title: string
  description?: string
  scheduledFor: string
  status: ReminderStatus
  sentAt?: string
  snoozeUntil?: string
  telegramMessageId?: string
  createdAt: string
  updatedAt: string
  event?: CalendarEvent
  email?: Email
}

export interface ReminderInput {
  eventId?: string
  emailId?: string
  type: ReminderType
  title: string
  description?: string
  scheduledFor: string
}

// AI Types
export interface AIEmailReply {
  id: string
  subject: string
  body: string
  confidence: number
  tone: 'professional' | 'casual' | 'friendly' | 'formal'
  length: 'short' | 'medium' | 'long'
  originalEmailId: string
}

export interface AISmartReminder {
  suggestions: {
    timing: string[]
    message: string
    priority: 'low' | 'medium' | 'high'
    confidence: number
    smartFeatures: {
      adaptiveTiming: boolean
      contextAware: boolean
      personalizedMessage: boolean
    }
  }
  analysis: {
    type: string
    confidence: number
    reasoning: string
  }
}

export interface AIContentAnalysis {
  urgency: 'low' | 'medium' | 'high' | 'critical'
  sentiment: 'positive' | 'neutral' | 'negative'
  actionItems: string[]
  keyTopics: string[]
  responsePriority: 'low' | 'medium' | 'high'
  suggestedResponseTime: string
  summary: string
  confidence: number
}

export interface AISchedulingSuggestion {
  timeSlots: Array<{
    start: string
    end: string
    score: number
    reasoning: string
  }>
  confidence: number
  reasoning: string
  alternatives: Array<{
    type: string
    description: string
    timeAdjustment?: number
    durationAdjustment?: number
  }>
}

// Telegram Types
export interface TelegramUser {
  id: string
  telegramId: string
  username?: string
  firstName: string
  lastName?: string
  linkedAt: string
}

export interface TelegramSendResult {
  telegramId: string
  username?: string
  success: boolean
  messageId?: string
  error?: string
}

// Settings Types
export interface CalendarSettings {
  autoSync: boolean
  syncInterval: number
  createReminders: boolean
  reminderTimings: string[]
  workingHours: {
    start: string
    end: string
    timezone: string
  }
  excludeWeekends: boolean
  excludeAllDayEvents: boolean
  calendarFilters: {
    includeCalendars: string[]
    excludeCalendars: string[]
    includeKeywords: string[]
    excludeKeywords: string[]
  }
}

export interface EmailSettings {
  autoSync: boolean
  syncInterval: number
  createReminders: boolean
  reminderTimings: string[]
  importantEmailReminders: boolean
  unreadEmailThreshold: number
  emailFilters: {
    includeSenders: string[]
    excludeSenders: string[]
    includeKeywords: string[]
    excludeKeywords: string[]
    includeLabels: string[]
    excludeLabels: string[]
  }
  aiSettings: {
    enableAiSuggestions: boolean
    defaultTone: 'professional' | 'casual' | 'friendly' | 'formal'
    defaultLength: 'short' | 'medium' | 'long'
    autoGenerateReplies: boolean
  }
  notificationSettings: {
    emailNotifications: boolean
    telegramNotifications: boolean
    urgentEmailsOnly: boolean
    quietHours: {
      enabled: boolean
      start: string
      end: string
      timezone: string
    }
  }
}

// System Types
export interface SystemStatus {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  version: string
  environment: string
  uptime: number
  services: {
    database: string
    telegram: string
    openai: string
    scheduler: string
    google_oauth: string
  }
  statistics: {
    users: {
      total: number
      active: number
      telegramConnected: number
    }
    reminders: {
      total: number
      pending: number
    }
    content: {
      emails: number
      events: number
    }
  }
}

// Search Types
export interface SearchParams {
  q?: string
  from?: string
  subject?: string
  isRead?: boolean
  isImportant?: boolean
  hasAttachments?: boolean
  dateFrom?: string
  dateTo?: string
  limit?: number
  offset?: number
}

export interface SearchResults<T> {
  items: T[]
  results: {
    highRelevance: T[]
    mediumRelevance: T[]
    lowRelevance: T[]
    noScore: T[]
  }
  pagination: {
    total: number
    limit: number
    offset: number
    hasMore: boolean
  }
  stats: {
    totalFound: number
    highRelevance: number
    mediumRelevance: number
    lowRelevance: number
  }
}
