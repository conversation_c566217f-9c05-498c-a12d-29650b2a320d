import { google } from 'googleapis'
import { prisma } from './prisma'
import { GoogleCalendarEvent, GmailMessage } from '@/types'

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET
const REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI || process.env.NEXTAUTH_URL + '/api/auth/google/callback'

export function createOAuth2Client() {
  return new google.auth.OAuth2(
    GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET,
    REDIRECT_URI
  )
}

export function getAuthUrl(scopes: string[]): string {
  const oauth2Client = createOAuth2Client()
  
  return oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: scopes,
    prompt: 'consent'
  })
}

export async function exchangeCodeForTokens(code: string) {
  const oauth2Client = createOAuth2Client()

  try {
    const { tokens } = await oauth2Client.getToken(code)
    return tokens
  } catch (error) {
    console.error('Error exchanging code for tokens:', error)
    throw new Error('Failed to exchange code for tokens')
  }
}

export async function getAuthenticatedClient(userId: string) {
  try {
    const tokenRecord = await prisma.oAuthToken.findFirst({
      where: {
        userId,
        provider: 'google'
      }
    })

    if (!tokenRecord) {
      throw new Error('No Google OAuth token found for user')
    }

    const oauth2Client = createOAuth2Client()
    oauth2Client.setCredentials({
      access_token: tokenRecord.accessToken,
      refresh_token: tokenRecord.refreshToken,
      expiry_date: tokenRecord.expiresAt.getTime()
    })

    if (tokenRecord.expiresAt < new Date()) {
      await refreshTokenIfNeeded(oauth2Client, tokenRecord)
    }

    return oauth2Client
  } catch (error) {
    console.error('Error getting authenticated client:', error)
    throw new Error('Failed to get authenticated Google client')
  }
}

async function refreshTokenIfNeeded(oauth2Client: any, tokenRecord: any) {
  try {
    const { credentials } = await oauth2Client.refreshAccessToken()
    
    await prisma.oAuthToken.update({
      where: { id: tokenRecord.id },
      data: {
        accessToken: credentials.access_token!,
        expiresAt: new Date(credentials.expiry_date!),
        updatedAt: new Date()
      }
    })

    oauth2Client.setCredentials(credentials)
  } catch (error) {
    console.error('Error refreshing token:', error)
    throw new Error('Failed to refresh Google OAuth token')
  }
}

export class GoogleCalendarService {
  private calendar: any

  constructor(private oauth2Client: any) {
    this.calendar = google.calendar({ version: 'v3', auth: oauth2Client })
  }

  async getEvents(timeMin?: Date, timeMax?: Date): Promise<GoogleCalendarEvent[]> {
    try {
      const response = await this.calendar.events.list({
        calendarId: 'primary',
        timeMin: timeMin?.toISOString(),
        timeMax: timeMax?.toISOString(),
        singleEvents: true,
        orderBy: 'startTime'
      })

      return response.data.items || []
    } catch (error) {
      console.error('Error fetching calendar events:', error)
      throw new Error('Failed to fetch calendar events')
    }
  }

  async createEvent(event: Partial<GoogleCalendarEvent>): Promise<GoogleCalendarEvent> {
    try {
      const response = await this.calendar.events.insert({
        calendarId: 'primary',
        resource: event
      })

      return response.data
    } catch (error) {
      console.error('Error creating calendar event:', error)
      throw new Error('Failed to create calendar event')
    }
  }

  async updateEvent(eventId: string, event: Partial<GoogleCalendarEvent>): Promise<GoogleCalendarEvent> {
    try {
      const response = await this.calendar.events.update({
        calendarId: 'primary',
        eventId,
        resource: event
      })

      return response.data
    } catch (error) {
      console.error('Error updating calendar event:', error)
      throw new Error('Failed to update calendar event')
    }
  }

  async deleteEvent(eventId: string): Promise<void> {
    try {
      await this.calendar.events.delete({
        calendarId: 'primary',
        eventId
      })
    } catch (error) {
      console.error('Error deleting calendar event:', error)
      throw new Error('Failed to delete calendar event')
    }
  }

  async getEvent(eventId: string): Promise<GoogleCalendarEvent> {
    try {
      const response = await this.calendar.events.get({
        calendarId: 'primary',
        eventId
      })

      return response.data
    } catch (error) {
      console.error('Error fetching calendar event:', error)
      throw new Error('Failed to fetch calendar event')
    }
  }

  async listCalendars(): Promise<any[]> {
    try {
      const response = await this.calendar.calendarList.list()
      return response.data.items || []
    } catch (error) {
      console.error('Error listing calendars:', error)
      throw new Error('Failed to list calendars')
    }
  }

  async watchEvents(webhookUrl: string, channelId?: string): Promise<any> {
    try {
      const response = await this.calendar.events.watch({
        calendarId: 'primary',
        resource: {
          id: channelId || `channel-${Date.now()}`,
          type: 'web_hook',
          address: webhookUrl,
          token: 'optional-verification-token'
        }
      })

      return response.data
    } catch (error) {
      console.error('Error setting up calendar watch:', error)
      throw new Error('Failed to set up calendar watch')
    }
  }

  async stopWatch(channelId: string, resourceId: string): Promise<void> {
    try {
      await this.calendar.channels.stop({
        resource: {
          id: channelId,
          resourceId
        }
      })
    } catch (error) {
      console.error('Error stopping calendar watch:', error)
      throw new Error('Failed to stop calendar watch')
    }
  }
}

export class GmailService {
  private gmail: any

  constructor(private oauth2Client: any) {
    this.gmail = google.gmail({ version: 'v1', auth: oauth2Client })
  }

  async getMessages(query?: string, maxResults: number = 50): Promise<GmailMessage[]> {
    try {
      const response = await this.gmail.users.messages.list({
        userId: 'me',
        q: query,
        maxResults
      })

      const messages = response.data.messages || []
      const detailedMessages = await Promise.all(
        messages.map(async (msg: any) => {
          const detail = await this.gmail.users.messages.get({
            userId: 'me',
            id: msg.id
          })
          return detail.data
        })
      )

      return detailedMessages
    } catch (error) {
      console.error('Error fetching Gmail messages:', error)
      throw new Error('Failed to fetch Gmail messages')
    }
  }

  async sendReply(messageId: string, threadId: string, replyContent: {
    to: string[]
    cc?: string[]
    subject: string
    body: string
  }): Promise<void> {
    try {
      const emailContent = this.createEmailContent(replyContent)
      
      await this.gmail.users.messages.send({
        userId: 'me',
        resource: {
          raw: emailContent,
          threadId
        }
      })
    } catch (error) {
      console.error('Error sending Gmail reply:', error)
      throw new Error('Failed to send Gmail reply')
    }
  }

  async getMessage(messageId: string): Promise<GmailMessage> {
    try {
      const response = await this.gmail.users.messages.get({
        userId: 'me',
        id: messageId
      })

      return response.data
    } catch (error) {
      console.error('Error fetching Gmail message:', error)
      throw new Error('Failed to fetch Gmail message')
    }
  }

  async markAsRead(messageId: string): Promise<void> {
    try {
      await this.gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        resource: {
          removeLabelIds: ['UNREAD']
        }
      })
    } catch (error) {
      console.error('Error marking email as read:', error)
      throw new Error('Failed to mark email as read')
    }
  }

  async markAsUnread(messageId: string): Promise<void> {
    try {
      await this.gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        resource: {
          addLabelIds: ['UNREAD']
        }
      })
    } catch (error) {
      console.error('Error marking email as unread:', error)
      throw new Error('Failed to mark email as unread')
    }
  }

  async markAsImportant(messageId: string): Promise<void> {
    try {
      await this.gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        resource: {
          addLabelIds: ['IMPORTANT']
        }
      })
    } catch (error) {
      console.error('Error marking email as important:', error)
      throw new Error('Failed to mark email as important')
    }
  }

  async deleteMessage(messageId: string): Promise<void> {
    try {
      await this.gmail.users.messages.trash({
        userId: 'me',
        id: messageId
      })
    } catch (error) {
      console.error('Error deleting Gmail message:', error)
      throw new Error('Failed to delete Gmail message')
    }
  }

  async getLabels(): Promise<any[]> {
    try {
      const response = await this.gmail.users.labels.list({
        userId: 'me'
      })

      return response.data.labels || []
    } catch (error) {
      console.error('Error fetching Gmail labels:', error)
      throw new Error('Failed to fetch Gmail labels')
    }
  }

  async searchMessages(query: string, maxResults: number = 50): Promise<GmailMessage[]> {
    try {
      const response = await this.gmail.users.messages.list({
        userId: 'me',
        q: query,
        maxResults
      })

      const messages = response.data.messages || []
      const detailedMessages = await Promise.all(
        messages.map(async (msg: any) => {
          const detail = await this.gmail.users.messages.get({
            userId: 'me',
            id: msg.id
          })
          return detail.data
        })
      )

      return detailedMessages
    } catch (error) {
      console.error('Error searching Gmail messages:', error)
      throw new Error('Failed to search Gmail messages')
    }
  }

  private createEmailContent(content: {
    to: string[]
    cc?: string[]
    subject: string
    body: string
  }): string {
    const headers = [
      `To: ${content.to.join(', ')}`,
      content.cc && content.cc.length > 0 ? `Cc: ${content.cc.join(', ')}` : '',
      `Subject: ${content.subject}`,
      'Content-Type: text/html; charset=utf-8',
      ''
    ].filter(Boolean).join('\r\n')

    const email = headers + '\r\n' + content.body
    return Buffer.from(email).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '')
  }
}

export async function getGoogleServices(userId: string) {
  const oauth2Client = await getAuthenticatedClient(userId)
  
  return {
    calendar: new GoogleCalendarService(oauth2Client),
    gmail: new GmailService(oauth2Client)
  }
}
