import TelegramBot from 'node-telegram-bot-api'

const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN

class TelegramBotManager {
  private bot: TelegramBot | null = null
  private isPolling = false
  private handlers: { [key: string]: Function[] } = {}

  constructor() {
    if (!TELEGRAM_BOT_TOKEN) {
      throw new Error('TELEGRAM_BOT_TOKEN is not configured')
    }
  }

  async initialize(): Promise<TelegramBot> {
    console.log('🚀 Initializing Telegram Bot Manager...')

    if (this.bot) {
      console.log('⚠️ Bot already exists, stopping current instance...')
      await this.stop()
    }

    try {
      // Create bot instance
      this.bot = new TelegramBot(TELEGRAM_BOT_TOKEN!, {
        polling: false,
        webHook: false
      })

      console.log('✅ Telegram bot instance created')
      return this.bot
    } catch (error) {
      console.error('❌ Failed to create Telegram bot:', error)
      throw error
    }
  }

  async startPolling(): Promise<void> {
    if (!this.bot) {
      throw new Error('Bot not initialized. Call initialize() first.')
    }

    if (this.isPolling) {
      console.log('⚠️ Polling already active')
      return
    }

    try {
      console.log('🔄 Starting Telegram bot polling...')

      // Set up error handling before starting polling
      this.bot.on('polling_error', (error) => {
        console.error('🚨 Telegram polling error:', error.message || error)
        this.isPolling = false

        // Don't auto-restart on conflicts - this usually means multiple instances
        if (error.message && (error.message.includes('409 Conflict') || error.message.includes('terminated by other getUpdates'))) {
          console.log('⚠️ Conflict detected - multiple bot instances running. Manual restart required.')
          return
        }

        // Log other errors but don't auto-restart to prevent loops
        console.log('⚠️ Polling stopped due to error. Use /api/telegram/init?restart=true to restart.')
      })

      // Start polling with more conservative settings
      await this.bot.startPolling({
        restart: true,
        polling: {
          interval: 2000, // Increased interval to reduce load
          params: {
            timeout: 5, // Reduced timeout to avoid EFATAL errors
            allowed_updates: ['message', 'callback_query'] // Only listen for what we need
          }
        }
      })

      this.isPolling = true
      console.log('✅ Telegram bot polling started successfully')

    } catch (error) {
      console.error('❌ Failed to start polling:', error)
      this.isPolling = false
      throw error
    }
  }

  async stop(): Promise<void> {
    if (!this.bot) return

    try {
      console.log('⏹️ Stopping Telegram bot...')

      if (this.isPolling) {
        try {
          // Use a timeout to prevent hanging
          await Promise.race([
            this.bot.stopPolling(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Stop polling timeout')), 5000))
          ])
        } catch (stopError) {
          console.warn('⚠️ Error stopping polling (forcing stop):', stopError.message)
        }
        this.isPolling = false
      }

      // Remove all listeners
      this.bot.removeAllListeners()

      // Clear the bot instance
      this.bot = null

      // Add a small delay to ensure cleanup
      await new Promise(resolve => setTimeout(resolve, 1000))

      console.log('✅ Telegram bot stopped')
    } catch (error) {
      console.error('❌ Error stopping bot:', error)
      // Force clear even if there's an error
      this.isPolling = false
      this.bot = null
    }
  }

  async setupMessageHandlers(): Promise<void> {
    if (!this.bot) {
      throw new Error('Bot not initialized')
    }

    console.log('🔄 Setting up message handlers...')

    try {
      // Import handlers
      const { handleMessage, handleCallbackQuery } = await import('@/app/api/telegram/webhook/route')

      // Clear existing listeners
      this.bot.removeAllListeners('message')
      this.bot.removeAllListeners('callback_query')

      // Set up message handler
      this.bot.on('message', async (msg) => {
        try {
          console.log(`📨 Received message from ${msg.from?.id} (${msg.from?.username}): "${msg.text}"`)
          await handleMessage(msg)
        } catch (error) {
          console.error('❌ Error handling message:', error)
        }
      })

      // Set up callback query handler
      this.bot.on('callback_query', async (query) => {
        try {
          console.log(`🔘 Received callback query from ${query.from?.id}: "${query.data}"`)
          await handleCallbackQuery(query)
        } catch (error) {
          console.error('❌ Error handling callback query:', error)
        }
      })

      console.log('✅ Message handlers set up successfully')
    } catch (error) {
      console.error('❌ Error setting up handlers:', error)
      throw error
    }
  }

  getBot(): TelegramBot | null {
    return this.bot
  }

  isPollingActive(): boolean {
    return this.isPolling && this.bot?.isPolling() === true
  }

  async getStatus(): Promise<{
    initialized: boolean
    polling: boolean
    botInfo?: any
  }> {
    if (!this.bot) {
      return { initialized: false, polling: false }
    }

    try {
      // Use a timeout for the getMe call to avoid hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Timeout')), 5000)
      })

      const me = await Promise.race([
        this.bot.getMe(),
        timeoutPromise
      ]) as any

      return {
        initialized: true,
        polling: this.isPollingActive(),
        botInfo: {
          id: me.id,
          username: me.username,
          first_name: me.first_name
        }
      }
    } catch (error) {
      console.warn('⚠️ Could not get bot info (this is normal during startup):', error.message)
      return {
        initialized: true,
        polling: this.isPollingActive(),
        botInfo: {
          status: 'Bot initialized but info unavailable'
        }
      }
    }
  }
}

// Export singleton instance
export const telegramBotManager = new TelegramBotManager()
export default telegramBotManager
