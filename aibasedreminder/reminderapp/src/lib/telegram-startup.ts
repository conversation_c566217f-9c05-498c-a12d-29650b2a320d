// TELEGRAM BOT STARTUP SYSTEM - Activates automatically when app starts

let startupActivated = false

// Auto-activate when this module is imported
if (typeof window === 'undefined' && !startupActivated) {
  console.log('🚀 STARTUP: Initializing Telegram bot auto-activation...')
  
  // Activate after a short delay to ensure environment is ready
  setTimeout(async () => {
    try {
      await activateTelegramBotOnStartup()
    } catch (error) {
      console.error('❌ STARTUP: Failed to activate bot:', error)
      // Retry after 30 seconds
      setTimeout(() => activateTelegramBotOnStartup(), 30000)
    }
  }, 8000) // 8 seconds after startup
}

async function activateTelegramBotOnStartup() {
  if (startupActivated) {
    return
  }

  try {
    console.log('🚀 STARTUP: Activating Telegram bot system...')
    startupActivated = true

    // Get the base URL
    const baseUrl = process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'https://aibasedreminder.vercel.app'
    
    console.log('🔗 STARTUP: Using base URL:', baseUrl)

    // Activate the auto-activate system
    const response = await fetch(`${baseUrl}/api/telegram/auto-activate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })

    if (response.ok) {
      const result = await response.json()
      console.log('✅ STARTUP: Telegram bot activated successfully!')
      console.log('📊 STARTUP result:', result)
    } else {
      console.error('❌ STARTUP: Failed to activate bot:', response.status)
      startupActivated = false // Allow retry
      
      // Retry after 30 seconds
      setTimeout(() => activateTelegramBotOnStartup(), 30000)
    }

  } catch (error) {
    console.error('❌ STARTUP: Error activating bot:', error)
    startupActivated = false // Allow retry
    
    // Retry after 30 seconds
    setTimeout(() => activateTelegramBotOnStartup(), 30000)
  }
}

export { activateTelegramBotOnStartup }
