import { prisma } from './prisma'
import { sendReminderNotification, sendEmailNotification, sendEmailSummary, sendCalendarDigest } from './telegram'
import { addMinutes, addHours, addDays, isAfter, isBefore } from 'date-fns'
import { getGoogleServices } from './google'

export class NotificationScheduler {
  private static instance: NotificationScheduler
  private isRunning = false
  private intervalId: NodeJS.Timeout | null = null
  private failedRefreshAttempts = new Map<string, number>() // Track failed refresh attempts per user
  private lastRefreshAttempt = new Map<string, number>() // Track last refresh attempt timestamp

  private constructor() {}

  static getInstance(): NotificationScheduler {
    if (!NotificationScheduler.instance) {
      NotificationScheduler.instance = new NotificationScheduler()
    }
    return NotificationScheduler.instance
  }

  start(): void {
    if (this.isRunning) {
      console.log('Notification scheduler is already running')
      return
    }

    console.log('Starting notification scheduler...')
    this.isRunning = true

    this.intervalId = setInterval(async () => {
      await this.processPendingNotifications()
      await this.checkForNewEmails()
    }, 10 * 1000) // Check every 10 seconds for faster email detection
    this.scheduleDailyDigests()
  }

  stop(): void {
    if (!this.isRunning) {
      return
    }

    console.log('Stopping notification scheduler...')
    this.isRunning = false

    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
  }

  async processPendingNotifications(): Promise<void> {
    try {
      const now = new Date()

      const pendingReminders = await prisma.reminder.findMany({
        where: {
          status: 'PENDING',
          scheduledFor: {
            lte: now
          }
        },
        include: {
          user: {
            include: {
              telegramUsers: {
                where: { isActive: true }
              }
            }
          },
          email: true
        },
        take: 100
      })

      console.log(`Processing ${pendingReminders.length} pending reminders`)

      for (const reminder of pendingReminders) {
        await this.processReminder(reminder)
      }

      // Process email read status updates
      await this.updateEmailReadStatus()

    } catch (error) {
      console.error('Error processing pending notifications:', error)
    }
  }

  async checkForNewEmails(): Promise<void> {
    try {
      // Get all users with active Telegram connections (including expired tokens for refresh attempt)
      const users = await prisma.user.findMany({
        where: {
          telegramUsers: {
            some: { isActive: true }
          },
          oauthTokens: {
            some: {
              provider: 'google'
            }
          }
        },
        include: {
          telegramUsers: {
            where: { isActive: true }
          },
          oauthTokens: {
            where: { provider: 'google' }
          }
        }
      })

      console.log(`Checking emails for ${users.length} users with Telegram connections`)

      // Separate users with valid vs expired tokens
      const validUsers = users.filter(user =>
        user.oauthTokens.some(token => token.expiresAt > new Date())
      )
      const expiredUsers = users.filter(user =>
        user.oauthTokens.every(token => token.expiresAt <= new Date())
      )

      console.log(`📧 ${validUsers.length} users with valid tokens, ${expiredUsers.length} users with expired tokens`)

      // Process users with valid tokens
      for (const user of validUsers) {
        await this.syncUserEmails(user)
      }

      // Attempt to refresh expired tokens and process (with cooldown)
      for (const user of expiredUsers) {
        const now = Date.now()
        const lastAttempt = this.lastRefreshAttempt.get(user.email) || 0
        const failedAttempts = this.failedRefreshAttempts.get(user.email) || 0

        // Implement exponential backoff: 5min, 15min, 30min, then 1 hour
        const cooldownPeriods = [5 * 60 * 1000, 15 * 60 * 1000, 30 * 60 * 1000, 60 * 60 * 1000]
        const cooldownPeriod = cooldownPeriods[Math.min(failedAttempts, cooldownPeriods.length - 1)]

        if (now - lastAttempt < cooldownPeriod) {
          continue // Skip this user due to cooldown
        }

        console.log(`🔄 Attempting to refresh expired token for ${user.email} (attempt ${failedAttempts + 1})`)
        try {
          await this.refreshUserTokenAndSync(user)
          // Reset failed attempts on success
          this.failedRefreshAttempts.delete(user.email)
          this.lastRefreshAttempt.delete(user.email)
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          this.lastRefreshAttempt.set(user.email, now)

          if (errorMessage.includes('invalid_grant')) {
            this.failedRefreshAttempts.set(user.email, failedAttempts + 1)
            if (failedAttempts === 0) { // Only log once
              console.log(`⚠️  ${user.email}: Refresh token expired - user needs to re-authenticate`)
            }
          } else {
            this.failedRefreshAttempts.set(user.email, failedAttempts + 1)
            console.error(`❌ Failed to refresh token for ${user.email}:`, errorMessage)
          }
        }
      }

    } catch (error) {
      console.error('Error checking for new emails:', error)
    }
  }

  private async refreshUserTokenAndSync(user: any): Promise<void> {
    try {
      const tokenRecord = user.oauthTokens[0]
      if (!tokenRecord || !tokenRecord.refreshToken) {
        console.log(`❌ No refresh token available for ${user.email}`)
        return
      }

      // Import Google OAuth client
      const { google } = await import('googleapis')
      const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        process.env.GOOGLE_REDIRECT_URI
      )

      oauth2Client.setCredentials({
        access_token: tokenRecord.accessToken,
        refresh_token: tokenRecord.refreshToken,
        expiry_date: tokenRecord.expiresAt.getTime()
      })

      // Attempt to refresh the token
      const { credentials } = await oauth2Client.refreshAccessToken()

      // Update the database with new token
      await prisma.oAuthToken.update({
        where: { id: tokenRecord.id },
        data: {
          accessToken: credentials.access_token!,
          expiresAt: new Date(credentials.expiry_date!),
          updatedAt: new Date()
        }
      })

      console.log(`✅ Successfully refreshed token for ${user.email}`)

      // Now sync emails with refreshed token
      await this.syncUserEmails(user)

    } catch (error) {
      console.error(`❌ Token refresh failed for ${user.email}:`, error)
      throw error
    }
  }

  private async syncUserEmails(user: any): Promise<void> {
    try {
      const { gmail } = await getGoogleServices(user.id)

      // Get recent unread emails (last 30 seconds for immediate detection)
      const thirtySecondsAgo = new Date(Date.now() - 30 * 1000)
      const query = `is:unread after:${Math.floor(thirtySecondsAgo.getTime() / 1000)}`

      const gmailMessages = await gmail.getMessages(query, 5)

      if (gmailMessages.length === 0) {
        return
      }

      console.log(`📧 Found ${gmailMessages.length} new emails for ${user.email}`)

      for (const gmailMessage of gmailMessages) {
        // Check if we already processed this email
        const existingEmail = await prisma.email.findFirst({
          where: {
            userId: user.id,
            gmailMessageId: gmailMessage.id
          }
        })

        if (existingEmail) {
          continue // Skip already processed emails
        }

        // Process new email with comprehensive label categorization
        const emailData = await this.processEmailWithLabels(user.id, gmailMessage)

        // Save email to database
        const dbEmail = await prisma.email.create({
          data: emailData
        })

        // Send immediate Telegram notification
        console.log(`📧 Sending immediate notification for new email: ${emailData.subject}`)

        const notificationSent = await sendEmailNotification(user.id, {
          id: dbEmail.id,
          subject: emailData.subject,
          from: emailData.from,
          body: emailData.body,
          isImportant: emailData.isImportant,
          receivedAt: emailData.receivedAt,
          labels: JSON.parse(emailData.labels || '[]')
        })

        if (notificationSent) {
          console.log(`✅ Immediate notification sent for: ${emailData.subject}`)

          // Create persistent reminder for unread emails
          if (!emailData.isRead) {
            await this.createEmailReminder(user.id, dbEmail.id, emailData.subject)
          }
        }
      }

    } catch (error) {
      console.error(`Error syncing emails for user ${user.email}:`, error)
    }
  }

  private async processEmailWithLabels(userId: string, gmailMessage: any): Promise<any> {
    try {
      // Extract headers
      const headers = gmailMessage.payload?.headers || []
      const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
      const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown Sender'
      const to = headers.find((h: any) => h.name === 'To')?.value?.split(',').map((email: string) => email.trim()) || []
      const cc = headers.find((h: any) => h.name === 'Cc')?.value?.split(',').map((email: string) => email.trim()) || []

      // Extract email body
      let body = ''
      if (gmailMessage.payload?.parts) {
        const textPart = gmailMessage.payload.parts.find((part: any) => part.mimeType === 'text/plain')
        if (textPart?.body?.data) {
          body = Buffer.from(textPart.body.data, 'base64').toString('utf-8')
        }
      } else if (gmailMessage.payload?.body?.data) {
        body = Buffer.from(gmailMessage.payload.body.data, 'base64').toString('utf-8')
      }

      // Process Gmail labels for categorization
      const labelIds = gmailMessage.labelIds || []
      const labels = this.categorizeEmailLabels(labelIds)

      // Determine email status and importance
      const isRead = !labelIds.includes('UNREAD')
      const isImportant = labelIds.includes('IMPORTANT') || false
      const isSpam = labelIds.includes('SPAM') || false
      const isStarred = labelIds.includes('STARRED') || false
      const isSnoozed = labelIds.includes('SNOOZED') || false
      const isDraft = labelIds.includes('DRAFT') || false
      const isSent = labelIds.includes('SENT') || false
      const hasAttachments = gmailMessage.payload?.parts?.some((part: any) => part.filename) || false

      return {
        userId,
        gmailMessageId: gmailMessage.id,
        threadId: gmailMessage.threadId || '',
        subject,
        from,
        to: to.join(','),
        cc: cc.join(','),
        body: body.substring(0, 2000), // Limit body length
        isRead,
        isImportant,
        hasAttachments,
        labels: JSON.stringify(labels), // Store labels as JSON string
        receivedAt: new Date(parseInt(gmailMessage.internalDate || '0'))
      }
    } catch (error) {
      console.error('Error processing email with labels:', error)
      throw error
    }
  }

  private categorizeEmailLabels(labelIds: string[]): string[] {
    const categories = []

    // Core Gmail labels
    if (labelIds.includes('UNREAD')) categories.push('UNREAD')
    if (labelIds.includes('SPAM')) categories.push('SPAM')
    if (labelIds.includes('STARRED')) categories.push('STARRED')
    if (labelIds.includes('SNOOZED')) categories.push('SNOOZED')
    if (labelIds.includes('DRAFT')) categories.push('DRAFT')
    if (labelIds.includes('SENT')) categories.push('SENT')
    if (labelIds.includes('IMPORTANT')) categories.push('IMPORTANT')
    if (labelIds.includes('INBOX')) categories.push('INBOX')

    // Custom labels (user-created)
    labelIds.forEach(labelId => {
      if (!['UNREAD', 'SPAM', 'STARRED', 'SNOOZED', 'DRAFT', 'SENT', 'IMPORTANT', 'INBOX', 'CATEGORY_PERSONAL', 'CATEGORY_SOCIAL', 'CATEGORY_PROMOTIONS', 'CATEGORY_UPDATES', 'CATEGORY_FORUMS'].includes(labelId)) {
        categories.push(labelId)
      }
    })

    return categories
  }

  private async createEmailReminder(userId: string, emailId: string, subject: string): Promise<void> {
    try {
      // Create a reminder for unread email that will trigger in 5 minutes
      const reminderTime = addMinutes(new Date(), 5)

      await prisma.reminder.create({
        data: {
          userId,
          emailId,
          title: `Unread Email Reminder: ${subject}`,
          description: `You have an unread email: "${subject}". This reminder will repeat every 5 minutes until the email is read.`,
          type: 'EMAIL',
          scheduledFor: reminderTime,
          status: 'PENDING'
        }
      })

      console.log(`📅 Created email reminder for: ${subject}`)
    } catch (error) {
      console.error('Error creating email reminder:', error)
    }
  }

  private async updateEmailReadStatus(): Promise<void> {
    try {
      // Get all users with active Telegram connections and valid OAuth tokens
      const users = await prisma.user.findMany({
        where: {
          telegramUsers: {
            some: { isActive: true }
          },
          oauthTokens: {
            some: {
              provider: 'google',
              expiresAt: {
                gt: new Date()
              }
            }
          }
        },
        include: {
          emails: {
            where: { isRead: false },
            take: 20 // Limit to avoid overwhelming API
          }
        }
      })

      for (const user of users) {
        if (user.emails.length === 0) continue

        try {
          const { gmail } = await getGoogleServices(user.id)

          for (const email of user.emails) {
            try {
              // Check current status of email in Gmail
              const gmailMessage = await gmail.getMessage(email.gmailMessageId)
              const isCurrentlyRead = !gmailMessage.labelIds?.includes('UNREAD')

              if (isCurrentlyRead && !email.isRead) {
                // Email was marked as read in Gmail, update database
                await prisma.email.update({
                  where: { id: email.id },
                  data: { isRead: true }
                })

                // Cancel any pending email reminders for this email
                await prisma.reminder.updateMany({
                  where: {
                    emailId: email.id,
                    status: 'PENDING'
                  },
                  data: {
                    status: 'CANCELLED'
                  }
                })

                console.log(`✅ Email marked as read, cancelled reminders: ${email.subject}`)
              }
            } catch (emailError) {
              console.error(`Error checking email ${email.gmailMessageId}:`, emailError)
            }
          }
        } catch (userError) {
          console.error(`Error updating email status for user ${user.email}:`, userError)
        }
      }
    } catch (error) {
      console.error('Error updating email read status:', error)
    }
  }

  private async processReminder(reminder: any): Promise<void> {
    try {
      if (!reminder.user.telegramUsers || reminder.user.telegramUsers.length === 0) {
        console.log(`Skipping reminder ${reminder.id} - no Telegram connection`)
        await this.markReminderAsSkipped(reminder.id)
        return
      }

      // Special handling for email reminders
      if (reminder.type === 'EMAIL' && reminder.email) {
        // Check if the email is still unread
        const currentEmail = await prisma.email.findUnique({
          where: { id: reminder.email.id }
        })

        if (!currentEmail || currentEmail.isRead) {
          // Email was read, cancel this reminder
          await prisma.reminder.update({
            where: { id: reminder.id },
            data: { status: 'CANCELLED' }
          })
          console.log(`Email reminder cancelled - email was read: ${reminder.title}`)
          return
        }

        // Send email reminder notification
        const sent = await sendEmailNotification(reminder.userId, {
          id: currentEmail.id,
          subject: `🔔 REMINDER: ${currentEmail.subject}`,
          from: currentEmail.from,
          body: currentEmail.body,
          isImportant: currentEmail.isImportant,
          receivedAt: currentEmail.receivedAt,
          labels: JSON.parse(currentEmail.labels || '[]')
        })

        if (sent) {
          // Mark current reminder as sent and create next reminder in 5 minutes
          await prisma.reminder.update({
            where: { id: reminder.id },
            data: { status: 'SENT' }
          })

          // Create next recurring reminder
          const nextReminderTime = addMinutes(new Date(), 5)
          await prisma.reminder.create({
            data: {
              userId: reminder.userId,
              emailId: reminder.emailId,
              title: reminder.title,
              description: reminder.description,
              type: 'EMAIL',
              scheduledFor: nextReminderTime,
              status: 'PENDING'
            }
          })

          console.log(`Email reminder sent and next reminder scheduled: ${reminder.title}`)
        } else {
          await this.markReminderAsFailed(reminder.id)
        }

        return
      }

      // Handle regular reminders
      const sent = await sendReminderNotification(reminder.userId, {
        id: reminder.id,
        title: reminder.title,
        description: reminder.description,
        type: reminder.type.toLowerCase() as any,
        scheduledFor: reminder.scheduledFor
      })

      if (sent) {
        await prisma.reminder.update({
          where: { id: reminder.id },
          data: {
            status: 'SENT'
          }
        })
        console.log(`Reminder ${reminder.id} sent successfully`)
      } else {
        await this.markReminderAsFailed(reminder.id)
      }

    } catch (error) {
      console.error(`Error processing reminder ${reminder.id}:`, error)
      await this.markReminderAsFailed(reminder.id)
    }
  }

  private async processSnoozedReminders(): Promise<void> {
    try {
      // Note: Snooze functionality disabled as snoozeUntil field not in current schema
      // This method is kept for future implementation
      console.log('Snooze functionality not implemented in current schema')
    } catch (error) {
      console.error('Error processing snoozed reminders:', error)
    }
  }

  private async markReminderAsSkipped(reminderId: string): Promise<void> {
    await prisma.reminder.update({
      where: { id: reminderId },
      data: {
        status: 'CANCELLED',
        updatedAt: new Date()
      }
    })
  }

  private async markReminderAsFailed(reminderId: string): Promise<void> {
    await prisma.reminder.update({
      where: { id: reminderId },
      data: {
        status: 'CANCELLED'
      }
    })
  }

  private scheduleDailyDigests(): void {
    this.scheduleRecurringTask('morning-digest', '08:00', async () => {
      await this.sendMorningDigests()
    })

    this.scheduleRecurringTask('evening-summary', '18:00', async () => {
      await this.sendEveningSummaries()
    })
  }

  private scheduleRecurringTask(taskName: string, time: string, task: () => Promise<void>): void {
    const [hours, minutes] = time.split(':').map(Number)
    
    const scheduleNext = () => {
      const now = new Date()
      const scheduledTime = new Date()
      scheduledTime.setHours(hours, minutes, 0, 0)
      
      if (scheduledTime <= now) {
        scheduledTime.setDate(scheduledTime.getDate() + 1)
      }
      
      const delay = scheduledTime.getTime() - now.getTime()
      
      setTimeout(async () => {
        try {
          console.log(`Running scheduled task: ${taskName}`)
          await task()
        } catch (error) {
          console.error(`Error in scheduled task ${taskName}:`, error)
        }
        
        scheduleNext()
      }, delay)
    }
    
    scheduleNext()
  }

  private async sendMorningDigests(): Promise<void> {
    try {
      const users = await prisma.user.findMany({
        include: {
          telegramUsers: {
            where: { isActive: true }
          }
        },
        where: {
          telegramUsers: {
            some: { isActive: true }
          }
        }
      })

      for (const user of users) {
        await this.sendUserMorningDigest(user.id)
      }

    } catch (error) {
      console.error('Error sending morning digests:', error)
    }
  }

  private async sendEveningSummaries(): Promise<void> {
    try {
      const users = await prisma.user.findMany({
        include: {
          telegramUsers: {
            where: { isActive: true }
          }
        },
        where: {
          telegramUsers: {
            some: { isActive: true }
          }
        }
      })

      for (const user of users) {
        await this.sendUserEveningSummary(user.id)
      }

    } catch (error) {
      console.error('Error sending evening summaries:', error)
    }
  }

  private async sendUserMorningDigest(userId: string): Promise<void> {
    try {
      const today = new Date()
      const tomorrow = addDays(today, 1)
      
      const todayEvents = await prisma.calendarEvent.findMany({
        where: {
          userId,
          startTime: {
            gte: new Date(today.getFullYear(), today.getMonth(), today.getDate()),
            lt: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)
          }
        },
        orderBy: { startTime: 'asc' }
      })

      const tomorrowEvents = await prisma.calendarEvent.findMany({
        where: {
          userId,
          startTime: {
            gte: new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate()),
            lt: new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate() + 1)
          }
        },
        orderBy: { startTime: 'asc' }
      })

      const upcomingImportant = await prisma.calendarEvent.findMany({
        where: {
          userId,
          startTime: { gte: today },
          OR: [
            { title: { contains: 'important' } },
            { description: { contains: 'important' } }
          ]
        },
        orderBy: { startTime: 'asc' },
        take: 3
      })

      await sendCalendarDigest(userId, {
        todayEvents,
        tomorrowEvents,
        upcomingImportant
      })

    } catch (error) {
      console.error(`Error sending morning digest for user ${userId}:`, error)
    }
  }

  private async sendUserEveningSummary(userId: string): Promise<void> {
    try {
      const [unreadEmails, importantEmails] = await Promise.all([
        prisma.email.count({
          where: { userId, isRead: false }
        }),
        prisma.email.findMany({
          where: { userId, isImportant: true, isRead: false },
          orderBy: { receivedAt: 'desc' },
          take: 5
        })
      ])

      const urgentEmails = await prisma.email.findMany({
        where: {
          userId,
          isRead: false,
          OR: [
            { subject: { contains: 'urgent' } },
            { subject: { contains: 'asap' } },
            { isImportant: true }
          ]
        },
        orderBy: { receivedAt: 'desc' },
        take: 3
      })

      await sendEmailSummary(userId, {
        unreadCount: unreadEmails,
        importantCount: importantEmails.length,
        urgentEmails
      })

    } catch (error) {
      console.error(`Error sending evening summary for user ${userId}:`, error)
    }
  }

  async sendTestNotification(userId: string): Promise<boolean> {
    return await sendReminderNotification(userId, {
      id: 'test',
      title: 'Test Notification',
      description: 'This is a test notification from AI Reminder',
      type: 'custom',
      scheduledFor: new Date()
    })
  }

  getStatus(): { isRunning: boolean; uptime?: number } {
    return {
      isRunning: this.isRunning,
      uptime: this.isRunning ? Date.now() : undefined
    }
  }
}

export const notificationScheduler = NotificationScheduler.getInstance()
