import axios, { AxiosInstance, AxiosResponse } from 'axios'
import Cookies from 'js-cookie'
import { toast } from 'sonner'
import type {
  ApiResponse,
  User,
  UserProfile,
  AuthTokens,
  GoogleAuthUrl,
  CalendarEvent,
  CalendarEventInput,
  CalendarSyncStats,
  CalendarAnalytics,
  Email,
  EmailCategories,
  EmailSyncStats,
  EmailAnalytics,
  Reminder,
  ReminderInput,
  AIEmailReply,
  AISmartReminder,
  AIContentAnalysis,
  AISchedulingSuggestion,
  TelegramUser,
  CalendarSettings,
  EmailSettings,
  SystemStatus,
  SearchParams,
  SearchResults
} from './types'

class ApiClient {
  private client: AxiosInstance
  private baseURL: string

  constructor() {
    // Use current domain in production, localhost in development
    this.baseURL = process.env.NEXT_PUBLIC_API_URL ||
      (typeof window !== 'undefined' && window.location.origin) ||
      'http://localhost:3001'
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 60000, // Increased to 60 seconds for email operations
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response
      },
      (error) => {
        if (error.response?.status === 401) {
          this.clearToken()
          toast.error('Session expired. Please login again.')
          // Redirect to login or refresh page
          window.location.reload()
        } else if (error.response?.status === 429) {
          toast.error('Too many requests. Please try again later.')
        } else if (error.response?.data?.message) {
          toast.error(error.response.data.message)
        } else if (error.message) {
          toast.error(error.message)
        } else {
          toast.error('An unexpected error occurred')
        }
        return Promise.reject(error)
      }
    )
  }

  // Token management
  private getToken(): string | null {
    try {
      const cookieToken = Cookies.get('auth_token')
      const localToken = localStorage.getItem('auth_token')
      return cookieToken || localToken
    } catch (error) {
      console.error('Error getting token:', error)
      return null
    }
  }

  private setToken(token: string): void {
    if (!token || typeof token !== 'string') {
      console.error('Invalid token provided to setToken:', token)
      return
    }

    try {
      Cookies.set('auth_token', token, { expires: 7, secure: true, sameSite: 'strict' })
      localStorage.setItem('auth_token', token)
    } catch (error) {
      console.error('Error setting token:', error)
    }
  }

  private clearToken(): void {
    Cookies.remove('auth_token')
    localStorage.removeItem('auth_token')
  }

  // Helper method for API calls
  private async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any,
    params?: any
  ): Promise<T> {
    try {
      const response = await this.client.request<ApiResponse<T>>({
        method,
        url: endpoint,
        data,
        params,
      })
      
      if (response.data.success) {
        return response.data.data as T
      } else {
        throw new Error(response.data.message || 'API request failed')
      }
    } catch (error) {
      console.error(`API ${method} ${endpoint} error:`, error)
      throw error
    }
  }

  // Authentication API
  async getGoogleAuthUrl(): Promise<GoogleAuthUrl> {
    return this.request<GoogleAuthUrl>('GET', '/api/auth/google')
  }

  async handleGoogleCallback(code: string): Promise<AuthTokens> {
    if (!code || typeof code !== 'string') {
      throw new Error('Invalid authorization code provided')
    }

    try {
      console.log('📡 API Client: Sending OAuth callback request...')
      const response = await this.request<any>('POST', '/api/auth/google/callback', { code })
      console.log('📥 API Client: Raw response:', response)

      // Handle different response formats
      let result: AuthTokens

      if (response.success && response.data) {
        console.log('✅ API Client: Success response format detected')
        // Backend returns { success: true, data: { user, token, googleConnected } }
        result = {
          token: response.data.token,
          user: response.data.user,
          googleConnected: response.data.googleConnected || true
        }
      } else if (response.token) {
        console.log('✅ API Client: Direct token response format detected')
        // Direct token response
        result = response as AuthTokens
      } else {
        console.error('❌ API Client: Invalid response format:', response)
        throw new Error('Invalid response format from authentication server')
      }

      if (!result.token) {
        console.error('❌ API Client: No token in result:', result)
        throw new Error('No authentication token received')
      }

      console.log('💾 API Client: Setting token...')
      this.setToken(result.token)
      console.log('✅ API Client: OAuth callback complete')
      return result

    } catch (error) {
      console.error('❌ API Client: Google callback error:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('Authentication failed')
    }
  }

  async login(email: string, password?: string): Promise<AuthTokens> {
    const result = await this.request<AuthTokens>('POST', '/api/auth/login', { email, password })
    this.setToken(result.token)
    return result
  }

  async register(email: string, name: string): Promise<AuthTokens> {
    const result = await this.request<AuthTokens>('POST', '/api/auth/register', { email, name })
    this.setToken(result.token)
    return result
  }

  async getCurrentUser(): Promise<UserProfile> {
    return this.request<UserProfile>('GET', '/api/auth/me')
  }

  async refreshToken(): Promise<AuthTokens> {
    const result = await this.request<AuthTokens>('POST', '/api/auth/refresh')
    this.setToken(result.token)
    return result
  }

  async logout(): Promise<void> {
    await this.request<void>('POST', '/api/auth/logout')
    this.clearToken()
  }

  // User Management API
  async updateProfile(data: { name?: string; avatar?: string }): Promise<User> {
    return this.request<User>('PUT', '/api/user/profile', data)
  }

  async deleteAccount(): Promise<void> {
    await this.request<void>('DELETE', '/api/user/profile')
    this.clearToken()
  }

  async getUserConnections(): Promise<UserProfile['connections']> {
    return this.request<UserProfile['connections']>('GET', '/api/user/connections')
  }

  async disconnectService(provider: 'google' | 'telegram'): Promise<void> {
    return this.request<void>('DELETE', `/api/user/connections?provider=${provider}`)
  }

  // Calendar API
  async getCalendarEvents(params?: {
    timeMin?: string
    timeMax?: string
    maxResults?: number
    sync?: boolean
  }): Promise<{ events: CalendarEvent[]; categories?: any; synced?: boolean }> {
    return this.request<{ events: CalendarEvent[]; categories?: any; synced?: boolean }>('GET', '/api/calendar/events', undefined, params)
  }

  async createCalendarEvent(event: CalendarEventInput): Promise<{ event: CalendarEvent; googleEventId?: string }> {
    return this.request<{ event: CalendarEvent; googleEventId?: string }>('POST', '/api/calendar/events', event)
  }

  async getCalendarEvent(eventId: string, fresh?: boolean): Promise<{ event: CalendarEvent }> {
    return this.request<{ event: CalendarEvent }>('GET', `/api/calendar/events/${eventId}`, undefined, { fresh })
  }

  async updateCalendarEvent(eventId: string, event: Partial<CalendarEventInput>): Promise<{ event: CalendarEvent }> {
    return this.request<{ event: CalendarEvent }>('PUT', `/api/calendar/events/${eventId}`, event)
  }

  async deleteCalendarEvent(eventId: string): Promise<void> {
    return this.request<void>('DELETE', `/api/calendar/events/${eventId}`)
  }

  async syncCalendar(options?: {
    timeMin?: string
    timeMax?: string
    fullSync?: boolean
    createReminders?: boolean
  }): Promise<{ syncStats: CalendarSyncStats }> {
    return this.request<{ syncStats: CalendarSyncStats }>('POST', '/api/calendar/sync', options)
  }

  async getCalendarSyncStatus(includeStats?: boolean): Promise<{ lastSyncTime?: string; stats?: any }> {
    return this.request<{ lastSyncTime?: string; stats?: any }>('GET', '/api/calendar/sync', undefined, { stats: includeStats })
  }

  async searchCalendarEvents(params: SearchParams): Promise<SearchResults<CalendarEvent>> {
    return this.request<SearchResults<CalendarEvent>>('GET', '/api/calendar/search', undefined, params)
  }

  async advancedCalendarSearch(criteria: any): Promise<{ events: CalendarEvent[] }> {
    return this.request<{ events: CalendarEvent[] }>('POST', '/api/calendar/search', criteria)
  }

  async getCalendarAnalytics(period?: string, includeReminders?: boolean): Promise<CalendarAnalytics> {
    return this.request<CalendarAnalytics>('GET', '/api/calendar/analytics', undefined, { period, reminders: includeReminders })
  }

  async getCalendarSettings(): Promise<{ settings: CalendarSettings }> {
    return this.request<{ settings: CalendarSettings }>('GET', '/api/calendar/settings')
  }

  async updateCalendarSettings(settings: Partial<CalendarSettings>): Promise<{ settings: CalendarSettings }> {
    return this.request<{ settings: CalendarSettings }>('PUT', '/api/calendar/settings', settings)
  }

  async resetCalendarSettings(): Promise<{ settings: CalendarSettings }> {
    return this.request<{ settings: CalendarSettings }>('POST', '/api/calendar/settings')
  }

  // Email API
  async getEmails(params?: {
    q?: string
    maxResults?: number
    sync?: boolean
    includeRead?: boolean
    limit?: number
    offset?: number
  }): Promise<{ emails: Email[]; categories: EmailCategories; pagination: any }> {
    try {
      return await this.request<{ emails: Email[]; categories: EmailCategories; pagination: any }>('GET', '/api/emails', undefined, params)
    } catch (error) {
      console.error('❌ getEmails error:', error)
      // Return empty result on timeout or error to prevent UI crash
      if (error instanceof Error && (error.message.includes('timeout') || error.message.includes('ECONNABORTED'))) {
        console.log('⏰ Email fetch timed out, returning empty result')
        return {
          emails: [],
          categories: { urgent: [], important: [], normal: [], lowPriority: [] },
          pagination: { total: 0, page: 1, limit: 50, hasMore: false }
        }
      }
      throw error
    }
  }

  async sendEmail(data: {
    to: string[]
    cc?: string[]
    bcc?: string[]
    subject: string
    htmlBody?: string
    textBody?: string
    threadId?: string
    replyToMessageId?: string
  }): Promise<{ sent: boolean }> {
    return this.request<{ sent: boolean }>('POST', '/api/emails', data)
  }

  async getEmail(emailId: string, fresh?: boolean): Promise<{ email: Email }> {
    return this.request<{ email: Email }>('GET', `/api/emails/${emailId}`, undefined, { fresh })
  }

  async updateEmail(emailId: string, data: { isRead?: boolean; isImportant?: boolean }): Promise<{ email: Email }> {
    return this.request<{ email: Email }>('PUT', `/api/emails/${emailId}`, data)
  }

  async deleteEmail(emailId: string): Promise<void> {
    return this.request<void>('DELETE', `/api/emails/${emailId}`)
  }

  async sendEmailReply(data: {
    to: string[]
    cc?: string[]
    subject: string
    body: string
    threadId?: string
    originalEmailId?: string
  }): Promise<{ sent: boolean }> {
    return this.request<{ sent: boolean }>('POST', '/api/emails/reply', data)
  }

  async syncEmails(options?: {
    query?: string
    maxResults?: number
    fullSync?: boolean
    createReminders?: boolean
  }): Promise<{ syncStats: EmailSyncStats }> {
    return this.request<{ syncStats: EmailSyncStats }>('POST', '/api/emails/sync', options)
  }

  async getEmailSyncStatus(includeStats?: boolean): Promise<{ lastSyncTime?: string; stats?: any }> {
    return this.request<{ lastSyncTime?: string; stats?: any }>('GET', '/api/emails/sync', undefined, { stats: includeStats })
  }

  async searchEmails(params: SearchParams): Promise<SearchResults<Email>> {
    return this.request<SearchResults<Email>>('GET', '/api/emails/search', undefined, params)
  }

  async advancedEmailSearch(criteria: any): Promise<{ emails: Email[] }> {
    return this.request<{ emails: Email[] }>('POST', '/api/emails/search', criteria)
  }

  async getEmailAnalytics(period?: string, includeReminders?: boolean): Promise<EmailAnalytics> {
    return this.request<EmailAnalytics>('GET', '/api/emails/analytics', undefined, { period, reminders: includeReminders })
  }

  async getEmailSettings(): Promise<{ settings: EmailSettings }> {
    return this.request<{ settings: EmailSettings }>('GET', '/api/emails/settings')
  }

  async updateEmailSettings(settings: Partial<EmailSettings>): Promise<{ settings: EmailSettings }> {
    return this.request<{ settings: EmailSettings }>('PUT', '/api/emails/settings', settings)
  }

  async resetEmailSettings(): Promise<{ settings: EmailSettings }> {
    return this.request<{ settings: EmailSettings }>('POST', '/api/emails/settings')
  }

  // AI API (Pro features)
  async generateEmailReply(data: {
    emailId: string
    tone?: 'professional' | 'casual' | 'friendly' | 'formal'
    length?: 'short' | 'medium' | 'long'
    context?: string
    customInstructions?: string
  }): Promise<{ suggestion: AIEmailReply }> {
    return this.request<{ suggestion: AIEmailReply }>('POST', '/api/emails/ai-reply', data)
  }

  async getAIEmailReplies(limit?: number, offset?: number): Promise<{ suggestions: any[] }> {
    return this.request<{ suggestions: any[] }>('GET', '/api/emails/ai-reply', undefined, { limit, offset })
  }

  async generateSmartReminder(data: {
    eventType: string
    eventData: any
    userPreferences?: any
    analysisType?: string
  }): Promise<AISmartReminder> {
    return this.request<AISmartReminder>('POST', '/api/ai/smart-reminders', data)
  }

  async getSmartReminderHistory(limit?: number): Promise<{ suggestions: any[] }> {
    return this.request<{ suggestions: any[] }>('GET', '/api/ai/smart-reminders', undefined, { limit })
  }

  async analyzeContent(data: {
    contentType: 'email' | 'calendar'
    contentId: string
    analysisType?: string
  }): Promise<{ analysis: AIContentAnalysis }> {
    return this.request<{ analysis: AIContentAnalysis }>('POST', '/api/ai/content-analysis', data)
  }

  async getContentAnalysisHistory(contentType?: string, limit?: number): Promise<{ analyses: any[] }> {
    return this.request<{ analyses: any[] }>('GET', '/api/ai/content-analysis', undefined, { contentType, limit })
  }

  async getIntelligentScheduling(data: {
    schedulingRequest: any
    preferences?: any
    constraints?: any
    analysisDepth?: string
  }): Promise<AISchedulingSuggestion> {
    return this.request<AISchedulingSuggestion>('POST', '/api/ai/intelligent-scheduling', data)
  }

  async getOptimalTimeSlots(params?: {
    days?: number
    duration?: number
    weekends?: boolean
  }): Promise<{ optimalSlots: any[] }> {
    return this.request<{ optimalSlots: any[] }>('GET', '/api/ai/intelligent-scheduling', undefined, params)
  }

  // Reminder API
  async getReminders(params?: {
    status?: string
    type?: string
    limit?: number
    offset?: number
  }): Promise<{ reminders: Reminder[]; pagination: any }> {
    return this.request<{ reminders: Reminder[]; pagination: any }>('GET', '/api/reminders', undefined, params)
  }

  async createReminder(reminder: ReminderInput): Promise<{ reminder: Reminder }> {
    return this.request<{ reminder: Reminder }>('POST', '/api/reminders', reminder)
  }

  async getReminder(reminderId: string): Promise<{ reminder: Reminder }> {
    return this.request<{ reminder: Reminder }>('GET', `/api/reminders/${reminderId}`)
  }

  async updateReminder(reminderId: string, data: Partial<ReminderInput>): Promise<{ reminder: Reminder }> {
    return this.request<{ reminder: Reminder }>('PUT', `/api/reminders/${reminderId}`, data)
  }

  async deleteReminder(reminderId: string): Promise<void> {
    return this.request<void>('DELETE', `/api/reminders/${reminderId}`)
  }

  async snoozeReminder(reminderId: string, minutes: number): Promise<{ reminder: Reminder }> {
    return this.request<{ reminder: Reminder }>('POST', `/api/reminders/${reminderId}/snooze`, { minutes })
  }

  async completeReminder(reminderId: string): Promise<{ reminder: Reminder }> {
    return this.request<{ reminder: Reminder }>('POST', `/api/reminders/${reminderId}/complete`)
  }

  // Telegram API
  async getTelegramStatus(): Promise<{ telegramAccounts: TelegramUser[]; isLinked: boolean }> {
    return this.request<{ telegramAccounts: TelegramUser[]; isLinked: boolean }>('GET', '/api/telegram/link')
  }

  async linkTelegramAccount(data: {
    telegramId: string
    username?: string
    firstName: string
    lastName?: string
  }): Promise<{ linked: boolean; telegramUser: TelegramUser }> {
    return this.request<{ linked: boolean; telegramUser: TelegramUser }>('POST', '/api/telegram/link', data)
  }

  async unlinkTelegramAccount(): Promise<{ unlinked: boolean }> {
    return this.request<{ unlinked: boolean }>('DELETE', '/api/telegram/link')
  }

  async sendTelegramMessage(data: {
    message: string
    parseMode?: 'HTML' | 'Markdown'
    replyMarkup?: any
  }): Promise<{ sent: boolean; messageIds: string[] }> {
    return this.request<{ sent: boolean; messageIds: string[] }>('POST', '/api/telegram/send', data)
  }

  async generateTelegramLinkCode(): Promise<{ linkCode: string; expiresAt: string; instructions: string[] }> {
    return this.request<{ linkCode: string; expiresAt: string; instructions: string[] }>('POST', '/api/telegram/generate-link-code')
  }

  // System API
  async getSystemStatus(): Promise<SystemStatus> {
    return this.request<SystemStatus>('GET', '/api/system/status')
  }

  async runSystemTest(testType: string, testData?: any): Promise<{ result: any }> {
    return this.request<{ result: any }>('POST', '/api/system/test', { testType, testData })
  }

  async getHealthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.request<{ status: string; timestamp: string }>('GET', '/api/health')
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.getToken()
  }

  getApiUrl(): string {
    return this.baseURL
  }
}

// Export singleton instance
export const apiClient = new ApiClient()
export default apiClient
