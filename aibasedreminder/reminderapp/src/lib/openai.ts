import OpenAI from 'openai'

let openai: OpenAI | null = null

export function getOpenAIClient(): OpenAI {
  if (!openai) {
    const apiKey = process.env.OPENAI_API_KEY
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY environment variable is not set')
    }
    openai = new OpenAI({ apiKey })
  }
  return openai
}

export interface EmailReplyRequest {
  originalEmail: {
    subject: string
    from: string
    body: string
    receivedAt: string
  }
  context?: string
  tone?: 'professional' | 'casual' | 'friendly' | 'formal'
  length?: 'short' | 'medium' | 'long'
}

export interface EmailReplyResponse {
  subject: string
  body: string
  confidence: number
}

export async function generateEmailReply(request: EmailReplyRequest): Promise<EmailReplyResponse> {
  try {
    const openaiClient = getOpenAIClient()
    const { originalEmail, context, tone = 'professional', length = 'medium' } = request

    const systemPrompt = `You are an AI assistant that helps users write professional email replies.
    Generate appropriate email responses based on the original email content and user preferences.

    Guidelines:
    - Maintain a ${tone} tone
    - Keep the response ${length} in length
    - Be helpful and relevant
    - Include proper email etiquette
    - Don't make assumptions about information not provided
    - If the email requires specific information you don't have, suggest the user provide it`

    const userPrompt = `Please generate a reply to this email:

    From: ${originalEmail.from}
    Subject: ${originalEmail.subject}
    Received: ${originalEmail.receivedAt}

    Original Email:
    ${originalEmail.body}

    ${context ? `Additional Context: ${context}` : ''}

    Please provide:
    1. A suggested subject line for the reply
    2. The email body content
    3. A confidence score (0-100) indicating how confident you are about the appropriateness of this reply`

    const completion = await openaiClient.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 1000
    })

    const response = completion.choices[0]?.message?.content
    if (!response) {
      throw new Error('No response generated from OpenAI')
    }

    const lines = response.split('\n')
    let subject = ''
    let body = ''
    let confidence = 85 
    let currentSection = ''
    for (const line of lines) {
      if (line.toLowerCase().includes('subject')) {
        currentSection = 'subject'
        subject = line.replace(/.*subject[:\-\s]*/i, '').trim()
      } else if (line.toLowerCase().includes('body') || line.toLowerCase().includes('email')) {
        currentSection = 'body'
      } else if (line.toLowerCase().includes('confidence')) {
        const match = line.match(/(\d+)/)
        if (match) {
          confidence = parseInt(match[1])
        }
      } else if (currentSection === 'body' && line.trim()) {
        body += line + '\n'
      }
    }

    if (!body.trim()) {
      body = response
      subject = `Re: ${originalEmail.subject}`
    }

    return {
      subject: subject || `Re: ${originalEmail.subject}`,
      body: body.trim(),
      confidence: Math.min(Math.max(confidence, 0), 100)
    }

  } catch (error) {
    console.error('Error generating email reply:', error)
    throw new Error('Failed to generate email reply')
  }
}

export interface ReminderSuggestionRequest {
  eventType: 'meeting' | 'email' | 'custom'
  eventData: {
    title: string
    description?: string
    startTime?: string
    participants?: string[]
    priority?: 'low' | 'medium' | 'high'
  }
  userPreferences?: {
    reminderTiming?: string[]
    workingHours?: { start: string; end: string }
    timezone?: string
  }
}

export interface ReminderSuggestion {
  timing: string[]
  message: string
  priority: 'low' | 'medium' | 'high'
}

export async function generateReminderSuggestions(request: ReminderSuggestionRequest): Promise<ReminderSuggestion> {
  try {
    const openaiClient = getOpenAIClient()
    const { eventType, eventData, userPreferences } = request

    const systemPrompt = `You are an AI assistant that helps users create smart reminders for meetings and emails.
    Analyze the event details and suggest appropriate reminder timing and messages.

    Consider:
    - Event importance and type
    - User's working hours and preferences
    - Best practices for reminder timing
    - Clear, actionable reminder messages`

    const userPrompt = `Please suggest reminders for this ${eventType}:

    Title: ${eventData.title}
    ${eventData.description ? `Description: ${eventData.description}` : ''}
    ${eventData.startTime ? `Start Time: ${eventData.startTime}` : ''}
    ${eventData.participants ? `Participants: ${eventData.participants.join(', ')}` : ''}
    Priority: ${eventData.priority || 'medium'}

    ${userPreferences ? `User Preferences:
    ${userPreferences.reminderTiming ? `Preferred Timing: ${userPreferences.reminderTiming.join(', ')}` : ''}
    ${userPreferences.workingHours ? `Working Hours: ${userPreferences.workingHours.start} - ${userPreferences.workingHours.end}` : ''}
    ${userPreferences.timezone ? `Timezone: ${userPreferences.timezone}` : ''}` : ''}

    Please provide:
    1. Suggested reminder timing (e.g., "15 minutes before", "1 hour before", "1 day before")
    2. A clear reminder message
    3. Priority level (low/medium/high)`

    const completion = await openaiClient.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.5,
      max_tokens: 500
    })

    const response = completion.choices[0]?.message?.content
    if (!response) {
      throw new Error('No response generated from OpenAI')
    }

    const defaultTiming = eventType === 'meeting' ? ['15 minutes before', '1 hour before'] : ['1 day after']
    const defaultMessage = `Reminder: ${eventData.title}`
    const defaultPriority = eventData.priority || 'medium'

    return {
      timing: defaultTiming,
      message: response.split('\n').find(line => line.toLowerCase().includes('message'))?.replace(/.*message[:\-\s]*/i, '') || defaultMessage,
      priority: defaultPriority as 'low' | 'medium' | 'high'
    }

  } catch (error) {
    console.error('Error generating reminder suggestions:', error)
    throw new Error('Failed to generate reminder suggestions')
  }
}

export async function checkOpenAIConnection(): Promise<boolean> {
  try {
    if (!process.env.OPENAI_API_KEY) {
      return false
    }
    const openaiClient = getOpenAIClient()
    await openaiClient.models.list()
    return true
  } catch (error) {
    console.error('OpenAI connection error:', error)
    return false
  }
}
