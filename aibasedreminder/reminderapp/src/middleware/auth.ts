import { NextRequest, NextResponse } from 'next/server'
import { getUserFromToken, extractTokenFromHeader } from '@/lib/auth'
import { User } from '@/types'
import { prisma } from '@/lib/prisma'

export interface AuthenticatedRequest extends NextRequest {
  user?: User
}

export async function authenticateRequest(request: NextRequest): Promise<User | null> {
  try {
    const authHeader = request.headers.get('authorization')
    const token = extractTokenFromHeader(authHeader)
    
    if (!token) {
      return null
    }

    const user = await getUserFromToken(token)
    return user
  } catch (error) {
    console.error('Authentication middleware error:', error)
    return null
  }
}

export function requireAuth() {
  return async (request: NextRequest): Promise<User | null> => {
    const user = await authenticateRequest(request)
    return user
  }
}

export function requireProUser() {
  return async (request: NextRequest): Promise<User | null> => {
    const user = await authenticateRequest(request)

    if (!user || !user.isPro) {
      return null
    }

    return user
  }
}

export function requireGoogle<T extends any[]>(
  handler: (request: NextRequest, user: User, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      // Get user from session/auth
      const user = await authenticateRequest(request)

      if (!user) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        )
      }

      // Check if user has valid Google OAuth token
      const oauthToken = await prisma.oAuthToken.findFirst({
        where: {
          userId: user.id,
          provider: 'google',
          expiresAt: {
            gt: new Date()
          }
        }
      })

      if (!oauthToken) {
        return NextResponse.json(
          { success: false, error: 'Google authentication required' },
          { status: 401 }
        )
      }

      return await handler(request, user, ...args)
    } catch (error) {
      console.error('requireGoogle middleware error:', error)
      return NextResponse.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}
