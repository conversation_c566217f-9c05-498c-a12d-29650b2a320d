import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from './auth'
import { unauthorizedResponse, forbiddenResponse } from '@/utils/response'
import { User } from '@/types'

export interface ProtectedRouteOptions {
  requirePro?: boolean
  requireGoogleAuth?: boolean
  requireTelegram?: boolean
}

export function withAuth(
  handler: (request: NextRequest, user: User) => Promise<NextResponse>,
  options: ProtectedRouteOptions = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      const user = await authenticateRequest(request)
      if (!user) {
        return unauthorizedResponse('Authentication required')
      }

      if (options.requirePro && !user.isPro) {
        return forbiddenResponse('Pro subscription required')
      }

      if (options.requireGoogleAuth) {
        const { prisma } = await import('@/lib/prisma')
        const googleToken = await prisma.oAuthToken.findFirst({
          where: {
            userId: user.id,
            provider: 'google',
            expiresAt: { gt: new Date() }
          }
        })

        if (!googleToken) {
          return forbiddenResponse('Google account connection required')
        }
      }

      if (options.requireTelegram) {
        const { prisma } = await import('@/lib/prisma')
        const telegramUser = await prisma.telegramUser.findFirst({
          where: {
            userId: user.id,
            isActive: true
          }
        })

        if (!telegramUser) {
          return forbiddenResponse('Telegram account connection required')
        }
      }

      return await handler(request, user)

    } catch (error) {
      console.error('Protected route error:', error)
      return unauthorizedResponse('Authentication failed')
    }
  }
}

export function requireAuth(handler: (request: NextRequest, user: User) => Promise<NextResponse>) {
  return withAuth(handler)
}

export function requirePro(handler: (request: NextRequest, user: User) => Promise<NextResponse>) {
  return withAuth(handler, { requirePro: true })
}

export function requireGoogle(handler: (request: NextRequest, user: User) => Promise<NextResponse>) {
  return withAuth(handler, { requireGoogleAuth: true })
}

export function requireTelegram(handler: (request: NextRequest, user: User) => Promise<NextResponse>) {
  return withAuth(handler, { requireTelegram: true })
}

export function requireProAndGoogle(handler: (request: NextRequest, user: User) => Promise<NextResponse>) {
  return withAuth(handler, { requirePro: true, requireGoogleAuth: true })
}
