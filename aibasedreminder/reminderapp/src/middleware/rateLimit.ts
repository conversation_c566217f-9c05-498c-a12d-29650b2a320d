import { NextRequest } from 'next/server'

interface RateLimitConfig {
  windowMs: number
  maxRequests: number
  keyGenerator?: (request: NextRequest) => string
}

interface RateLimitStore {
  [key: string]: {
    count: number
    resetTime: number
  }
}

const store: RateLimitStore = {}

setInterval(() => {
  const now = Date.now()
  Object.keys(store).forEach(key => {
    if (store[key].resetTime < now) {
      delete store[key]
    }
  })
}, 5 * 60 * 1000)

export function createRateLimit(config: RateLimitConfig) {
  const { windowMs, maxRequests, keyGenerator } = config

  return (request: NextRequest): { allowed: boolean; remaining: number; resetTime: number } => {
    const key = keyGenerator ? keyGenerator(request) : getDefaultKey(request)
    const now = Date.now()
    const resetTime = now + windowMs

    if (!store[key] || store[key].resetTime < now) {
      store[key] = {
        count: 0,
        resetTime
      }
    }

    store[key].count++

    const allowed = store[key].count <= maxRequests
    const remaining = Math.max(0, maxRequests - store[key].count)

    return {
      allowed,
      remaining,
      resetTime: store[key].resetTime
    }
  }
}

function getDefaultKey(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : request.ip || 'unknown'
  return `rate_limit:${ip}`
}

export const generalRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, 
  maxRequests: 100
})

export const authRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000,
  maxRequests: process.env.NODE_ENV === 'development' ? 1000 : 100, // Very high limit for development
  keyGenerator: (request) => {
    const forwarded = request.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : request.ip || 'unknown'
    return `auth_rate_limit:${ip}`
  }
})

export const apiRateLimit = createRateLimit({
  windowMs: 60 * 1000, 
  maxRequests: 60
})

export const telegramRateLimit = createRateLimit({
  windowMs: 60 * 1000,
  maxRequests: 30
})

// Function to clear rate limit store (useful for development/testing)
export function clearRateLimitStore() {
  Object.keys(store).forEach(key => {
    delete store[key]
  })
}
