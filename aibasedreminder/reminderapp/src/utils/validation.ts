import { z } from 'zod'

export const userRegistrationSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(1, 'Name is required').optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional()
})

export const userLoginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required').optional()
})

export const reminderCreateSchema = z.object({
  type: z.enum(['meeting', 'email', 'custom']),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  scheduledFor: z.string().datetime('Invalid date format'),
  eventId: z.string().optional(),
  emailId: z.string().optional()
})

export const reminderUpdateSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  description: z.string().optional(),
  scheduledFor: z.string().datetime('Invalid date format').optional(),
  status: z.enum(['pending', 'sent', 'snoozed', 'completed', 'cancelled']).optional(),
  snoozeUntil: z.string().datetime('Invalid date format').optional()
})

export const calendarEventSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  startTime: z.string().datetime('Invalid start time format'),
  endTime: z.string().datetime('Invalid end time format'),
  location: z.string().optional(),
  attendees: z.array(z.string().email()).optional()
})

export const emailReplySchema = z.object({
  to: z.array(z.string().email()),
  cc: z.array(z.string().email()).optional(),
  subject: z.string().min(1, 'Subject is required'),
  body: z.string().min(1, 'Body is required'),
  threadId: z.string().optional()
})

export const telegramUserSchema = z.object({
  telegramId: z.string().min(1, 'Telegram ID is required'),
  username: z.string().optional(),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().optional()
})

export function validateRequest<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; error: string } {
  try {
    const validatedData = schema.parse(data)
    return { success: true, data: validatedData }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
      return { success: false, error: errorMessage }
    }
    return { success: false, error: 'Validation failed' }
  }
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export function sanitizeString(str: string): string {
  return str.trim().replace(/[<>]/g, '')
}
