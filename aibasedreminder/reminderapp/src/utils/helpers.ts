import { format, parseISO, addMinutes, addHours, addDays, isAfter, isBefore } from 'date-fns'

export function formatDateTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  return format(dateObj, 'yyyy-MM-dd HH:mm:ss')
}

export function formatDateTimeForUser(date: Date | string, timezone?: string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  
  if (timezone) {
    return dateObj.toLocaleString('en-US', { timeZone: timezone })
  }
  
  return format(dateObj, 'MMM dd, yyyy at h:mm a')
}

export function addTimeToDate(date: Date, amount: number, unit: 'minutes' | 'hours' | 'days'): Date {
  switch (unit) {
    case 'minutes':
      return addMinutes(date, amount)
    case 'hours':
      return addHours(date, amount)
    case 'days':
      return addDays(date, amount)
    default:
      return date
  }
}

export function isDateInRange(date: Date, start: Date, end: Date): boolean {
  return isAfter(date, start) && isBefore(date, end)
}

export function truncateString(str: string, maxLength: number): string {
  if (str.length <= maxLength) return str
  return str.substring(0, maxLength - 3) + '...'
}

export function capitalizeFirstLetter(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

export function slugify(str: string): string {
  return str
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

export function uniqueArray<T>(array: T[]): T[] {
  return [...new Set(array)]
}

export function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size))
  }
  return chunks
}

export function omitKeys<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  const result = { ...obj }
  keys.forEach(key => delete result[key])
  return result
}

export function pickKeys<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key]
    }
  })
  return result
}

export function extractEmailDomain(email: string): string {
  return email.split('@')[1] || ''
}

export function isValidEmailDomain(domain: string): boolean {
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/
  return domainRegex.test(domain)
}

export function parseEmailAddresses(emailString: string): string[] {
  return emailString
    .split(/[,;]/)
    .map(email => email.trim())
    .filter(email => email.length > 0)
}

export function getPriorityColor(priority: 'low' | 'medium' | 'high'): string {
  switch (priority) {
    case 'low':
      return '#10B981' 
    case 'medium':
      return '#F59E0B' 
    case 'high':
      return '#EF4444' 
    default:
      return '#6B7280' 
  }
}

export function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'pending':
      return '#F59E0B' 
    case 'sent':
      return '#10B981' 
    case 'snoozed':
      return '#8B5CF6'
    case 'completed':
      return '#10B981'
    case 'cancelled':
      return '#6B7280' 
    default:
      return '#6B7280' 
  }
}

export function parseReminderTiming(timing: string): { amount: number; unit: 'minutes' | 'hours' | 'days' } | null {
  const match = timing.match(/(\d+)\s*(minute|hour|day)s?\s*(before|after)?/i)
  if (!match) return null

  const amount = parseInt(match[1])
  const unit = match[2].toLowerCase() + 's' as 'minutes' | 'hours' | 'days'

  return { amount, unit }
}

export function calculateReminderTime(eventTime: Date, timing: string): Date | null {
  const parsed = parseReminderTiming(timing)
  if (!parsed) return null

  const isAfter = timing.toLowerCase().includes('after')
  const multiplier = isAfter ? 1 : -1

  return addTimeToDate(eventTime, parsed.amount * multiplier, parsed.unit)
}

export function createErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  if (typeof error === 'string') {
    return error
  }
  return 'An unknown error occurred'
}

export function logError(context: string, error: unknown): void {
  console.error(`[${context}] Error:`, error)
}

export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export async function retryAsync<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: unknown

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      if (i < maxRetries) {
        await delay(delayMs * Math.pow(2, i)) 
      }
    }
  }

  throw lastError
}

export function getEnvVar(name: string, defaultValue?: string): string {
  const value = process.env[name]
  if (!value && !defaultValue) {
    throw new Error(`Environment variable ${name} is required`)
  }
  return value || defaultValue!
}

export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production'
}

export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development'
}
