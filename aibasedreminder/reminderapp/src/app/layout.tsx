import type { <PERSON>ada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/sonner'
import { ServiceInitializer } from '@/components/ServiceInitializer'
import '@/lib/telegram-startup' // Auto-activate Telegram bot on startup
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'ReminderAPP - Smart Calendar & Email Management',
    template: '%s | ReminderAPP'
  },
  description: 'Intelligent reminder system with AI-powered features, Google Calendar sync, Gmail integration, and Telegram notifications. Never miss important events or emails again.',
  keywords: [
    'ReminderAPP',
    'AI reminder',
    'smart calendar',
    'email management',
    'Google Calendar',
    'Gmail integration',
    'Telegram bot',
    'productivity',
    'scheduling',
    'artificial intelligence',
    'automation',
    'notifications',
    'task management',
    'calendar sync',
    'email sync',
    'smart notifications'
  ],
  authors: [{ name: 'ReminderAPP Team' }],
  creator: 'ReminderAPP Team',
  publisher: 'ReminderAPP',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3001'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'ReminderAPP - Smart Calendar & Email Management',
    description: 'Intelligent reminder system with AI-powered features, Google Calendar sync, Gmail integration, and Telegram notifications. Never miss important events or emails again.',
    siteName: 'ReminderAPP',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'ReminderAPP - Smart Calendar & Email Management',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ReminderAPP - Smart Calendar & Email Management',
    description: 'Intelligent reminder system with AI-powered features, Google Calendar sync, Gmail integration, and Telegram notifications.',
    images: ['/og-image.png'],
    creator: '@reminderapp',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
  category: 'productivity',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/placeholder-logo.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/placeholder-logo.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/placeholder-logo.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#000000" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "ReminderAPP",
              "description": "Intelligent reminder system with AI-powered features, Google Calendar sync, Gmail integration, and Telegram notifications.",
              "url": process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3001",
              "applicationCategory": "ProductivityApplication",
              "operatingSystem": "Web",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
              },
              "featureList": [
                "AI-powered reminders",
                "Google Calendar integration",
                "Gmail synchronization",
                "Telegram notifications",
                "Smart scheduling",
                "Email management",
                "Analytics dashboard"
              ]
            })
          }}
        />
      </head>
      <body className={inter.className}>
        <ServiceInitializer />
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              style: {
                background: '#1f2937',
                color: '#f9fafb',
                border: '1px solid #374151',
              },
            }}
          />
        </ThemeProvider>
      </body>
    </html>
  )
}
