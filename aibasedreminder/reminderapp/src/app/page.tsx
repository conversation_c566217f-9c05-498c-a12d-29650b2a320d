"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { Navbar } from "@/components/navbar"
import { <PERSON> } from "@/components/hero"
import { Features } from "@/components/features"
import { AnnouncementBanner } from "@/components/announcement-banner"
import { HowItWorks } from "@/components/how-it-works"
import { CTASection } from "@/components/cta-section"
import { Footer } from "@/components/footer"
import { Dashboard } from "@/components/dashboard/dashboard"
import { useAuthStore } from "@/lib/stores/auth-store"
import { Loader2, CheckCircle, XCircle } from "lucide-react"
import { toast } from "sonner"

type AppState = 'landing' | 'dashboard' | 'auth-callback' | 'auth-error'

// Component that handles search params (needs to be wrapped in Suspense)
function AuthCallbackHandler({
  onStateChange,
  onStatusChange,
  onMessageChange
}: {
  onStateChange: (state: AppState) => void
  onStatusChange: (status: 'loading' | 'success' | 'error') => void
  onMessageChange: (message: string) => void
}) {
  const searchParams = useSearchParams()
  const { handleGoogleCallback } = useAuthStore()

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const code = searchParams?.get('code')
        const error = searchParams?.get('error')

        console.log('🔍 Auth callback debug:', { code: !!code, error, url: window.location.href })

        if (error) {
          console.error('❌ OAuth error:', error)
          onStateChange('auth-error')
          onStatusChange('error')
          onMessageChange(`Authentication failed: ${error}`)
          toast.error(`Authentication failed: ${error}`)
          setTimeout(() => onStateChange('landing'), 5000)
          return
        }

        if (code && typeof code === 'string' && code.trim()) {
          console.log('✅ Processing OAuth code:', code.substring(0, 20) + '...')
          onStateChange('auth-callback')
          onStatusChange('loading')
          onMessageChange('Processing authentication...')

          try {
            console.log('🔄 Calling handleGoogleCallback...')
            const result = await handleGoogleCallback(code)
            console.log('✅ Authentication successful:', result)

            onStatusChange('success')
            onMessageChange('Successfully authenticated! Welcome to ReminderAPP!')
            toast.success('Successfully authenticated!')

            // Clear URL parameters and show dashboard
            window.history.replaceState({}, document.title, window.location.pathname)

            // Wait a bit longer and ensure user state is set before switching to dashboard
            setTimeout(() => {
              console.log('🎯 Switching to dashboard...')
              onStateChange('dashboard')
            }, 1500)

          } catch (error) {
            console.error('❌ Auth callback error:', error)
            onStatusChange('error')
            onMessageChange(error instanceof Error ? error.message : 'Authentication failed')
            toast.error('Authentication failed: ' + (error instanceof Error ? error.message : 'Unknown error'))
            setTimeout(() => onStateChange('landing'), 3000)
          }
        } else {
          console.log('ℹ️ No OAuth code found in URL')
        }
      } catch (error) {
        console.error('❌ Error in auth callback handler:', error)
        onStateChange('auth-error')
        onStatusChange('error')
        onMessageChange('An unexpected error occurred during authentication')
        toast.error('Authentication error')
        setTimeout(() => onStateChange('landing'), 3000)
      }
    }

    handleAuthCallback()
  }, [searchParams, handleGoogleCallback, onStateChange, onStatusChange, onMessageChange])

  return null
}

export default function Home() {
  const [appState, setAppState] = useState<AppState>('landing')
  const [authStatus, setAuthStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [authMessage, setAuthMessage] = useState('')

  const { user, isAuthenticated, getCurrentUser, isLoading, logout } = useAuthStore()

  // Check authentication status on load
  useEffect(() => {
    const checkAuth = async () => {
      if (isAuthenticated && !user) {
        try {
          await getCurrentUser()
        } catch (error) {
          console.error('Failed to get current user:', error)
          // If getCurrentUser fails, the user token might be invalid
          // Clear the authentication state
          await logout()
        }
      }
    }

    checkAuth()
  }, [isAuthenticated, user, getCurrentUser, logout])

  // Auto-switch to dashboard if authenticated (but only after successful validation)
  useEffect(() => {
    if (isAuthenticated && user && appState === 'landing') {
      // Validate authentication before switching to dashboard
      const validateAndSwitch = async () => {
        try {
          await getCurrentUser() // This will throw if tokens are invalid
          setAppState('dashboard')
        } catch (error) {
          console.log('Auth validation failed, staying on landing page')
          // The getCurrentUser error handler will clear auth state
        }
      }

      // Add a small delay to ensure the auth check is complete
      const timer = setTimeout(validateAndSwitch, 500)
      return () => clearTimeout(timer)
    }
  }, [isAuthenticated, user, appState, getCurrentUser])

  const handleOpenDashboard = () => {
    if (isAuthenticated && user) {
      setAppState('dashboard')
    }
  }

  const handleBackToLanding = () => {
    setAppState('landing')
  }

  // Render different states
  const renderAuthCallback = () => (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="max-w-md w-full mx-auto p-6">
        <div className="text-center space-y-6">
          {/* Logo */}
          <div className="flex items-center justify-center space-x-2 mb-8">
            <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-amber-600 to-amber-700">
              <span className="text-white text-xl font-bold">R</span>
            </div>
            <span className="text-2xl font-bold text-white">ReminderAPP</span>
          </div>

          {/* Status Icon */}
          <div className="flex justify-center">
            {authStatus === 'loading' && (
              <Loader2 className="w-12 h-12 text-amber-600 animate-spin" />
            )}
            {authStatus === 'success' && (
              <CheckCircle className="w-12 h-12 text-green-500" />
            )}
            {authStatus === 'error' && (
              <XCircle className="w-12 h-12 text-red-500" />
            )}
          </div>

          {/* Status Message */}
          <div className="space-y-2">
            <h1 className="text-xl font-semibold text-white">
              {authStatus === 'loading' && 'Authenticating...'}
              {authStatus === 'success' && 'Authentication Successful!'}
              {authStatus === 'error' && 'Authentication Failed'}
            </h1>
            <p className="text-gray-400 text-sm">
              {authMessage || 'Please wait while we complete your authentication.'}
            </p>
          </div>

          {/* Loading Progress */}
          {authStatus === 'loading' && (
            <div className="w-full bg-gray-800 rounded-full h-2">
              <div className="bg-gradient-to-r from-amber-600 to-amber-700 h-2 rounded-full animate-pulse"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  )

  const renderAuthError = () => (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="max-w-md w-full mx-auto p-6">
        <div className="text-center space-y-6">
          {/* Logo */}
          <div className="flex items-center justify-center space-x-2 mb-8">
            <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-amber-600 to-amber-700">
              <span className="text-white text-xl font-bold">R</span>
            </div>
            <span className="text-2xl font-bold text-white">ReminderAPP</span>
          </div>

          <XCircle className="w-16 h-16 text-red-500 mx-auto" />

          <div className="space-y-2">
            <h1 className="text-2xl font-semibold text-white">Authentication Failed</h1>
            <p className="text-gray-400">{authMessage}</p>
          </div>

          <button
            onClick={handleBackToLanding}
            className="mt-6 px-6 py-2 bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white font-medium rounded-lg transition-all duration-200"
          >
            Try Again
          </button>
        </div>
      </div>
    </div>
  )

  const renderLanding = () => (
    <div className="min-h-screen bg-black text-white">
      <AnnouncementBanner />
      <Navbar onOpenDashboard={handleOpenDashboard} />
      <Hero />
      <Features />
      <HowItWorks />
      <CTASection />
      <Footer />
    </div>
  )

  const renderDashboard = () => (
    <Dashboard onClose={handleBackToLanding} />
  )

  // Loading state
  if (isLoading && appState === 'landing') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-amber-600 animate-spin mx-auto mb-4" />
          <p className="text-gray-400">Loading ReminderAPP...</p>
        </div>
      </div>
    )
  }

  // Render based on current state
  return (
    <>
      {/* Handle OAuth callback with Suspense */}
      <Suspense fallback={null}>
        <AuthCallbackHandler
          onStateChange={setAppState}
          onStatusChange={setAuthStatus}
          onMessageChange={setAuthMessage}
        />
      </Suspense>

      {(() => {
        switch (appState) {
          case 'auth-callback':
            return renderAuthCallback()
          case 'auth-error':
            return renderAuthError()
          case 'dashboard':
            return renderDashboard()
          case 'landing':
          default:
            return renderLanding()
        }
      })()}
    </>
  )
}
