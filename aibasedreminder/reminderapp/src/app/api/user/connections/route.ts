import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireAuth } from '@/middleware/protected'
import { prisma } from '@/lib/prisma'

export const GET = requireAuth(async (request: NextRequest, user) => {
  try {
    const [googleTokens, telegramUsers] = await Promise.all([
      prisma.oAuthToken.findMany({
        where: { userId: user.id },
        select: {
          provider: true,
          expiresAt: true,
          scope: true,
          createdAt: true,
          updatedAt: true
        }
      }),
      prisma.telegramUser.findMany({
        where: { userId: user.id },
        select: {
          telegramId: true,
          username: true,
          firstName: true,
          lastName: true,
          isActive: true,
          createdAt: true
        }
      })
    ])

    const googleConnection = googleTokens.find(token => token.provider === 'google')
    const activeGoogleConnection = googleConnection && googleConnection.expiresAt > new Date()

    const activeTelegramConnection = telegramUsers.find(tg => tg.isActive)

    return successResponse({
      google: {
        connected: !!activeGoogleConnection,
        expiresAt: googleConnection?.expiresAt,
        scopes: googleConnection?.scope || [],
        connectedAt: googleConnection?.createdAt,
        lastUpdated: googleConnection?.updatedAt
      },
      telegram: {
        connected: !!activeTelegramConnection,
        username: activeTelegramConnection?.username,
        firstName: activeTelegramConnection?.firstName,
        lastName: activeTelegramConnection?.lastName,
        connectedAt: activeTelegramConnection?.createdAt
      }
    }, 'Connections retrieved successfully')

  } catch (error) {
    console.error('Get connections error:', error)
    return serverErrorResponse('Failed to get connections')
  }
})

export const DELETE = requireAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const provider = searchParams.get('provider')

    if (!provider) {
      return errorResponse('Provider parameter is required')
    }

    if (provider === 'google') {
      const deletedCount = await prisma.oAuthToken.deleteMany({
        where: {
          userId: user.id,
          provider: 'google'
        }
      })

      if (deletedCount.count === 0) {
        return errorResponse('Google account not connected', 404)
      }

      await Promise.all([
        prisma.calendarEvent.deleteMany({ where: { userId: user.id } }),
        prisma.email.deleteMany({ where: { userId: user.id } })
      ])

      return successResponse(
        { disconnected: true, provider: 'google' },
        'Google account disconnected successfully'
      )

    } else if (provider === 'telegram') {
      const updatedCount = await prisma.telegramUser.updateMany({
        where: {
          userId: user.id,
          isActive: true
        },
        data: {
          isActive: false
        }
      })

      if (updatedCount.count === 0) {
        return errorResponse('Telegram account not connected', 404)
      }

      return successResponse(
        { disconnected: true, provider: 'telegram' },
        'Telegram account disconnected successfully'
      )

    } else {
      return errorResponse('Invalid provider. Supported providers: google, telegram')
    }

  } catch (error) {
    console.error('Disconnect account error:', error)
    return serverErrorResponse('Failed to disconnect account')
  }
})
