import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { validateRequest } from '@/utils/validation'
import { requireAuth } from '@/middleware/protected'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  avatar: z.string().url('Invalid avatar URL').optional()
})

export const PUT = requireAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const validation = validateRequest(updateProfileSchema, body)
    
    if (!validation.success) {
      return errorResponse(validation.error)
    }

    const { name, avatar } = validation.data

    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        ...(name !== undefined && { name }),
        ...(avatar !== undefined && { avatar }),
        updatedAt: new Date()
      }
    })

    return successResponse({
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        avatar: updatedUser.avatar,
        isPro: updatedUser.isPro,
        updatedAt: updatedUser.updatedAt
      }
    }, 'Profile updated successfully')

  } catch (error) {
    console.error('Update profile error:', error)
    return serverErrorResponse('Failed to update profile')
  }
})

export const DELETE = requireAuth(async (request: NextRequest, user) => {
  try {
    await prisma.$transaction(async (tx) => {
      await tx.reminder.deleteMany({ where: { userId: user.id } })
      await tx.calendarEvent.deleteMany({ where: { userId: user.id } })
      await tx.email.deleteMany({ where: { userId: user.id } })
      await tx.telegramUser.deleteMany({ where: { userId: user.id } })
      await tx.oAuthToken.deleteMany({ where: { userId: user.id } })
      
      await tx.user.delete({ where: { id: user.id } })
    })

    return successResponse(
      { deleted: true },
      'Account deleted successfully'
    )

  } catch (error) {
    console.error('Delete account error:', error)
    return serverErrorResponse('Failed to delete account')
  }
})
