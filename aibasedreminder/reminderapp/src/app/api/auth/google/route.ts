import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { getAuthUrl } from '@/lib/google'
import { authRateLimit } from '@/middleware/rateLimit'

const REQUIRED_SCOPES = [
  'https://www.googleapis.com/auth/userinfo.email',
  'https://www.googleapis.com/auth/userinfo.profile',
  'https://www.googleapis.com/auth/calendar',
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/gmail.send',
  'https://www.googleapis.com/auth/gmail.modify'
]

export async function GET(request: NextRequest) {
  try {
    const rateLimitResult = authRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Too many authentication attempts. Please try again later.', 429)
    }

    if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
      return errorResponse('Google OAuth is not configured', 500)
    }

    const authUrl = getAuthUrl(REQUIRED_SCOPES)

    return successResponse({
      authUrl,
      scopes: REQUIRED_SCOPES
    }, 'Google OAuth URL generated successfully')

  } catch (error) {
    console.error('Google OAuth URL generation error:', error)
    return serverErrorResponse('Failed to generate Google OAuth URL')
  }
}
