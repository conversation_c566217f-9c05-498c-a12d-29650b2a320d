import { NextRequest, NextResponse } from 'next/server'
import { getAuthUrl } from '@/lib/google'

export async function GET(request: NextRequest) {
  try {
    const scopes = [
      'openid',
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/gmail.readonly',
      'https://www.googleapis.com/auth/gmail.modify',
      'https://www.googleapis.com/auth/gmail.send'
    ]

    const authUrl = getAuthUrl(scopes)

    // Get the current host from the request
    const host = request.headers.get('host') || 'localhost:3001'
    const protocol = request.headers.get('x-forwarded-proto') || 'http'
    const currentUrl = `${protocol}://${host}`

    // Replace the redirect URI in the auth URL to match current port
    const updatedAuthUrl = authUrl.replace(
      'http%3A%2F%2Flocalhost%3A3001%2Fapi%2Fauth%2Fgoogle%2Fcallback',
      encodeURIComponent(`${currentUrl}/api/auth/google/callback`)
    )

    return NextResponse.json({
      success: true,
      authUrl: updatedAuthUrl,
      currentUrl,
      message: 'Please visit this URL to re-authenticate with Google',
      instructions: 'After clicking the URL, you will be redirected back to complete the authentication process.'
    })

  } catch (error) {
    console.error('Error generating re-auth URL:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to generate authentication URL' },
      { status: 500 }
    )
  }
}
