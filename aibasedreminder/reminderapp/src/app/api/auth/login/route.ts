import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { validateRequest, userLoginSchema } from '@/utils/validation'
import { authenticateUser, generateToken } from '@/lib/auth'
import { authRateLimit } from '@/middleware/rateLimit'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const rateLimitResult = authRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Too many login attempts. Please try again later.', 429)
    }

    const body = await request.json()
    const validation = validateRequest(userLoginSchema, body)
    
    if (!validation.success) {
      return errorResponse(validation.error)
    }

    const { email, password } = validation.data

    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    if (!user) {
      return errorResponse('User not found. Please register first or use Google OAuth.', 404)
    }

    if (!password && user.oauthTokens.length > 0) {
      const token = generateToken(user)
      
      return successResponse({
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          isPro: user.isPro
        },
        token,
        googleConnected: user.oauthTokens.length > 0
      }, 'Login successful')
    }

    if (password) {
      const authenticatedUser = await authenticateUser(email, password)
      if (!authenticatedUser) {
        return errorResponse('Invalid credentials', 401)
      }

      const token = generateToken(authenticatedUser)
      
      return successResponse({
        user: {
          id: authenticatedUser.id,
          email: authenticatedUser.email,
          name: authenticatedUser.name,
          avatar: authenticatedUser.avatar,
          isPro: authenticatedUser.isPro
        },
        token,
        googleConnected: user.oauthTokens.length > 0
      }, 'Login successful')
    }

    return errorResponse('Password required for non-OAuth users', 400)

  } catch (error) {
    console.error('Login error:', error)
    return serverErrorResponse('Login failed')
  }
}
