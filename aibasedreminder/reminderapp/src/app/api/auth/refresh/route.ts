import { NextRequest } from 'next/server'
import { successResponse, errorResponse, unauthorizedResponse, serverErrorResponse } from '@/utils/response'
import { authenticateRequest } from '@/middleware/auth'
import { generateToken } from '@/lib/auth'
import { getAuthenticatedClient } from '@/lib/google'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request)
    if (!user) {
      return unauthorizedResponse()
    }

    const oauthToken = await prisma.oAuthToken.findFirst({
      where: {
        userId: user.id,
        provider: 'google'
      }
    })

    if (!oauthToken) {
      return errorResponse('No Google OAuth token found. Please reconnect your Google account.', 404)
    }

    try {
      await getAuthenticatedClient(user.id)
      
      const updatedToken = await prisma.oAuthToken.findFirst({
        where: {
          userId: user.id,
          provider: 'google'
        }
      })

      const newJwtToken = generateToken(user)

      return successResponse({
        token: newJwtToken,
        googleTokenValid: updatedToken && updatedToken.expiresAt > new Date(),
        tokenExpiresAt: updatedToken?.expiresAt
      }, 'Tokens refreshed successfully')

    } catch (error) {
      console.error('Token refresh error:', error)
      
      
      await prisma.oAuthToken.delete({
        where: { id: oauthToken.id }
      })

      return errorResponse('Google token refresh failed. Please reconnect your Google account.', 401)
    }

  } catch (error) {
    console.error('Refresh token error:', error)
    return serverErrorResponse('Token refresh failed')
  }
}
