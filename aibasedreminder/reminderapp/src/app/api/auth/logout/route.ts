import { NextRequest } from 'next/server'
import { successResponse, unauthorizedResponse, serverErrorResponse } from '@/utils/response'
import { authenticateRequest } from '@/middleware/auth'

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request)
    if (!user) {
      return unauthorizedResponse()
    }

   
    
    console.log(`User ${user.email} logged out at ${new Date().toISOString()}`)

    return successResponse(
      { loggedOut: true },
      'Logout successful'
    )

  } catch (error) {
    console.error('Logout error:', error)
    return serverErrorResponse('Logout failed')
  }
}
