import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { validateRequest, userRegistrationSchema } from '@/utils/validation'
import { createOrUpdateUser, generateToken } from '@/lib/auth'
import { authRateLimit } from '@/middleware/rateLimit'

export async function POST(request: NextRequest) {
  try {
    const rateLimitResult = authRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Too many registration attempts. Please try again later.', 429)
    }

    const body = await request.json()
    const validation = validateRequest(userRegistrationSchema, body)
    
    if (!validation.success) {
      return errorResponse(validation.error)
    }

    const { email, name } = validation.data

    const user = await createOrUpdateUser({
      email,
      name
    })

    const token = generateToken(user)

    return successResponse({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        isPro: user.isPro
      },
      token
    }, 'User registered successfully')

  } catch (error) {
    console.error('Registration error:', error)
    return serverErrorResponse('Registration failed')
  }
}
