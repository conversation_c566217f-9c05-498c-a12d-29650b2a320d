import { NextRequest } from 'next/server'
import { successResponse, unauthorizedResponse, serverErrorResponse } from '@/utils/response'
import { authenticateRequest } from '@/middleware/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request)
    if (!user) {
      return unauthorizedResponse()
    }

    const userWithDetails = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        oauthTokens: {
          select: {
            provider: true,
            expiresAt: true,
            scope: true,
            createdAt: true
          }
        },
        telegramUsers: {
          where: { isActive: true },
          select: {
            username: true,
            firstName: true,
            createdAt: true
          }
        },
        _count: {
          select: {
            reminders: true,
            calendarEvents: true,
            emails: true
          }
        }
      }
    })

    if (!userWithDetails) {
      return unauthorizedResponse()
    }

    const googleToken = userWithDetails.oauthTokens.find(token => token.provider === 'google')
    const googleConnected = googleToken && googleToken.expiresAt > new Date()

    return successResponse({
      user: {
        id: userWithDetails.id,
        email: userWithDetails.email,
        name: userWithDetails.name,
        avatar: userWithDetails.avatar,
        isPro: userWithDetails.isPro,
        createdAt: userWithDetails.createdAt,
        updatedAt: userWithDetails.updatedAt
      },
      connections: {
        google: {
          connected: !!googleConnected,
          scopes: googleToken?.scope || [],
          connectedAt: googleToken?.createdAt
        },
        telegram: {
          connected: userWithDetails.telegramUsers.length > 0,
          username: userWithDetails.telegramUsers[0]?.username,
          connectedAt: userWithDetails.telegramUsers[0]?.createdAt
        }
      },
      stats: {
        totalReminders: userWithDetails._count.reminders,
        totalEvents: userWithDetails._count.calendarEvents,
        totalEmails: userWithDetails._count.emails
      }
    }, 'User profile retrieved successfully')

  } catch (error) {
    console.error('Get user profile error:', error)
    return serverErrorResponse('Failed to get user profile')
  }
}
