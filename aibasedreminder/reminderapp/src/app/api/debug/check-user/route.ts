import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    console.log('🔍 DEBUG: Checking connected users and their email addresses')

    // Get all users with Telegram connections using raw SQL
    const users = await prisma.$queryRaw`
      SELECT 
        u.id, u.email, u.name,
        tu."telegramId", tu."isActive" as telegram_active,
        ot."accessToken" IS NOT NULL as has_google_token,
        ot."expiresAt" > NOW() as token_valid
      FROM users u
      LEFT JOIN "TelegramUser" tu ON u.id = tu."userId"
      LEFT JOIN "OAuthToken" ot ON u.id = ot."userId" AND ot.provider = 'google'
      ORDER BY u."createdAt" DESC
    ` as any[]

    console.log(`👥 Found ${users.length} users in database`)

    // Also check for any unread emails
    const unreadEmails = await prisma.$queryRaw`
      SELECT 
        e.id, e.subject, e."from", e."receivedAt",
        u.email as user_email
      FROM emails e
      JOIN users u ON e."userId" = u.id
      WHERE e."isRead" = false
      ORDER BY e."receivedAt" DESC
      LIMIT 10
    ` as any[]

    console.log(`📧 Found ${unreadEmails.length} unread emails`)

    return successResponse({
      users,
      unreadEmails,
      timestamp: new Date().toISOString()
    }, `Found ${users.length} users and ${unreadEmails.length} unread emails`)

  } catch (error) {
    console.error('❌ Debug check failed:', error)
    return serverErrorResponse(`Debug check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
