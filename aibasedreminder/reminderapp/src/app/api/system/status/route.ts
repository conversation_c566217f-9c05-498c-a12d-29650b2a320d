import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { checkOpenAIConnection } from '@/lib/openai'
import { notificationScheduler } from '@/lib/notificationScheduler'
import { telegramBotManager } from '@/lib/telegram-bot-manager'

export async function GET(request: NextRequest) {
  try {
    const [
      databaseStatus,
      telegramStatus,
      openaiStatus,
      schedulerStatus
    ] = await Promise.all([
      checkDatabaseConnection(),
      checkTelegramStatus(),
      checkOpenAIConnection(),
      checkSchedulerStatus()
    ])

    const stats = await getSystemStatistics()

    const systemStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      services: {
        database: databaseStatus,
        telegram: telegramStatus,
        openai: openaiStatus,
        scheduler: schedulerStatus,
        google_oauth: process.env.GOOGLE_CLIENT_ID ? 'configured' : 'not_configured'
      },
      statistics: stats,
      health: {
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      }
    }

    const unhealthyServices = Object.values(systemStatus.services).filter(
      status => status === 'unhealthy' || status === 'error'
    )

    if (unhealthyServices.length > 0) {
      systemStatus.status = 'degraded'
    }

    return successResponse(systemStatus, 'System status retrieved successfully')

  } catch (error) {
    console.error('System status check error:', error)
    return serverErrorResponse('Failed to check system status')
  }
}

async function checkDatabaseConnection(): Promise<string> {
  try {
    await prisma.$queryRaw`SELECT 1`
    return 'healthy'
  } catch (error) {
    console.error('Database connection error:', error)
    return 'unhealthy'
  }
}

async function checkTelegramStatus(): Promise<string> {
  try {
    return telegramBotManager.isPollingActive() ? 'healthy' : 'stopped'
  } catch (error) {
    console.error('Telegram status error:', error)
    return 'not_initialized'
  }
}

async function checkSchedulerStatus(): Promise<string> {
  try {
    const status = notificationScheduler.getStatus()
    return status.isRunning ? 'healthy' : 'stopped'
  } catch (error) {
    console.error('Scheduler status error:', error)
    return 'error'
  }
}

async function getSystemStatistics(): Promise<any> {
  try {
    const [
      totalUsers,
      activeUsers,
      totalReminders,
      pendingReminders,
      totalEmails,
      totalEvents,
      telegramUsers
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({
        where: {
          updatedAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) 
          }
        }
      }),
      prisma.reminder.count(),
      prisma.reminder.count({ where: { status: 'PENDING' } }),
      prisma.email.count(),
      prisma.calendarEvent.count(),
      prisma.telegramUser.count({ where: { isActive: true } })
    ])

    return {
      users: {
        total: totalUsers,
        active: activeUsers,
        telegramConnected: telegramUsers
      },
      reminders: {
        total: totalReminders,
        pending: pendingReminders
      },
      content: {
        emails: totalEmails,
        events: totalEvents
      }
    }
  } catch (error) {
    console.error('Error getting system statistics:', error)
    return {
      users: { total: 0, active: 0, telegramConnected: 0 },
      reminders: { total: 0, pending: 0 },
      content: { emails: 0, events: 0 }
    }
  }
}
