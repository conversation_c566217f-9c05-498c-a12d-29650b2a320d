import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { notificationScheduler } from '@/lib/notificationScheduler'

export async function GET(request: NextRequest) {
  try {
    const status = notificationScheduler.getStatus()
    return successResponse({
      isRunning: status.isRunning,
      uptime: status.uptime
    }, 'Scheduler status retrieved successfully')
  } catch (error) {
    console.error('Error getting scheduler status:', error)
    return serverErrorResponse('Failed to get scheduler status')
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}))
    const { action } = body

    if (action === 'start') {
      notificationScheduler.start()
      console.log('✅ Notification scheduler started via API')
      return successResponse({ 
        message: 'Scheduler started successfully',
        status: notificationScheduler.getStatus()
      }, 'Scheduler started')
    } else if (action === 'stop') {
      notificationScheduler.stop()
      console.log('⏹️ Notification scheduler stopped via API')
      return successResponse({ 
        message: 'Scheduler stopped successfully',
        status: notificationScheduler.getStatus()
      }, 'Scheduler stopped')
    } else if (action === 'restart') {
      notificationScheduler.stop()
      setTimeout(() => {
        notificationScheduler.start()
        console.log('🔄 Notification scheduler restarted via API')
      }, 1000)
      return successResponse({ 
        message: 'Scheduler restarted successfully'
      }, 'Scheduler restarted')
    } else {
      return errorResponse('Invalid action. Use "start", "stop", or "restart"', 400)
    }
  } catch (error) {
    console.error('Error controlling scheduler:', error)
    return serverErrorResponse('Failed to control scheduler')
  }
}
