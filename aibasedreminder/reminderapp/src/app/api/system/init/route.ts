import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 Initializing AI Reminder App systems...')
    
    // Set Telegram webhook
    const webhookResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/setWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: `${process.env.NEXTAUTH_URL}/api/telegram/webhook`,
        allowed_updates: ['message', 'callback_query']
      })
    })
    
    const webhookResult = await webhookResponse.json()
    console.log('📱 Telegram webhook result:', webhookResult)
    
    // Trigger initial email check
    const emailCheckResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/cron/check-emails`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })
    
    const emailCheckResult = await emailCheckResponse.json()
    console.log('📧 Initial email check result:', emailCheckResult)
    
    return successResponse({
      telegram: webhookResult,
      emailCheck: emailCheckResult,
      timestamp: new Date().toISOString()
    }, 'System initialization completed')
    
  } catch (error) {
    console.error('❌ System initialization failed:', error)
    return serverErrorResponse(`System initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
