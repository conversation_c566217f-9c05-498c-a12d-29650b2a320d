import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireAuth } from '@/middleware/protected'
import { prisma } from '@/lib/prisma'
import { notificationScheduler } from '@/lib/notificationScheduler'
import { sendMessage } from '@/lib/telegram'
import { getGoogleServices } from '@/lib/google'
import { generateEmailReply } from '@/lib/openai'

export const POST = requireAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const { testType, testData = {} } = body

    if (!testType) {
      return errorResponse('Test type is required')
    }

    let testResult = null

    switch (testType) {
      case 'telegram':
        testResult = await testTelegramIntegration(user.id, testData)
        break
      case 'google-calendar':
        testResult = await testGoogleCalendarIntegration(user.id, testData)
        break
      case 'google-gmail':
        testResult = await testGmailIntegration(user.id, testData)
        break
      case 'openai':
        testResult = await testOpenAIIntegration(user.id, testData)
        break
      case 'notifications':
        testResult = await testNotificationSystem(user.id, testData)
        break
      case 'database':
        testResult = await testDatabaseOperations(user.id, testData)
        break
      case 'full-system':
        testResult = await runFullSystemTest(user.id, testData)
        break
      default:
        return errorResponse('Invalid test type')
    }

    return successResponse({
      testType,
      result: testResult,
      timestamp: new Date().toISOString()
    }, `${testType} test completed`)

  } catch (error) {
    console.error('System test error:', error)
    return serverErrorResponse('System test failed')
  }
})

async function testTelegramIntegration(userId: string, testData: any): Promise<any> {
  try {
    const telegramUser = await prisma.telegramUser.findFirst({
      where: { userId, isActive: true }
    })

    if (!telegramUser) {
      return {
        success: false,
        error: 'No active Telegram connection found',
        steps: ['telegram_connection_check']
      }
    }

    const messageId = await sendMessage(
      telegramUser.telegramId,
      '🧪 <b>Test Message</b>\n\nThis is a test message from AI Reminder system.\n\n✅ Telegram integration is working correctly!',
      { parseMode: 'HTML' }
    )

    const notificationSent = await notificationScheduler.sendTestNotification(userId)

    return {
      success: true,
      details: {
        telegramConnected: true,
        testMessageSent: !!messageId,
        notificationSystemWorking: notificationSent,
        telegramId: telegramUser.telegramId,
        username: telegramUser.username
      },
      steps: ['telegram_connection_check', 'test_message_send', 'notification_test']
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      steps: ['telegram_connection_check']
    }
  }
}

async function testGoogleCalendarIntegration(userId: string, testData: any): Promise<any> {
  try {
    const oauthToken = await prisma.oAuthToken.findFirst({
      where: {
        userId,
        provider: 'google',
        expiresAt: { gt: new Date() }
      }
    })

    if (!oauthToken) {
      return {
        success: false,
        error: 'No valid Google OAuth token found',
        steps: ['oauth_token_check']
      }
    }

    const { calendar } = await getGoogleServices(userId)
    const events = await calendar.getEvents(new Date(), new Date(Date.now() + 7 * 24 * 60 * 60 * 1000))

    const dbEvents = await prisma.calendarEvent.count({ where: { userId } })

    return {
      success: true,
      details: {
        oauthTokenValid: true,
        calendarApiWorking: true,
        eventsRetrieved: events.length,
        databaseEvents: dbEvents,
        tokenExpiresAt: oauthToken.expiresAt
      },
      steps: ['oauth_token_check', 'calendar_api_test', 'database_sync_check']
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      steps: ['oauth_token_check', 'calendar_api_test']
    }
  }
}

async function testGmailIntegration(userId: string, testData: any): Promise<any> {
  try {
    const oauthToken = await prisma.oAuthToken.findFirst({
      where: {
        userId,
        provider: 'google',
        expiresAt: { gt: new Date() }
      }
    })

    if (!oauthToken) {
      return {
        success: false,
        error: 'No valid Google OAuth token found',
        steps: ['oauth_token_check']
      }
    }

    const { gmail } = await getGoogleServices(userId)
    const messages = await gmail.getMessages('is:unread', 10)

    const dbEmails = await prisma.email.count({ where: { userId } })

    return {
      success: true,
      details: {
        oauthTokenValid: true,
        gmailApiWorking: true,
        messagesRetrieved: messages.length,
        databaseEmails: dbEmails,
        tokenExpiresAt: oauthToken.expiresAt
      },
      steps: ['oauth_token_check', 'gmail_api_test', 'database_sync_check']
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      steps: ['oauth_token_check', 'gmail_api_test']
    }
  }
}

async function testOpenAIIntegration(userId: string, testData: any): Promise<any> {
  try {
    const user = await prisma.user.findUnique({ where: { id: userId } })
    
    if (!user?.isPro) {
      return {
        success: false,
        error: 'Pro subscription required for AI features',
        steps: ['pro_subscription_check']
      }
    }

    const testEmailData = {
      originalEmail: {
        subject: 'Test Email',
        from: '<EMAIL>',
        body: 'This is a test email for AI reply generation.',
        receivedAt: new Date().toISOString()
      },
      tone: 'professional' as const,
      length: 'medium' as const
    }

    const aiReply = await generateEmailReply(testEmailData)

    return {
      success: true,
      details: {
        proSubscription: true,
        aiReplyGenerated: true,
        replyConfidence: aiReply.confidence,
        replyLength: aiReply.body.length,
        openaiApiWorking: true
      },
      steps: ['pro_subscription_check', 'openai_api_test', 'ai_reply_generation']
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      steps: ['pro_subscription_check', 'openai_api_test']
    }
  }
}

async function testNotificationSystem(userId: string, testData: any): Promise<any> {
  try {
    const schedulerStatus = notificationScheduler.getStatus()

    const testReminder = await prisma.reminder.create({
      data: {
        userId,
        type: 'CUSTOM',
        title: 'Test Reminder',
        description: 'This is a test reminder for system testing',
        scheduledFor: new Date(Date.now() + 60 * 1000), 
        status: 'PENDING'
      }
    })

    const notificationSent = await notificationScheduler.sendTestNotification(userId)

    await prisma.reminder.delete({ where: { id: testReminder.id } })

    return {
      success: true,
      details: {
        schedulerRunning: schedulerStatus.isRunning,
        testReminderCreated: true,
        notificationSent,
        testReminderCleaned: true
      },
      steps: ['scheduler_status_check', 'test_reminder_creation', 'notification_send', 'cleanup']
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      steps: ['scheduler_status_check', 'test_reminder_creation']
    }
  }
}

async function testDatabaseOperations(userId: string, testData: any): Promise<any> {
  try {
    const testData_obj = {
      userId,
      type: 'CUSTOM' as const,
      title: 'Database Test Reminder',
      description: 'Testing database operations',
      scheduledFor: new Date(),
      status: 'PENDING' as const
    }

    const created = await prisma.reminder.create({ data: testData_obj })

    const read = await prisma.reminder.findUnique({ where: { id: created.id } })

    const updated = await prisma.reminder.update({
      where: { id: created.id },
      data: { title: 'Updated Test Reminder' }
    })

    await prisma.reminder.delete({ where: { id: created.id } })

    await prisma.$queryRaw`SELECT 1`

    return {
      success: true,
      details: {
        createOperation: !!created,
        readOperation: !!read,
        updateOperation: updated.title === 'Updated Test Reminder',
        deleteOperation: true,
        connectionTest: true
      },
      steps: ['create_test', 'read_test', 'update_test', 'delete_test', 'connection_test']
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      steps: ['database_operations_test']
    }
  }
}

async function runFullSystemTest(userId: string, testData: any): Promise<any> {
  try {
    const results = {
      database: await testDatabaseOperations(userId, testData),
      telegram: await testTelegramIntegration(userId, testData),
      googleCalendar: await testGoogleCalendarIntegration(userId, testData),
      gmail: await testGmailIntegration(userId, testData),
      notifications: await testNotificationSystem(userId, testData)
    }

    const user = await prisma.user.findUnique({ where: { id: userId } })
    if (user?.isPro) {
      results.openai = await testOpenAIIntegration(userId, testData)
    }

    const successCount = Object.values(results).filter(r => r.success).length
    const totalTests = Object.keys(results).length

    return {
      success: successCount === totalTests,
      summary: {
        totalTests,
        passed: successCount,
        failed: totalTests - successCount,
        successRate: (successCount / totalTests) * 100
      },
      results,
      steps: ['full_system_integration_test']
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      steps: ['full_system_integration_test']
    }
  }
}
