import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 Triggering continuous email monitoring...')
    
    // Trigger the continuous monitoring endpoint
    const monitoringUrl = `${process.env.NEXTAUTH_URL}/api/cron/continuous-monitor`
    
    const response = await fetch(monitoringUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (!response.ok) {
      throw new Error(`Monitoring trigger failed: ${response.status}`)
    }
    
    const result = await response.json()
    
    console.log('✅ Continuous monitoring triggered successfully:', result)
    
    return successResponse({
      triggered: true,
      result: result.data,
      timestamp: new Date().toISOString()
    }, 'Continuous monitoring triggered successfully')
    
  } catch (error) {
    console.error('❌ Failed to trigger continuous monitoring:', error)
    return serverErrorResponse(`Failed to trigger monitoring: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Also allow POST for webhook triggers
export const POST = GET
