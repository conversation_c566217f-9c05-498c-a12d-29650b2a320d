import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requirePro } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { getOpenAIClient } from '@/lib/openai'
import { prisma } from '@/lib/prisma'
import { addDays, addHours, format, isWithinInterval, parseISO } from 'date-fns'

export const POST = requirePro(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const body = await request.json()
    const { 
      schedulingRequest,
      preferences = {},
      constraints = {},
      analysisDepth = 'standard' 
    } = body

    if (!schedulingRequest) {
      return errorResponse('Scheduling request is required')
    }

    const startDate = new Date()
    const endDate = addDays(startDate, 30)

    const [existingEvents, userPreferences, historicalPatterns] = await Promise.all([
      prisma.calendarEvent.findMany({
        where: {
          userId: user.id,
          startTime: { gte: startDate, lte: endDate }
        },
        orderBy: { startTime: 'asc' }
      }),
      getUserSchedulingPreferences(user.id),
      getHistoricalSchedulingPatterns(user.id)
    ])

    const schedulingSuggestions = await generateIntelligentScheduling({
      request: schedulingRequest,
      existingEvents,
      userPreferences: { ...userPreferences, ...preferences },
      constraints,
      historicalPatterns,
      analysisDepth
    })

    const analysisRecord = await prisma.reminder.create({
      data: {
        userId: user.id,
        type: 'CUSTOM',
        title: `AI Scheduling: ${schedulingRequest.title || 'Meeting Request'}`,
        description: `Intelligent scheduling analysis completed`,
        scheduledFor: new Date(),
        status: 'COMPLETED'
      }
    })

    return successResponse({
      suggestions: schedulingSuggestions,
      analysis: {
        depth: analysisDepth,
        confidence: schedulingSuggestions.confidence,
        reasoning: schedulingSuggestions.reasoning,
        alternatives: schedulingSuggestions.alternatives
      },
      context: {
        existingEventsCount: existingEvents.length,
        timeRange: { start: startDate, end: endDate },
        userPreferences,
        historicalPatterns
      },
      analysisId: analysisRecord.id
    }, 'Intelligent scheduling suggestions generated successfully')

  } catch (error) {
    console.error('Intelligent scheduling error:', error)
    return serverErrorResponse('Failed to generate scheduling suggestions')
  }
})

export const GET = requirePro(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')
    const duration = parseInt(searchParams.get('duration') || '60') // minutes
    const includeWeekends = searchParams.get('weekends') === 'true'

    const startDate = new Date()
    const endDate = addDays(startDate, days)

    const existingEvents = await prisma.calendarEvent.findMany({
      where: {
        userId: user.id,
        startTime: { gte: startDate, lte: endDate }
      },
      orderBy: { startTime: 'asc' }
    })

    const optimalSlots = findOptimalTimeSlots(
      existingEvents,
      startDate,
      endDate,
      duration,
      includeWeekends
    )

    return successResponse({
      optimalSlots,
      parameters: { days, duration, includeWeekends },
      existingEventsCount: existingEvents.length
    }, 'Optimal time slots retrieved successfully')

  } catch (error) {
    console.error('Get optimal slots error:', error)
    return serverErrorResponse('Failed to get optimal time slots')
  }
})


async function getUserSchedulingPreferences(userId: string): Promise<any> {
  return {
    workingHours: { start: '09:00', end: '17:00' },
    preferredMeetingLength: 60,
    bufferTime: 15,
    maxMeetingsPerDay: 6,
    preferredDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
    avoidTimeSlots: ['12:00-13:00'], 
    timezone: 'UTC'
  }
}

async function getHistoricalSchedulingPatterns(userId: string): Promise<any> {
  const pastEvents = await prisma.calendarEvent.findMany({
    where: {
      userId,
      startTime: { gte: addDays(new Date(), -90) } 
    }
  })

  return analyzeHistoricalPatterns(pastEvents)
}

function analyzeHistoricalPatterns(events: any[]): any {
  const hourCounts = new Array(24).fill(0)
  const dayCounts = new Array(7).fill(0)
  const durationCounts = { short: 0, medium: 0, long: 0 }

  events.forEach(event => {
    const hour = new Date(event.startTime).getHours()
    const day = new Date(event.startTime).getDay()
    const duration = (new Date(event.endTime).getTime() - new Date(event.startTime).getTime()) / (1000 * 60)

    hourCounts[hour]++
    dayCounts[day]++

    if (duration <= 30) durationCounts.short++
    else if (duration <= 90) durationCounts.medium++
    else durationCounts.long++
  })

  const preferredHour = hourCounts.indexOf(Math.max(...hourCounts))
  const preferredDay = dayCounts.indexOf(Math.max(...dayCounts))

  return {
    preferredStartHour: preferredHour,
    preferredDay,
    averageMeetingDuration: events.length > 0 ? 
      events.reduce((sum, e) => sum + ((new Date(e.endTime).getTime() - new Date(e.startTime).getTime()) / (1000 * 60)), 0) / events.length : 60,
    meetingFrequency: events.length / 90, 
    durationPreferences: durationCounts
  }
}

async function generateIntelligentScheduling(params: any): Promise<any> {
  try {
    const openai = getOpenAIClient()

    const systemPrompt = `You are an AI scheduling assistant that helps optimize meeting scheduling based on user preferences, existing calendar events, and historical patterns.

    Consider:
    - User's working hours and preferences
    - Existing calendar conflicts
    - Optimal meeting timing based on historical data
    - Buffer time between meetings
    - Meeting duration and type
    - Attendee availability (if provided)
    - Time zone considerations
    - Energy levels throughout the day`

    const userPrompt = `Please suggest optimal scheduling for this request:

    Meeting Request: ${JSON.stringify(params.request)}
    
    User Preferences: ${JSON.stringify(params.userPreferences)}
    
    Constraints: ${JSON.stringify(params.constraints)}
    
    Historical Patterns: ${JSON.stringify(params.historicalPatterns)}
    
    Existing Events: ${params.existingEvents.length} events in the next 30 days
    
    Provide 3-5 optimal time slot suggestions with reasoning for each.`

    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
      max_tokens: 1500
    })

    const response = completion.choices[0]?.message?.content
    if (!response) {
      throw new Error('No scheduling suggestions generated')
    }

    const timeSlots = generateTimeSlots(params)
    
    return {
      timeSlots,
      confidence: 88,
      reasoning: response,
      alternatives: generateAlternativeSlots(params),
      aiAnalysis: response
    }

  } catch (error) {
    console.error('AI scheduling generation error:', error)
    throw error
  }
}

function generateTimeSlots(params: any): any[] {
  const { request, existingEvents, userPreferences } = params
  const duration = request.duration || userPreferences.preferredMeetingLength || 60
  const startDate = new Date()
  const endDate = addDays(startDate, 14)

  const slots = []
  let currentDate = new Date(startDate)

  while (currentDate <= endDate && slots.length < 5) {
    const dayOfWeek = currentDate.getDay()
    
    
    if ((dayOfWeek === 0 || dayOfWeek === 6) && !userPreferences.includeWeekends) {
      currentDate = addDays(currentDate, 1)
      continue
    }

    
    const workStart = parseInt(userPreferences.workingHours.start.split(':')[0])
    const workEnd = parseInt(userPreferences.workingHours.end.split(':')[0])

    for (let hour = workStart; hour < workEnd; hour++) {
      const slotStart = new Date(currentDate)
      slotStart.setHours(hour, 0, 0, 0)
      const slotEnd = new Date(slotStart.getTime() + duration * 60 * 1000)

     
      const hasConflict = existingEvents.some(event => {
        const eventStart = new Date(event.startTime)
        const eventEnd = new Date(event.endTime)
        return isWithinInterval(slotStart, { start: eventStart, end: eventEnd }) ||
               isWithinInterval(slotEnd, { start: eventStart, end: eventEnd })
      })

      if (!hasConflict) {
        slots.push({
          start: slotStart,
          end: slotEnd,
          score: calculateSlotScore(slotStart, params),
          reasoning: `Optimal time based on your preferences and availability`
        })
      }
    }

    currentDate = addDays(currentDate, 1)
  }

 
  return slots.sort((a, b) => b.score - a.score).slice(0, 5)
}

function calculateSlotScore(slotStart: Date, params: any): number {
  let score = 50 

  const hour = slotStart.getHours()
  const { historicalPatterns, userPreferences } = params

  
  if (hour === historicalPatterns.preferredStartHour) score += 20

  
  if (hour >= 10 && hour <= 15) score += 15

  
  if (hour === 12) score -= 10

 
  const day = slotStart.getDay()
  if (day >= 2 && day <= 4) score += 10

  return score
}

function generateAlternativeSlots(params: any): any[] {
 
  return [
    {
      type: 'earlier',
      description: 'Earlier in the day for better focus',
      timeAdjustment: -2 
    },
    {
      type: 'later',
      description: 'Later in the day for more preparation time',
      timeAdjustment: 2 
    },
    {
      type: 'shorter',
      description: 'Shorter meeting duration',
      durationAdjustment: -15
    }
  ]
}

function findOptimalTimeSlots(
  existingEvents: any[],
  startDate: Date,
  endDate: Date,
  duration: number,
  includeWeekends: boolean
): any[] {
  const slots = []
  let currentDate = new Date(startDate)

  while (currentDate <= endDate) {
    const dayOfWeek = currentDate.getDay()
    
    
    if ((dayOfWeek === 0 || dayOfWeek === 6) && !includeWeekends) {
      currentDate = addDays(currentDate, 1)
      continue
    }

    
    for (let hour = 9; hour < 17; hour++) {
      const slotStart = new Date(currentDate)
      slotStart.setHours(hour, 0, 0, 0)
      const slotEnd = new Date(slotStart.getTime() + duration * 60 * 1000)

    
      const hasConflict = existingEvents.some(event => {
        const eventStart = new Date(event.startTime)
        const eventEnd = new Date(event.endTime)
        return isWithinInterval(slotStart, { start: eventStart, end: eventEnd }) ||
               isWithinInterval(slotEnd, { start: eventStart, end: eventEnd })
      })

      if (!hasConflict) {
        slots.push({
          start: slotStart,
          end: slotEnd,
          duration,
          available: true
        })
      }
    }

    currentDate = addDays(currentDate, 1)
  }

  return slots.slice(0, 20) 
}
