import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requirePro } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { getOpenAIClient } from '@/lib/openai'
import { prisma } from '@/lib/prisma'

export const POST = requirePro(async (request: NextRequest, user) => {
  try {
   
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const body = await request.json()
    const { contentType, contentId, analysisType = 'comprehensive' } = body

    if (!contentType || !contentId) {
      return errorResponse('Content type and ID are required')
    }

    let content = null
    let analysisResult = null

    if (contentType === 'email') {
      content = await prisma.email.findFirst({
        where: { id: contentId, userId: user.id }
      })
      
      if (!content) {
        return errorResponse('Email not found', 404)
      }

      analysisResult = await analyzeEmailContent(content, analysisType)
    } else if (contentType === 'calendar') {
      content = await prisma.calendarEvent.findFirst({
        where: { id: contentId, userId: user.id }
      })
      
      if (!content) {
        return errorResponse('Calendar event not found', 404)
      }

      analysisResult = await analyzeCalendarContent(content, analysisType)
    } else {
      return errorResponse('Invalid content type. Supported: email, calendar')
    }

  
    const analysisRecord = await prisma.reminder.create({
      data: {
        userId: user.id,
        type: 'CUSTOM',
        title: `AI Analysis: ${contentType} - ${content.title || content.subject}`,
        description: `AI content analysis completed`,
        scheduledFor: new Date(),
        status: 'COMPLETED'
      }
    })

    return successResponse({
      analysis: analysisResult,
      content: {
        id: content.id,
        type: contentType,
        title: content.title || content.subject
      },
      analysisId: analysisRecord.id
    }, 'Content analysis completed successfully')

  } catch (error) {
    console.error('Content analysis error:', error)
    return serverErrorResponse('Failed to analyze content')
  }
})

export const GET = requirePro(async (request: NextRequest, user) => {
  try {
  
    const { searchParams } = new URL(request.url)
    const contentType = searchParams.get('contentType')
    const limit = parseInt(searchParams.get('limit') || '20')

    const whereClause: any = {
      userId: user.id,
      title: { startsWith: 'AI Analysis:' }
    }

    if (contentType) {
      whereClause.title = { contains: contentType }
    }

    const analyses = await prisma.reminder.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      take: limit
    })

    return successResponse({
      analyses,
      totalAnalyses: analyses.length
    }, 'Analysis history retrieved successfully')

  } catch (error) {
    console.error('Get analysis history error:', error)
    return serverErrorResponse('Failed to get analysis history')
  }
})



async function analyzeEmailContent(email: any, analysisType: string): Promise<any> {
  try {
    const openai = getOpenAIClient()

    const systemPrompt = `You are an AI assistant that analyzes email content to provide insights and suggestions.
    Analyze the email for:
    - Urgency level (low, medium, high, critical)
    - Sentiment (positive, neutral, negative)
    - Action items and tasks mentioned
    - Key topics and themes
    - Response priority
    - Suggested response time
    - Important people mentioned
    - Deadlines or dates mentioned`

    const userPrompt = `Please analyze this email:

    From: ${email.from}
    Subject: ${email.subject}
    Received: ${email.receivedAt}
    
    Content:
    ${email.body}
    
    Provide a comprehensive analysis including urgency, sentiment, action items, and recommendations.`

    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
      max_tokens: 1000
    })

    const response = completion.choices[0]?.message?.content
    if (!response) {
      throw new Error('No analysis generated')
    }


    return {
      urgency: extractUrgencyLevel(response),
      sentiment: extractSentiment(response),
      actionItems: extractActionItems(response),
      keyTopics: extractKeyTopics(response),
      responsePriority: extractResponsePriority(response),
      suggestedResponseTime: extractResponseTime(response),
      importantPeople: extractImportantPeople(response),
      deadlines: extractDeadlines(response),
      summary: response,
      confidence: 85,
      analysisType
    }

  } catch (error) {
    console.error('Email analysis error:', error)
    throw error
  }
}

async function analyzeCalendarContent(event: any, analysisType: string): Promise<any> {
  try {
    const openai = getOpenAIClient()

    const systemPrompt = `You are an AI assistant that analyzes calendar events to provide insights and suggestions.
    Analyze the event for:
    - Meeting importance (low, medium, high, critical)
    - Preparation requirements
    - Key attendees and their roles
    - Meeting type and purpose
    - Optimal reminder timing
    - Pre-meeting tasks
    - Follow-up requirements
    - Potential conflicts or issues`

    const userPrompt = `Please analyze this calendar event:

    Title: ${event.title}
    Description: ${event.description || 'No description'}
    Start: ${event.startTime}
    End: ${event.endTime}
    Location: ${event.location || 'No location specified'}
    Attendees: ${event.attendees?.join(', ') || 'No attendees listed'}
    
    Provide a comprehensive analysis including importance, preparation needs, and recommendations.`

    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
      max_tokens: 1000
    })

    const response = completion.choices[0]?.message?.content
    if (!response) {
      throw new Error('No analysis generated')
    }

    return {
      importance: extractImportanceLevel(response),
      preparationRequirements: extractPreparationRequirements(response),
      keyAttendees: extractKeyAttendees(response),
      meetingType: extractMeetingType(response),
      optimalReminderTiming: extractOptimalTiming(response),
      preMeetingTasks: extractPreMeetingTasks(response),
      followUpRequirements: extractFollowUpRequirements(response),
      summary: response,
      confidence: 85,
      analysisType
    }

  } catch (error) {
    console.error('Calendar analysis error:', error)
    throw error
  }
}



function extractUrgencyLevel(response: string): string {
  const urgencyKeywords = {
    critical: ['critical', 'urgent', 'asap', 'immediately'],
    high: ['high priority', 'important', 'soon'],
    medium: ['medium', 'moderate'],
    low: ['low', 'when convenient', 'no rush']
  }

  const lowerResponse = response.toLowerCase()
  
  for (const [level, keywords] of Object.entries(urgencyKeywords)) {
    if (keywords.some(keyword => lowerResponse.includes(keyword))) {
      return level
    }
  }
  
  return 'medium'
}

function extractSentiment(response: string): string {
  const sentimentKeywords = {
    positive: ['positive', 'happy', 'pleased', 'excited', 'good'],
    negative: ['negative', 'frustrated', 'angry', 'disappointed', 'concerned'],
    neutral: ['neutral', 'professional', 'formal']
  }

  const lowerResponse = response.toLowerCase()
  
  for (const [sentiment, keywords] of Object.entries(sentimentKeywords)) {
    if (keywords.some(keyword => lowerResponse.includes(keyword))) {
      return sentiment
    }
  }
  
  return 'neutral'
}

function extractActionItems(response: string): string[] {

  const actionWords = ['action', 'task', 'todo', 'need to', 'should', 'must']
  const lines = response.split('\n')
  
  return lines.filter(line => 
    actionWords.some(word => line.toLowerCase().includes(word))
  ).slice(0, 5) 
}

function extractKeyTopics(response: string): string[] {
  const topics = []
  const lines = response.split('\n')
  
  lines.forEach(line => {
    if (line.includes('topic') || line.includes('theme')) {
      topics.push(line.trim())
    }
  })
  
  return topics.slice(0, 5)
}

function extractResponsePriority(response: string): string {
  if (response.toLowerCase().includes('high priority')) return 'high'
  if (response.toLowerCase().includes('low priority')) return 'low'
  return 'medium'
}

function extractResponseTime(response: string): string {
  const timePatterns = [
    'within 24 hours',
    'within 2 hours',
    'within 1 hour',
    'immediately',
    'by end of day',
    'within a week'
  ]
  
  const lowerResponse = response.toLowerCase()
  
  for (const pattern of timePatterns) {
    if (lowerResponse.includes(pattern)) {
      return pattern
    }
  }
  
  return 'within 24 hours'
}

function extractImportantPeople(response: string): string[] {
  return []
}

function extractDeadlines(response: string): string[] {
  return []
}

function extractImportanceLevel(response: string): string {
  return extractUrgencyLevel(response) 
}

function extractPreparationRequirements(response: string): string[] {
  return extractActionItems(response) 
}

function extractKeyAttendees(response: string): string[] {
  return []
}

function extractMeetingType(response: string): string {
  const types = ['standup', 'review', 'planning', 'presentation', 'discussion']
  const lowerResponse = response.toLowerCase()
  
  for (const type of types) {
    if (lowerResponse.includes(type)) {
      return type
    }
  }
  
  return 'general'
}

function extractOptimalTiming(response: string): string[] {
  return ['15 minutes before', '1 hour before']
}

function extractPreMeetingTasks(response: string): string[] {
  return extractActionItems(response)
}

function extractFollowUpRequirements(response: string): string[] {
  return []
}
