import { NextRequest } from 'next/server'
import { successResponse, errorResponse, notFoundResponse, serverErrorResponse } from '@/utils/response'
import { validateRequest, calendarEventSchema } from '@/utils/validation'
import { requireGoogle } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { getGoogleServices } from '@/lib/google'
import { prisma } from '@/lib/prisma'

interface RouteParams {
  params: {
    eventId: string
  }
}

export const GET = requireGoogle(async (request: NextRequest, user, { params }: RouteParams) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { eventId } = params

    const event = await prisma.calendarEvent.findFirst({
      where: {
        id: eventId,
        userId: user.id
      },
      include: {
        reminders: {
          orderBy: { scheduledFor: 'asc' }
        }
      }
    })

    if (!event) {
      return notFoundResponse('Calendar event not found')
    }

    const { searchParams } = new URL(request.url)
    const fetchFromGoogle = searchParams.get('fresh') === 'true'

    let googleEvent = null
    if (fetchFromGoogle) {
      try {
        const { calendar } = await getGoogleServices(user.id)
       
      } catch (error) {
        console.warn('Failed to fetch fresh event from Google:', error)
      }
    }

    return successResponse({
      event,
      googleEvent
    }, 'Calendar event retrieved successfully')

  } catch (error) {
    console.error('Get calendar event error:', error)
    return serverErrorResponse('Failed to fetch calendar event')
  }
})

export const PUT = requireGoogle(async (request: NextRequest, user, { params }: RouteParams) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { eventId } = params

    const existingEvent = await prisma.calendarEvent.findFirst({
      where: {
        id: eventId,
        userId: user.id
      }
    })

    if (!existingEvent) {
      return notFoundResponse('Calendar event not found')
    }

    const body = await request.json()
    const validation = validateRequest(calendarEventSchema.partial(), body)
    
    if (!validation.success) {
      return errorResponse(validation.error)
    }

    const { title, description, startTime, endTime, location, attendees } = validation.data

    const { calendar } = await getGoogleServices(user.id)

    const updateData: any = {}
    if (title !== undefined) updateData.summary = title
    if (description !== undefined) updateData.description = description
    if (startTime !== undefined) {
      updateData.start = {
        dateTime: new Date(startTime).toISOString(),
        timeZone: 'UTC'
      }
    }
    if (endTime !== undefined) {
      updateData.end = {
        dateTime: new Date(endTime).toISOString(),
        timeZone: 'UTC'
      }
    }
    if (location !== undefined) updateData.location = location
    if (attendees !== undefined) {
      updateData.attendees = attendees.map(email => ({ email }))
    }

    await calendar.updateEvent(existingEvent.googleEventId, updateData)

    const updatedEvent = await prisma.calendarEvent.update({
      where: { id: eventId },
      data: {
        ...(title !== undefined && { title }),
        ...(description !== undefined && { description }),
        ...(startTime !== undefined && { startTime: new Date(startTime) }),
        ...(endTime !== undefined && { endTime: new Date(endTime) }),
        ...(location !== undefined && { location }),
        ...(attendees !== undefined && { attendees }),
        updatedAt: new Date()
      },
      include: {
        reminders: true
      }
    })

    return successResponse({
      event: updatedEvent
    }, 'Calendar event updated successfully')

  } catch (error) {
    console.error('Update calendar event error:', error)
    return serverErrorResponse('Failed to update calendar event')
  }
})

export const DELETE = requireGoogle(async (request: NextRequest, user, { params }: RouteParams) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { eventId } = params

    const existingEvent = await prisma.calendarEvent.findFirst({
      where: {
        id: eventId,
        userId: user.id
      }
    })

    if (!existingEvent) {
      return notFoundResponse('Calendar event not found')
    }

    const { calendar } = await getGoogleServices(user.id)

    await calendar.deleteEvent(existingEvent.googleEventId)

    await prisma.calendarEvent.delete({
      where: { id: eventId }
    })

    return successResponse({
      deleted: true,
      eventId
    }, 'Calendar event deleted successfully')

  } catch (error) {
    console.error('Delete calendar event error:', error)
    return serverErrorResponse('Failed to delete calendar event')
  }
})
