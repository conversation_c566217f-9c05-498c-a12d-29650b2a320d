import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { validateRequest, calendarEventSchema } from '@/utils/validation'
import { requireGoogle } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { getGoogleServices } from '@/lib/google'
import { prisma } from '@/lib/prisma'
import { addDays, startOfDay, endOfDay } from 'date-fns'

export const GET = requireGoogle(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { searchParams } = new URL(request.url)
    const timeMin = searchParams.get('timeMin')
    const timeMax = searchParams.get('timeMax')
    const maxResults = parseInt(searchParams.get('maxResults') || '50')
    const syncToDatabase = searchParams.get('sync') === 'true'

    const defaultTimeMin = timeMin ? new Date(timeMin) : new Date()
    const defaultTimeMax = timeMax ? new Date(timeMax) : addDays(new Date(), 30)

    const { calendar } = await getGoogleServices(user.id)

    const googleEvents = await calendar.getEvents(defaultTimeMin, defaultTimeMax)

    if (syncToDatabase) {
      await syncEventsToDatabase(user.id, googleEvents)
    }

    const dbEvents = await prisma.calendarEvent.findMany({
      where: {
        userId: user.id,
        startTime: {
          gte: defaultTimeMin,
          lte: defaultTimeMax
        }
      },
      include: {
        reminders: {
          orderBy: { scheduledFor: 'asc' }
        }
      },
      orderBy: { startTime: 'asc' },
      take: maxResults
    })

    return successResponse({
      events: dbEvents,
      googleEvents: googleEvents.slice(0, maxResults),
      timeRange: {
        timeMin: defaultTimeMin,
        timeMax: defaultTimeMax
      },
      synced: syncToDatabase
    }, 'Calendar events retrieved successfully')

  } catch (error) {
    console.error('Get calendar events error:', error)
    return serverErrorResponse('Failed to fetch calendar events')
  }
})

export const POST = requireGoogle(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const body = await request.json()
    const validation = validateRequest(calendarEventSchema, body)
    
    if (!validation.success) {
      return errorResponse(validation.error)
    }

    const { title, description, startTime, endTime, location, attendees } = validation.data

    const { calendar } = await getGoogleServices(user.id)

    const googleEvent = await calendar.createEvent({
      summary: title,
      description,
      start: {
        dateTime: new Date(startTime).toISOString(),
        timeZone: 'UTC'
      },
      end: {
        dateTime: new Date(endTime).toISOString(),
        timeZone: 'UTC'
      },
      location,
      attendees: attendees?.map(email => ({ email }))
    })

    const dbEvent = await prisma.calendarEvent.create({
      data: {
        userId: user.id,
        googleEventId: googleEvent.id,
        title,
        description,
        startTime: new Date(startTime),
        endTime: new Date(endTime),
        location,
        attendees: attendees || []
      },
      include: {
        reminders: true
      }
    })

    return successResponse({
      event: dbEvent,
      googleEventId: googleEvent.id
    }, 'Calendar event created successfully')

  } catch (error) {
    console.error('Create calendar event error:', error)
    return serverErrorResponse('Failed to create calendar event')
  }
})

async function syncEventsToDatabase(userId: string, googleEvents: any[]) {
  try {
    for (const googleEvent of googleEvents) {
      if (!googleEvent.start?.dateTime || !googleEvent.end?.dateTime) {
        continue
      }

      await prisma.calendarEvent.upsert({
        where: {
          googleEventId: googleEvent.id
        },
        update: {
          title: googleEvent.summary || 'Untitled Event',
          description: googleEvent.description,
          startTime: new Date(googleEvent.start.dateTime),
          endTime: new Date(googleEvent.end.dateTime),
          location: googleEvent.location,
          attendees: googleEvent.attendees?.map((a: any) => a.email) || [],
          updatedAt: new Date()
        },
        create: {
          userId,
          googleEventId: googleEvent.id,
          title: googleEvent.summary || 'Untitled Event',
          description: googleEvent.description,
          startTime: new Date(googleEvent.start.dateTime),
          endTime: new Date(googleEvent.end.dateTime),
          location: googleEvent.location,
          attendees: googleEvent.attendees?.map((a: any) => a.email) || []
        }
      })
    }
  } catch (error) {
    console.error('Sync events to database error:', error)
    throw error
  }
}
