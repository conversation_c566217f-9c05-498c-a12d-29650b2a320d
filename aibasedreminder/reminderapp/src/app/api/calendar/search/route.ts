import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireGoogle } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { prisma } from '@/lib/prisma'

export const GET = requireGoogle(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const timeMin = searchParams.get('timeMin')
    const timeMax = searchParams.get('timeMax')
    const location = searchParams.get('location')
    const attendee = searchParams.get('attendee')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    if (!query && !location && !attendee) {
      return errorResponse('At least one search parameter (q, location, or attendee) is required')
    }

    const where: any = {
      userId: user.id
    }

    if (timeMin || timeMax) {
      where.startTime = {}
      if (timeMin) where.startTime.gte = new Date(timeMin)
      if (timeMax) where.startTime.lte = new Date(timeMax)
    }

    const textSearchConditions = []

    if (query) {
      const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0)
      
      for (const term of searchTerms) {
        textSearchConditions.push({
          OR: [
            { title: { contains: term, mode: 'insensitive' } },
            { description: { contains: term, mode: 'insensitive' } }
          ]
        })
      }
    }

    if (location) {
      textSearchConditions.push({
        location: { contains: location, mode: 'insensitive' }
      })
    }

    if (attendee) {
      textSearchConditions.push({
        attendees: { has: attendee }
      })
    }

    if (textSearchConditions.length > 0) {
      where.AND = textSearchConditions
    }

    const [events, totalCount] = await Promise.all([
      prisma.calendarEvent.findMany({
        where,
        include: {
          reminders: {
            select: {
              id: true,
              status: true,
              scheduledFor: true
            },
            orderBy: { scheduledFor: 'asc' }
          }
        },
        orderBy: [
          { startTime: 'asc' }
        ],
        take: limit,
        skip: offset
      }),
      prisma.calendarEvent.count({ where })
    ])

    const eventsWithScore = events.map(event => {
      let relevanceScore = 0

      if (query) {
        const searchTerms = query.toLowerCase().split(' ')
        const titleLower = event.title.toLowerCase()
        const descriptionLower = (event.description || '').toLowerCase()

        searchTerms.forEach(term => {
          if (titleLower.includes(term)) relevanceScore += 10
          if (descriptionLower.includes(term)) relevanceScore += 5
        })

        if (titleLower === query.toLowerCase()) relevanceScore += 20
      }

      if (location && event.location) {
        const locationLower = event.location.toLowerCase()
        if (locationLower.includes(location.toLowerCase())) relevanceScore += 15
        if (locationLower === location.toLowerCase()) relevanceScore += 25
      }

      if (attendee && event.attendees.includes(attendee)) {
        relevanceScore += 20
      }

      return {
        ...event,
        relevanceScore
      }
    })

    eventsWithScore.sort((a, b) => {
      if (a.relevanceScore !== b.relevanceScore) {
        return b.relevanceScore - a.relevanceScore
      }
      return new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
    })

    const results = {
      highRelevance: eventsWithScore.filter(e => e.relevanceScore >= 20),
      mediumRelevance: eventsWithScore.filter(e => e.relevanceScore >= 10 && e.relevanceScore < 20),
      lowRelevance: eventsWithScore.filter(e => e.relevanceScore > 0 && e.relevanceScore < 10),
      noScore: eventsWithScore.filter(e => e.relevanceScore === 0)
    }

    return successResponse({
      events: eventsWithScore,
      results,
      searchParams: {
        query,
        timeMin,
        timeMax,
        location,
        attendee
      },
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      },
      stats: {
        totalFound: totalCount,
        highRelevance: results.highRelevance.length,
        mediumRelevance: results.mediumRelevance.length,
        lowRelevance: results.lowRelevance.length
      }
    }, 'Calendar search completed successfully')

  } catch (error) {
    console.error('Calendar search error:', error)
    return serverErrorResponse('Failed to search calendar events')
  }
})

export const POST = requireGoogle(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const {
      textQuery,
      dateRange,
      timeRange,
      duration,
      attendees,
      locations,
      tags,
      reminderStatus,
      sortBy = 'relevance', 
      sortOrder = 'desc'
    } = body

    const where: any = {
      userId: user.id
    }

    if (dateRange) {
      where.startTime = {}
      if (dateRange.start) where.startTime.gte = new Date(dateRange.start)
      if (dateRange.end) where.startTime.lte = new Date(dateRange.end)
    }

    if (timeRange) {
   
    }

    
    if (duration) {
     
    }

    if (textQuery) {
      const searchTerms = textQuery.toLowerCase().split(' ').filter(term => term.length > 0)
      where.AND = searchTerms.map(term => ({
        OR: [
          { title: { contains: term, mode: 'insensitive' } },
          { description: { contains: term, mode: 'insensitive' } }
        ]
      }))
    }

    if (attendees && attendees.length > 0) {
      where.OR = attendees.map((attendee: string) => ({
        attendees: { has: attendee }
      }))
    }

    if (locations && locations.length > 0) {
      where.location = {
        in: locations
      }
    }

    let events = await prisma.calendarEvent.findMany({
      where,
      include: {
        reminders: true
      },
      orderBy: { startTime: 'asc' }
    })

    if (timeRange) {
      events = events.filter(event => {
        const eventHour = new Date(event.startTime).getHours()
        const startHour = timeRange.start ? parseInt(timeRange.start.split(':')[0]) : 0
        const endHour = timeRange.end ? parseInt(timeRange.end.split(':')[0]) : 23
        return eventHour >= startHour && eventHour <= endHour
      })
    }

    if (duration) {
      events = events.filter(event => {
        const eventDuration = (new Date(event.endTime).getTime() - new Date(event.startTime).getTime()) / (1000 * 60)
        if (duration.min && eventDuration < duration.min) return false
        if (duration.max && eventDuration > duration.max) return false
        return true
      })
    }

    if (reminderStatus) {
      events = events.filter(event => {
        const hasReminders = event.reminders && event.reminders.length > 0
        if (reminderStatus === 'with_reminders') return hasReminders
        if (reminderStatus === 'without_reminders') return !hasReminders
        if (reminderStatus === 'completed_reminders') {
          return hasReminders && event.reminders.some(r => r.status === 'COMPLETED')
        }
        return true
      })
    }

    if (sortBy === 'date') {
      events.sort((a, b) => {
        const comparison = new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
        return sortOrder === 'desc' ? -comparison : comparison
      })
    } else if (sortBy === 'duration') {
      events.sort((a, b) => {
        const aDuration = new Date(a.endTime).getTime() - new Date(a.startTime).getTime()
        const bDuration = new Date(b.endTime).getTime() - new Date(b.startTime).getTime()
        const comparison = aDuration - bDuration
        return sortOrder === 'desc' ? -comparison : comparison
      })
    }

    return successResponse({
      events,
      searchCriteria: body,
      totalFound: events.length
    }, 'Advanced calendar search completed successfully')

  } catch (error) {
    console.error('Advanced calendar search error:', error)
    return serverErrorResponse('Failed to perform advanced calendar search')
  }
})
