import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { validateRequest } from '@/utils/validation'
import { requireGoogle } from '@/middleware/protected'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const calendarSettingsSchema = z.object({
  autoSync: z.boolean().optional(),
  syncInterval: z.number().min(5).max(1440).optional(), 
  createReminders: z.boolean().optional(),
  reminderTimings: z.array(z.string()).optional(), 
  workingHours: z.object({
    start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), 
    end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    timezone: z.string().optional()
  }).optional(),
  excludeWeekends: z.boolean().optional(),
  excludeAllDayEvents: z.boolean().optional(),
  calendarFilters: z.object({
    includeCalendars: z.array(z.string()).optional(),
    excludeCalendars: z.array(z.string()).optional(),
    includeKeywords: z.array(z.string()).optional(),
    excludeKeywords: z.array(z.string()).optional()
  }).optional()
})

export const GET = requireGoogle(async (request: NextRequest, user) => {
  try {
  
    const userWithSettings = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        email: true,
        
      }
    })

  
    const defaultSettings = {
      autoSync: true,
      syncInterval: 60, 
      createReminders: true,
      reminderTimings: ["15 minutes before", "1 hour before"],
      workingHours: {
        start: "09:00",
        end: "17:00",
        timezone: "UTC"
      },
      excludeWeekends: false,
      excludeAllDayEvents: true,
      calendarFilters: {
        includeCalendars: [],
        excludeCalendars: [],
        includeKeywords: [],
        excludeKeywords: []
      }
    }

    return successResponse({
      settings: defaultSettings,
      lastUpdated: userWithSettings?.updatedAt || new Date()
    }, 'Calendar settings retrieved successfully')

  } catch (error) {
    console.error('Get calendar settings error:', error)
    return serverErrorResponse('Failed to get calendar settings')
  }
})

export const PUT = requireGoogle(async (request: NextRequest, user) => {
  try {
   
    const body = await request.json()
    const validation = validateRequest(calendarSettingsSchema, body)
    
    if (!validation.success) {
      return errorResponse(validation.error)
    }

    const settings = validation.data

    
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        updatedAt: new Date()
       
      }
    })

   
    console.log(`Calendar settings updated for user ${user.email}:`, settings)

    return successResponse({
      settings,
      updated: true,
      timestamp: updatedUser.updatedAt
    }, 'Calendar settings updated successfully')

  } catch (error) {
    console.error('Update calendar settings error:', error)
    return serverErrorResponse('Failed to update calendar settings')
  }
})

export const POST = requireGoogle(async (request: NextRequest, user) => {
  try {
    
    const defaultSettings = {
      autoSync: true,
      syncInterval: 60,
      createReminders: true,
      reminderTimings: ["15 minutes before", "1 hour before"],
      workingHours: {
        start: "09:00",
        end: "17:00",
        timezone: "UTC"
      },
      excludeWeekends: false,
      excludeAllDayEvents: true,
      calendarFilters: {
        includeCalendars: [],
        excludeCalendars: [],
        includeKeywords: [],
        excludeKeywords: []
      }
    }

   
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        updatedAt: new Date()
      }
    })

    return successResponse({
      settings: defaultSettings,
      reset: true,
      timestamp: updatedUser.updatedAt
    }, 'Calendar settings reset to defaults')

  } catch (error) {
    console.error('Reset calendar settings error:', error)
    return serverErrorResponse('Failed to reset calendar settings')
  }
})
