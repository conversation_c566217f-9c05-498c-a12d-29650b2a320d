import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function POST(request: NextRequest) {
  try {
    const channelId = request.headers.get('x-goog-channel-id')
    const channelToken = request.headers.get('x-goog-channel-token')
    const resourceId = request.headers.get('x-goog-resource-id')
    const resourceState = request.headers.get('x-goog-resource-state')
    const resourceUri = request.headers.get('x-goog-resource-uri')

    if (!channelId || !resourceId) {
      return errorResponse('Invalid webhook headers', 400)
    }

    console.log('Calendar webhook received:', {
      channelId,
      channelToken,
      resourceId,
      resourceState,
      resourceUri
    })

    switch (resourceState) {
      case 'sync':
        console.log('Calendar webhook sync notification received')
        break

      case 'exists':
        await handleCalendarUpdate(channelId, resourceId)
        break

      case 'not_exists':
        await handleCalendarDeletion(channelId, resourceId)
        break

      default:
        console.log(`Unknown resource state: ${resourceState}`)
    }

    return successResponse({ processed: true }, 'Webhook processed successfully')

  } catch (error) {
    console.error('Calendar webhook error:', error)
    return serverErrorResponse('Failed to process webhook')
  }
}

async function handleCalendarUpdate(channelId: string, resourceId: string) {
  try {
    
    
    const users = await prisma.user.findMany({
      include: {
        oauthTokens: {
          where: {
            provider: 'google',
            expiresAt: { gt: new Date() }
          }
        }
      }
    })

    for (const user of users) {
      if (user.oauthTokens.length === 0) continue

      try {
        const { calendar } = await getGoogleServices(user.id)

        const timeMin = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        const timeMax = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        
        const googleEvents = await calendar.getEvents(timeMin, timeMax)

        for (const googleEvent of googleEvents) {
          if (!googleEvent.start?.dateTime || !googleEvent.end?.dateTime) {
            continue
          }

          await prisma.calendarEvent.upsert({
            where: {
              googleEventId: googleEvent.id
            },
            update: {
              title: googleEvent.summary || 'Untitled Event',
              description: googleEvent.description,
              startTime: new Date(googleEvent.start.dateTime),
              endTime: new Date(googleEvent.end.dateTime),
              location: googleEvent.location,
              attendees: googleEvent.attendees?.map((a: any) => a.email) || [],
              updatedAt: new Date()
            },
            create: {
              userId: user.id,
              googleEventId: googleEvent.id,
              title: googleEvent.summary || 'Untitled Event',
              description: googleEvent.description,
              startTime: new Date(googleEvent.start.dateTime),
              endTime: new Date(googleEvent.end.dateTime),
              location: googleEvent.location,
              attendees: googleEvent.attendees?.map((a: any) => a.email) || []
            }
          })
        }

        console.log(`Synced ${googleEvents.length} events for user ${user.email}`)

      } catch (error) {
        console.error(`Error syncing calendar for user ${user.email}:`, error)
      }
    }

  } catch (error) {
    console.error('Error handling calendar update:', error)
    throw error
  }
}

async function handleCalendarDeletion(channelId: string, resourceId: string) {
  try {
    console.log(`Calendar deletion notification for channel ${channelId}, resource ${resourceId}`)
   
  } catch (error) {
    console.error('Error handling calendar deletion:', error)
    throw error
  }
}
