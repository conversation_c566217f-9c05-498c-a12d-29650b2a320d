import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireGoogle } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { prisma } from '@/lib/prisma'
import { startOfWeek, endOfWeek, startOfMonth, endOfMonth, format, eachDayOfInterval, eachWeekOfInterval, eachMonthOfInterval } from 'date-fns'

export const GET = requireGoogle(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'month' 
    const includeReminders = searchParams.get('reminders') === 'true'

    const now = new Date()
    let startDate: Date
    let endDate: Date

    switch (period) {
      case 'week':
        startDate = startOfWeek(now)
        endDate = endOfWeek(now)
        break
      case 'month':
        startDate = startOfMonth(now)
        endDate = endOfMonth(now)
        break
      case 'quarter':
        const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1)
        startDate = quarterStart
        endDate = new Date(quarterStart.getFullYear(), quarterStart.getMonth() + 3, 0)
        break
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1)
        endDate = new Date(now.getFullYear(), 11, 31)
        break
      default:
        startDate = startOfMonth(now)
        endDate = endOfMonth(now)
    }

    const events = await prisma.calendarEvent.findMany({
      where: {
        userId: user.id,
        startTime: {
          gte: startDate,
          lte: endDate
        }
      },
      include: includeReminders ? {
        reminders: {
          select: {
            id: true,
            status: true,
            scheduledFor: true,
            createdAt: true
          }
        }
      } : undefined,
      orderBy: { startTime: 'asc' }
    })

    const analytics = {
      period: {
        type: period,
        startDate,
        endDate,
        label: `${format(startDate, 'MMM dd')} - ${format(endDate, 'MMM dd, yyyy')}`
      },
      summary: {
        totalEvents: events.length,
        totalHours: calculateTotalHours(events),
        averageEventDuration: calculateAverageEventDuration(events),
        busyDays: calculateBusyDays(events),
        freeDays: calculateFreeDays(events, startDate, endDate)
      },
      breakdown: {
        byDay: calculateEventsByDay(events, startDate, endDate),
        byWeek: period !== 'week' ? calculateEventsByWeek(events, startDate, endDate) : null,
        byMonth: ['quarter', 'year'].includes(period) ? calculateEventsByMonth(events, startDate, endDate) : null,
        byHour: calculateEventsByHour(events),
        byDuration: calculateEventsByDuration(events)
      },
      patterns: {
        mostBusyDay: findMostBusyDay(events),
        mostBusyHour: findMostBusyHour(events),
        averageEventsPerDay: events.length / getDaysBetween(startDate, endDate),
        longestEvent: findLongestEvent(events),
        shortestEvent: findShortestEvent(events)
      }
    }

    if (includeReminders) {
      const allReminders = events.flatMap(event => event.reminders || [])
      analytics.reminders = {
        total: allReminders.length,
        byStatus: calculateRemindersByStatus(allReminders),
        effectiveness: calculateReminderEffectiveness(allReminders)
      }
    }

    return successResponse(analytics, 'Calendar analytics retrieved successfully')

  } catch (error) {
    console.error('Get calendar analytics error:', error)
    return serverErrorResponse('Failed to get calendar analytics')
  }
})


function calculateTotalHours(events: any[]): number {
  return events.reduce((total, event) => {
    const duration = new Date(event.endTime).getTime() - new Date(event.startTime).getTime()
    return total + (duration / (1000 * 60 * 60))
  }, 0)
}

function calculateAverageEventDuration(events: any[]): number {
  if (events.length === 0) return 0
  const totalHours = calculateTotalHours(events)
  return totalHours / events.length
}

function calculateBusyDays(events: any[]): number {
  const uniqueDays = new Set(
    events.map(event => format(new Date(event.startTime), 'yyyy-MM-dd'))
  )
  return uniqueDays.size
}

function calculateFreeDays(events: any[], startDate: Date, endDate: Date): number {
  const totalDays = getDaysBetween(startDate, endDate)
  const busyDays = calculateBusyDays(events)
  return totalDays - busyDays
}

function calculateEventsByDay(events: any[], startDate: Date, endDate: Date): any[] {
  const days = eachDayOfInterval({ start: startDate, end: endDate })
  
  return days.map(day => {
    const dayEvents = events.filter(event => 
      format(new Date(event.startTime), 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd')
    )
    
    return {
      date: format(day, 'yyyy-MM-dd'),
      dayName: format(day, 'EEEE'),
      eventCount: dayEvents.length,
      totalHours: calculateTotalHours(dayEvents)
    }
  })
}

function calculateEventsByWeek(events: any[], startDate: Date, endDate: Date): any[] {
  const weeks = eachWeekOfInterval({ start: startDate, end: endDate })
  
  return weeks.map(week => {
    const weekEnd = endOfWeek(week)
    const weekEvents = events.filter(event => {
      const eventDate = new Date(event.startTime)
      return eventDate >= week && eventDate <= weekEnd
    })
    
    return {
      weekStart: format(week, 'yyyy-MM-dd'),
      weekEnd: format(weekEnd, 'yyyy-MM-dd'),
      eventCount: weekEvents.length,
      totalHours: calculateTotalHours(weekEvents)
    }
  })
}

function calculateEventsByMonth(events: any[], startDate: Date, endDate: Date): any[] {
  const months = eachMonthOfInterval({ start: startDate, end: endDate })
  
  return months.map(month => {
    const monthEnd = endOfMonth(month)
    const monthEvents = events.filter(event => {
      const eventDate = new Date(event.startTime)
      return eventDate >= month && eventDate <= monthEnd
    })
    
    return {
      month: format(month, 'yyyy-MM'),
      monthName: format(month, 'MMMM yyyy'),
      eventCount: monthEvents.length,
      totalHours: calculateTotalHours(monthEvents)
    }
  })
}

function calculateEventsByHour(events: any[]): any[] {
  const hourCounts = new Array(24).fill(0)
  
  events.forEach(event => {
    const hour = new Date(event.startTime).getHours()
    hourCounts[hour]++
  })
  
  return hourCounts.map((count, hour) => ({
    hour,
    hourLabel: `${hour.toString().padStart(2, '0')}:00`,
    eventCount: count
  }))
}

function calculateEventsByDuration(events: any[]): any {
  const durations = {
    short: 0,    
    medium: 0, 
    long: 0,    
  }
  
  events.forEach(event => {
    const durationHours = (new Date(event.endTime).getTime() - new Date(event.startTime).getTime()) / (1000 * 60 * 60)
    
    if (durationHours < 0.5) {
      durations.short++
    } else if (durationHours < 2) {
      durations.medium++
    } else {
      durations.long++
    }
  })
  
  return durations
}

function findMostBusyDay(events: any[]): string | null {
  if (events.length === 0) return null
  
  const dayCounts = events.reduce((acc, event) => {
    const day = format(new Date(event.startTime), 'EEEE')
    acc[day] = (acc[day] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  return Object.entries(dayCounts).reduce((a, b) => dayCounts[a[0]] > dayCounts[b[0]] ? a : b)[0]
}

function findMostBusyHour(events: any[]): number | null {
  if (events.length === 0) return null
  
  const hourCounts = events.reduce((acc, event) => {
    const hour = new Date(event.startTime).getHours()
    acc[hour] = (acc[hour] || 0) + 1
    return acc
  }, {} as Record<number, number>)
  
  return parseInt(Object.entries(hourCounts).reduce((a, b) => hourCounts[parseInt(a[0])] > hourCounts[parseInt(b[0])] ? a : b)[0])
}

function findLongestEvent(events: any[]): any | null {
  if (events.length === 0) return null
  
  return events.reduce((longest, event) => {
    const duration = new Date(event.endTime).getTime() - new Date(event.startTime).getTime()
    const longestDuration = new Date(longest.endTime).getTime() - new Date(longest.startTime).getTime()
    return duration > longestDuration ? event : longest
  })
}

function findShortestEvent(events: any[]): any | null {
  if (events.length === 0) return null
  
  return events.reduce((shortest, event) => {
    const duration = new Date(event.endTime).getTime() - new Date(event.startTime).getTime()
    const shortestDuration = new Date(shortest.endTime).getTime() - new Date(shortest.startTime).getTime()
    return duration < shortestDuration ? event : shortest
  })
}

function calculateRemindersByStatus(reminders: any[]): any {
  return reminders.reduce((acc, reminder) => {
    acc[reminder.status] = (acc[reminder.status] || 0) + 1
    return acc
  }, {})
}

function calculateReminderEffectiveness(reminders: any[]): number {
  if (reminders.length === 0) return 0
  const completedReminders = reminders.filter(r => r.status === 'COMPLETED').length
  return (completedReminders / reminders.length) * 100
}

function getDaysBetween(startDate: Date, endDate: Date): number {
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}
