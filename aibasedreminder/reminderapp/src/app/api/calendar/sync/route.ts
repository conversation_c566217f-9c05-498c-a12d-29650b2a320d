import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireGoogle } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { getGoogleServices } from '@/lib/google'
import { prisma } from '@/lib/prisma'
import { addDays, subDays } from 'date-fns'

export const POST = requireGoogle(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const body = await request.json().catch(() => ({}))
    const {
      timeMin = subDays(new Date(), 7).toISOString(),
      timeMax = addDays(new Date(), 30).toISOString(), 
      fullSync = false,
      createReminders = true
    } = body

    const { calendar } = await getGoogleServices(user.id)

    const googleEvents = await calendar.getEvents(new Date(timeMin), new Date(timeMax))

    let syncStats = {
      totalGoogleEvents: googleEvents.length,
      created: 0,
      updated: 0,
      skipped: 0,
      remindersCreated: 0,
      errors: []
    }

    if (fullSync) {
      await prisma.calendarEvent.deleteMany({
        where: {
          userId: user.id,
          startTime: {
            gte: new Date(timeMin),
            lte: new Date(timeMax)
          }
        }
      })
    }

    for (const googleEvent of googleEvents) {
      try {
        if (!googleEvent.start?.dateTime || !googleEvent.end?.dateTime) {
          syncStats.skipped++
          continue 
        }

        const eventData = {
          userId: user.id,
          googleEventId: googleEvent.id,
          title: googleEvent.summary || 'Untitled Event',
          description: googleEvent.description || null,
          startTime: new Date(googleEvent.start.dateTime),
          endTime: new Date(googleEvent.end.dateTime),
          location: googleEvent.location || null,
          attendees: googleEvent.attendees?.map((a: any) => a.email) || []
        }

        const existingEvent = await prisma.calendarEvent.findUnique({
          where: { googleEventId: googleEvent.id }
        })

        let dbEvent
        if (existingEvent) {
          dbEvent = await prisma.calendarEvent.update({
            where: { googleEventId: googleEvent.id },
            data: {
              ...eventData,
              updatedAt: new Date()
            }
          })
          syncStats.updated++
        } else {
          // Create new event
          dbEvent = await prisma.calendarEvent.create({
            data: eventData
          })
          syncStats.created++
        }

        // Create automatic reminders if requested
        if (createReminders && !existingEvent) {
          const reminderCount = await createAutomaticReminders(dbEvent)
          syncStats.remindersCreated += reminderCount
        }

      } catch (error) {
        console.error(`Error syncing event ${googleEvent.id}:`, error)
        syncStats.errors.push({
          eventId: googleEvent.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    await prisma.user.update({
      where: { id: user.id },
      data: { updatedAt: new Date() }
    })

    return successResponse({
      syncStats,
      timeRange: { timeMin, timeMax },
      fullSync,
      createReminders
    }, 'Calendar sync completed successfully')

  } catch (error) {
    console.error('Calendar sync error:', error)
    return serverErrorResponse('Failed to sync calendar')
  }
})

export const GET = requireGoogle(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const includeStats = searchParams.get('stats') === 'true'

    const lastSyncTime = await prisma.user.findUnique({
      where: { id: user.id },
      select: { updatedAt: true }
    })

    let stats = null
    if (includeStats) {
      const [totalEvents, upcomingEvents, pastEvents] = await Promise.all([
        prisma.calendarEvent.count({ where: { userId: user.id } }),
        prisma.calendarEvent.count({
          where: {
            userId: user.id,
            startTime: { gte: new Date() }
          }
        }),
        prisma.calendarEvent.count({
          where: {
            userId: user.id,
            startTime: { lt: new Date() }
          }
        })
      ])

      stats = {
        totalEvents,
        upcomingEvents,
        pastEvents
      }
    }

    return successResponse({
      lastSyncTime: lastSyncTime?.updatedAt,
      stats
    }, 'Sync status retrieved successfully')

  } catch (error) {
    console.error('Get sync status error:', error)
    return serverErrorResponse('Failed to get sync status')
  }
})

async function createAutomaticReminders(event: any): Promise<number> {
  try {
    const now = new Date()
    const eventStart = new Date(event.startTime)
    
    if (eventStart <= now) {
      return 0
    }

    const reminders = []
    
    const timeDiff = eventStart.getTime() - now.getTime()
    const hoursUntilEvent = timeDiff / (1000 * 60 * 60)

    if (hoursUntilEvent > 0.25) {
      reminders.push({
        userId: event.userId,
        eventId: event.id,
        type: 'MEETING' as const,
        title: `Meeting reminder: ${event.title}`,
        description: `Your meeting "${event.title}" starts in 15 minutes`,
        scheduledFor: new Date(eventStart.getTime() - 15 * 60 * 1000)
      })
    }

    if (hoursUntilEvent > 1) {
      reminders.push({
        userId: event.userId,
        eventId: event.id,
        type: 'MEETING' as const,
        title: `Meeting reminder: ${event.title}`,
        description: `Your meeting "${event.title}" starts in 1 hour`,
        scheduledFor: new Date(eventStart.getTime() - 60 * 60 * 1000)
      })
    }

    if (hoursUntilEvent > 24) {
      reminders.push({
        userId: event.userId,
        eventId: event.id,
        type: 'MEETING' as const,
        title: `Meeting reminder: ${event.title}`,
        description: `Your meeting "${event.title}" is tomorrow`,
        scheduledFor: new Date(eventStart.getTime() - 24 * 60 * 60 * 1000)
      })
    }

    if (reminders.length > 0) {
      await prisma.reminder.createMany({
        data: reminders
      })
    }

    return reminders.length
  } catch (error) {
    console.error('Error creating automatic reminders:', error)
    return 0
  }
}
