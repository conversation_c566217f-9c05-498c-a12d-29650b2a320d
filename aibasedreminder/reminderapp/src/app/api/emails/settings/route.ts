import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { validateRequest } from '@/utils/validation'
import { requireGoogle } from '@/middleware/protected'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const emailSettingsSchema = z.object({
  autoSync: z.boolean().optional(),
  syncInterval: z.number().min(5).max(1440).optional(), 
  createReminders: z.boolean().optional(),
  reminderTimings: z.array(z.string()).optional(), 
  importantEmailReminders: z.boolean().optional(),
  unreadEmailThreshold: z.number().min(1).max(168).optional(), 
  emailFilters: z.object({
    includeSenders: z.array(z.string()).optional(),
    excludeSenders: z.array(z.string()).optional(),
    includeKeywords: z.array(z.string()).optional(),
    excludeKeywords: z.array(z.string()).optional(),
    includeLabels: z.array(z.string()).optional(),
    excludeLabels: z.array(z.string()).optional()
  }).optional(),
  aiSettings: z.object({
    enableAiSuggestions: z.boolean().optional(),
    defaultTone: z.enum(['professional', 'casual', 'friendly', 'formal']).optional(),
    defaultLength: z.enum(['short', 'medium', 'long']).optional(),
    autoGenerateReplies: z.boolean().optional()
  }).optional(),
  notificationSettings: z.object({
    emailNotifications: z.boolean().optional(),
    telegramNotifications: z.boolean().optional(),
    urgentEmailsOnly: z.boolean().optional(),
    quietHours: z.object({
      enabled: z.boolean(),
      start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
      end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
      timezone: z.string().optional()
    }).optional()
  }).optional()
})

export const GET = requireGoogle(async (request: NextRequest, user) => {
  try {
   
    const userWithSettings = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        email: true,
        isPro: true,
        updatedAt: true
      }
    })

    const defaultSettings = {
      autoSync: true,
      syncInterval: 30, 
      createReminders: true,
      reminderTimings: ["2 hours after", "1 day after"],
      importantEmailReminders: true,
      unreadEmailThreshold: 24, 
      emailFilters: {
        includeSenders: [],
        excludeSenders: ["noreply@", "no-reply@"],
        includeKeywords: [],
        excludeKeywords: ["unsubscribe", "newsletter"],
        includeLabels: ["INBOX", "IMPORTANT"],
        excludeLabels: ["SPAM", "TRASH"]
      },
      aiSettings: {
        enableAiSuggestions: userWithSettings?.isPro || false,
        defaultTone: "professional" as const,
        defaultLength: "medium" as const,
        autoGenerateReplies: false
      },
      notificationSettings: {
        emailNotifications: true,
        telegramNotifications: true,
        urgentEmailsOnly: false,
        quietHours: {
          enabled: true,
          start: "22:00",
          end: "08:00",
          timezone: "UTC"
        }
      }
    }

    return successResponse({
      settings: defaultSettings,
      lastUpdated: userWithSettings?.updatedAt || new Date(),
      isPro: userWithSettings?.isPro || false
    }, 'Email settings retrieved successfully')

  } catch (error) {
    console.error('Get email settings error:', error)
    return serverErrorResponse('Failed to get email settings')
  }
})

export const PUT = requireGoogle(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const validation = validateRequest(emailSettingsSchema, body)
    
    if (!validation.success) {
      return errorResponse(validation.error)
    }

    const settings = validation.data

    if (settings.aiSettings?.enableAiSuggestions && !user.isPro) {
      return errorResponse('AI features require Pro subscription')
    }

   
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        updatedAt: new Date()
      }
    })

    console.log(`Email settings updated for user ${user.email}:`, settings)

    return successResponse({
      settings,
      updated: true,
      timestamp: updatedUser.updatedAt
    }, 'Email settings updated successfully')

  } catch (error) {
    console.error('Update email settings error:', error)
    return serverErrorResponse('Failed to update email settings')
  }
})

export const POST = requireGoogle(async (request: NextRequest, user) => {
  try {
    const defaultSettings = {
      autoSync: true,
      syncInterval: 30,
      createReminders: true,
      reminderTimings: ["2 hours after", "1 day after"],
      importantEmailReminders: true,
      unreadEmailThreshold: 24,
      emailFilters: {
        includeSenders: [],
        excludeSenders: ["noreply@", "no-reply@"],
        includeKeywords: [],
        excludeKeywords: ["unsubscribe", "newsletter"],
        includeLabels: ["INBOX", "IMPORTANT"],
        excludeLabels: ["SPAM", "TRASH"]
      },
      aiSettings: {
        enableAiSuggestions: user.isPro,
        defaultTone: "professional" as const,
        defaultLength: "medium" as const,
        autoGenerateReplies: false
      },
      notificationSettings: {
        emailNotifications: true,
        telegramNotifications: true,
        urgentEmailsOnly: false,
        quietHours: {
          enabled: true,
          start: "22:00",
          end: "08:00",
          timezone: "UTC"
        }
      }
    }

    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        updatedAt: new Date()
      }
    })

    return successResponse({
      settings: defaultSettings,
      reset: true,
      timestamp: updatedUser.updatedAt
    }, 'Email settings reset to defaults')

  } catch (error) {
    console.error('Reset email settings error:', error)
    return serverErrorResponse('Failed to reset email settings')
  }
})
