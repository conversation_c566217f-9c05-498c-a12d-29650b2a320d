import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Background email check triggered...')
    
    // Get all users with valid Google OAuth tokens and Telegram connections
    const users = await prisma.user.findMany({
      where: {
        AND: [
          {
            oAuthTokens: {
              some: {
                provider: 'google',
                expiresAt: {
                  gt: new Date()
                }
              }
            }
          },
          {
            telegramUsers: {
              some: {
                isActive: true
              }
            }
          }
        ]
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oAuthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with valid tokens and Telegram connections`)
    
    let totalNewEmails = 0
    let totalNotifications = 0

    for (const user of users) {
      try {
        console.log(`📧 Checking emails for user: ${user.email}`)
        
        const { gmail } = await getGoogleServices(user.id)
        
        // Get recent unread emails (last 10 minutes)
        const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000)
        const query = `is:unread after:${Math.floor(tenMinutesAgo.getTime() / 1000)}`
        
        const gmailMessages = await gmail.getMessages(query, 10)
        console.log(`📨 Found ${gmailMessages.length} recent unread emails for ${user.email}`)
        
        let userNewEmails = 0
        let userNotifications = 0
        
        for (const gmailMessage of gmailMessages) {
          try {
            // Check if we already have this email
            const existingEmail = await prisma.email.findUnique({
              where: { gmailMessageId: gmailMessage.id }
            })
            
            if (!existingEmail) {
              // This is a new email - save it and send notification
              console.log(`✨ New email found: ${gmailMessage.subject}`)
              
              const newEmail = await prisma.email.create({
                data: {
                  userId: user.id,
                  gmailMessageId: gmailMessage.id,
                  threadId: gmailMessage.threadId || gmailMessage.id,
                  subject: gmailMessage.subject || 'No Subject',
                  from: gmailMessage.from || 'Unknown Sender',
                  to: gmailMessage.to || user.email,
                  cc: gmailMessage.cc || '',
                  body: gmailMessage.body || '',
                  snippet: gmailMessage.snippet || '',
                  receivedAt: new Date(gmailMessage.internalDate || Date.now()),
                  isRead: false,
                  category: 'inbox',
                  labels: gmailMessage.labelIds || []
                }
              })
              
              userNewEmails++
              
              // Send immediate notification to all user's Telegram accounts
              for (const telegramUser of user.telegramUsers) {
                const notificationMessage = `📧 NEW EMAIL RECEIVED!\n\n📧 From: ${gmailMessage.from}\n📝 Subject: ${gmailMessage.subject}\n📄 Preview: ${gmailMessage.snippet}\n⏰ Received: ${new Date().toLocaleString()}\n\n💬 Reply: reply:${newEmail.id}:Your message here\n📱 Example: reply:${newEmail.id}:Thank you for your email!\n\n⏰ I'll remind you every time you interact with the bot until you read or reply!`
                
                // Use direct Telegram API call
                await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    chat_id: telegramUser.telegramId,
                    text: notificationMessage
                  })
                })
                
                userNotifications++
                console.log(`📱 Sent notification to Telegram user ${telegramUser.telegramId}`)
              }
            }
          } catch (emailError) {
            console.error(`❌ Error processing email ${gmailMessage.id}:`, emailError)
          }
        }
        
        totalNewEmails += userNewEmails
        totalNotifications += userNotifications
        
        console.log(`✅ User ${user.email}: ${userNewEmails} new emails, ${userNotifications} notifications sent`)
        
      } catch (userError) {
        console.error(`❌ Error checking emails for user ${user.email}:`, userError)
      }
    }
    
    console.log(`🎉 Background email check completed: ${totalNewEmails} new emails, ${totalNotifications} notifications sent`)
    
    return successResponse({
      usersChecked: users.length,
      newEmails: totalNewEmails,
      notificationsSent: totalNotifications,
      timestamp: new Date().toISOString()
    }, 'Background email check completed successfully')
    
  } catch (error) {
    console.error('❌ Background email check failed:', error)
    return serverErrorResponse(`Background email check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const GET = POST
