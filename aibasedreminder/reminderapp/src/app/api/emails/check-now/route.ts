import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireGoogle } from '@/middleware/auth'

export const POST = requireGoogle(async (request: NextRequest, user) => {
  try {
    console.log('🔄 Manual email check triggered for user:', user.email)
    
    // Call the cron endpoint internally
    const cronUrl = `${process.env.NEXTAUTH_URL || 'https://aibasedreminder.vercel.app'}/api/cron/check-emails`
    
    const response = await fetch(cronUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (!response.ok) {
      throw new Error(`Cron job failed: ${response.status}`)
    }
    
    const result = await response.json()
    
    return successResponse({
      triggered: true,
      result: result.data,
      message: 'Email check triggered successfully'
    }, 'Manual email check completed')
    
  } catch (error) {
    console.error('❌ Manual email check failed:', error)
    return serverErrorResponse(`Manual email check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
})

export const GET = POST
