import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireGoogle } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { prisma } from '@/lib/prisma'
import { startOfWeek, endOfWeek, startOfMonth, endOfMonth, format, eachDayOfInterval, subDays } from 'date-fns'

export const GET = requireGoogle(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'month' 
    const includeReminders = searchParams.get('reminders') === 'true'

    const now = new Date()
    let startDate: Date
    let endDate: Date

    switch (period) {
      case 'week':
        startDate = startOfWeek(now)
        endDate = endOfWeek(now)
        break
      case 'month':
        startDate = startOfMonth(now)
        endDate = endOfMonth(now)
        break
      case 'quarter':
        const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1)
        startDate = quarterStart
        endDate = new Date(quarterStart.getFullYear(), quarterStart.getMonth() + 3, 0)
        break
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1)
        endDate = new Date(now.getFullYear(), 11, 31)
        break
      default:
        startDate = startOfMonth(now)
        endDate = endOfMonth(now)
    }

    const emails = await prisma.email.findMany({
      where: {
        userId: user.id,
        receivedAt: {
          gte: startDate,
          lte: endDate
        }
      },
      include: includeReminders ? {
        reminders: {
          select: {
            id: true,
            status: true,
            scheduledFor: true,
            createdAt: true
          }
        }
      } : undefined,
      orderBy: { receivedAt: 'desc' }
    })

    const analytics = {
      period: {
        type: period,
        startDate,
        endDate,
        label: `${format(startDate, 'MMM dd')} - ${format(endDate, 'MMM dd, yyyy')}`
      },
      summary: {
        totalEmails: emails.length,
        unreadEmails: emails.filter(e => !e.isRead).length,
        importantEmails: emails.filter(e => e.isImportant).length,
        emailsWithAttachments: emails.filter(e => e.hasAttachments).length,
        averageEmailsPerDay: emails.length / getDaysBetween(startDate, endDate),
        responseRate: calculateResponseRate(emails)
      },
      breakdown: {
        byDay: calculateEmailsByDay(emails, startDate, endDate),
        bySender: calculateEmailsBySender(emails),
        byHour: calculateEmailsByHour(emails),
        byReadStatus: {
          read: emails.filter(e => e.isRead).length,
          unread: emails.filter(e => !e.isRead).length
        },
        byImportance: {
          important: emails.filter(e => e.isImportant).length,
          normal: emails.filter(e => !e.isImportant).length
        }
      },
      patterns: {
        busiestDay: findBusiestEmailDay(emails),
        busiestHour: findBusiestEmailHour(emails),
        topSenders: getTopSenders(emails, 10),
        longestUnreadEmail: findLongestUnreadEmail(emails),
        oldestUnreadEmail: findOldestUnreadEmail(emails)
      },
      trends: {
        emailVolumeChange: await calculateEmailVolumeChange(user.id, startDate, endDate),
        readRateChange: await calculateReadRateChange(user.id, startDate, endDate)
      }
    }

    if (includeReminders) {
      const allReminders = emails.flatMap(email => email.reminders || [])
      analytics.reminders = {
        total: allReminders.length,
        byStatus: calculateRemindersByStatus(allReminders),
        effectiveness: calculateReminderEffectiveness(allReminders),
        averageResponseTime: calculateAverageResponseTime(allReminders)
      }
    }

    return successResponse(analytics, 'Email analytics retrieved successfully')

  } catch (error) {
    console.error('Get email analytics error:', error)
    return serverErrorResponse('Failed to get email analytics')
  }
})


function calculateResponseRate(emails: any[]): number {
  const totalEmails = emails.length
  const readEmails = emails.filter(e => e.isRead).length
  return totalEmails > 0 ? (readEmails / totalEmails) * 100 : 0
}

function calculateEmailsByDay(emails: any[], startDate: Date, endDate: Date): any[] {
  const days = eachDayOfInterval({ start: startDate, end: endDate })
  
  return days.map(day => {
    const dayEmails = emails.filter(email => 
      format(new Date(email.receivedAt), 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd')
    )
    
    return {
      date: format(day, 'yyyy-MM-dd'),
      dayName: format(day, 'EEEE'),
      emailCount: dayEmails.length,
      unreadCount: dayEmails.filter(e => !e.isRead).length,
      importantCount: dayEmails.filter(e => e.isImportant).length
    }
  })
}

function calculateEmailsBySender(emails: any[]): any[] {
  const senderCounts = emails.reduce((acc, email) => {
    const sender = email.from
    acc[sender] = (acc[sender] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  return Object.entries(senderCounts)
    .map(([sender, count]) => ({ sender, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 20) 
}

function calculateEmailsByHour(emails: any[]): any[] {
  const hourCounts = new Array(24).fill(0)
  
  emails.forEach(email => {
    const hour = new Date(email.receivedAt).getHours()
    hourCounts[hour]++
  })
  
  return hourCounts.map((count, hour) => ({
    hour,
    hourLabel: `${hour.toString().padStart(2, '0')}:00`,
    emailCount: count
  }))
}

function findBusiestEmailDay(emails: any[]): string | null {
  if (emails.length === 0) return null
  
  const dayCounts = emails.reduce((acc, email) => {
    const day = format(new Date(email.receivedAt), 'EEEE')
    acc[day] = (acc[day] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  return Object.entries(dayCounts).reduce((a, b) => dayCounts[a[0]] > dayCounts[b[0]] ? a : b)[0]
}

function findBusiestEmailHour(emails: any[]): number | null {
  if (emails.length === 0) return null
  
  const hourCounts = emails.reduce((acc, email) => {
    const hour = new Date(email.receivedAt).getHours()
    acc[hour] = (acc[hour] || 0) + 1
    return acc
  }, {} as Record<number, number>)
  
  return parseInt(Object.entries(hourCounts).reduce((a, b) => hourCounts[parseInt(a[0])] > hourCounts[parseInt(b[0])] ? a : b)[0])
}

function getTopSenders(emails: any[], limit: number): any[] {
  const senderCounts = emails.reduce((acc, email) => {
    const sender = email.from
    acc[sender] = (acc[sender] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  return Object.entries(senderCounts)
    .map(([sender, count]) => ({ sender, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, limit)
}

function findLongestUnreadEmail(emails: any[]): any | null {
  const unreadEmails = emails.filter(e => !e.isRead)
  if (unreadEmails.length === 0) return null
  
  return unreadEmails.reduce((longest, email) => {
    return email.body.length > longest.body.length ? email : longest
  })
}

function findOldestUnreadEmail(emails: any[]): any | null {
  const unreadEmails = emails.filter(e => !e.isRead)
  if (unreadEmails.length === 0) return null
  
  return unreadEmails.reduce((oldest, email) => {
    return new Date(email.receivedAt) < new Date(oldest.receivedAt) ? email : oldest
  })
}

async function calculateEmailVolumeChange(userId: string, startDate: Date, endDate: Date): Promise<number> {
  try {
    const periodDays = getDaysBetween(startDate, endDate)
    const previousStartDate = subDays(startDate, periodDays)
    const previousEndDate = subDays(endDate, periodDays)

    const [currentPeriodCount, previousPeriodCount] = await Promise.all([
      prisma.email.count({
        where: {
          userId,
          receivedAt: { gte: startDate, lte: endDate }
        }
      }),
      prisma.email.count({
        where: {
          userId,
          receivedAt: { gte: previousStartDate, lte: previousEndDate }
        }
      })
    ])

    if (previousPeriodCount === 0) return currentPeriodCount > 0 ? 100 : 0
    return ((currentPeriodCount - previousPeriodCount) / previousPeriodCount) * 100
  } catch (error) {
    console.error('Error calculating email volume change:', error)
    return 0
  }
}

async function calculateReadRateChange(userId: string, startDate: Date, endDate: Date): Promise<number> {
  try {
    const periodDays = getDaysBetween(startDate, endDate)
    const previousStartDate = subDays(startDate, periodDays)
    const previousEndDate = subDays(endDate, periodDays)

    const [currentPeriodRead, currentPeriodTotal, previousPeriodRead, previousPeriodTotal] = await Promise.all([
      prisma.email.count({
        where: {
          userId,
          receivedAt: { gte: startDate, lte: endDate },
          isRead: true
        }
      }),
      prisma.email.count({
        where: {
          userId,
          receivedAt: { gte: startDate, lte: endDate }
        }
      }),
      prisma.email.count({
        where: {
          userId,
          receivedAt: { gte: previousStartDate, lte: previousEndDate },
          isRead: true
        }
      }),
      prisma.email.count({
        where: {
          userId,
          receivedAt: { gte: previousStartDate, lte: previousEndDate }
        }
      })
    ])

    const currentReadRate = currentPeriodTotal > 0 ? (currentPeriodRead / currentPeriodTotal) * 100 : 0
    const previousReadRate = previousPeriodTotal > 0 ? (previousPeriodRead / previousPeriodTotal) * 100 : 0

    return currentReadRate - previousReadRate
  } catch (error) {
    console.error('Error calculating read rate change:', error)
    return 0
  }
}

function calculateRemindersByStatus(reminders: any[]): any {
  return reminders.reduce((acc, reminder) => {
    acc[reminder.status] = (acc[reminder.status] || 0) + 1
    return acc
  }, {})
}

function calculateReminderEffectiveness(reminders: any[]): number {
  if (reminders.length === 0) return 0
  const completedReminders = reminders.filter(r => r.status === 'COMPLETED').length
  return (completedReminders / reminders.length) * 100
}

function calculateAverageResponseTime(reminders: any[]): number {
  const completedReminders = reminders.filter(r => r.status === 'COMPLETED')
  if (completedReminders.length === 0) return 0

  const totalResponseTime = completedReminders.reduce((total, reminder) => {
    const responseTime = new Date(reminder.updatedAt).getTime() - new Date(reminder.scheduledFor).getTime()
    return total + responseTime
  }, 0)

  return totalResponseTime / completedReminders.length / (1000 * 60 * 60)
}

function getDaysBetween(startDate: Date, endDate: Date): number {
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}
