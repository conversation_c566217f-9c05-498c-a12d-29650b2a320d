import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'
import { sendEmailNotification } from '@/lib/telegram'

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Manual immediate email sync triggered')
    
    // Get all users with active Telegram connections and valid OAuth tokens
    const users = await prisma.user.findMany({
      where: {
        telegramUsers: {
          some: { isActive: true }
        },
        oauthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    console.log(`📧 Immediate sync for ${users.length} users with Telegram connections`)

    let totalNewEmails = 0

    for (const user of users) {
      const newEmails = await syncUserEmailsImmediate(user)
      totalNewEmails += newEmails
    }

    return NextResponse.json({
      success: true,
      data: {
        message: `Immediate email sync completed`,
        usersChecked: users.length,
        newEmailsFound: totalNewEmails
      }
    })

  } catch (error) {
    console.error('Error in immediate email sync:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to sync emails immediately'
    }, { status: 500 })
  }
}

async function syncUserEmailsImmediate(user: any): Promise<number> {
  try {
    const { gmail } = await getGoogleServices(user.id)
    
    // Get very recent unread emails (last 30 seconds for immediate detection)
    const thirtySecondsAgo = new Date(Date.now() - 30 * 1000)
    const query = `is:unread after:${Math.floor(thirtySecondsAgo.getTime() / 1000)}`
    
    console.log(`🔍 Checking immediate emails for ${user.email} with query: ${query}`)
    
    const gmailMessages = await gmail.getMessages(query, 10)
    
    if (gmailMessages.length === 0) {
      console.log(`📭 No new emails found for ${user.email}`)
      return 0
    }

    console.log(`📧 Found ${gmailMessages.length} immediate emails for ${user.email}`)

    let processedCount = 0

    for (const gmailMessage of gmailMessages) {
      // Check if we already processed this email
      const existingEmail = await prisma.email.findFirst({
        where: {
          userId: user.id,
          gmailMessageId: gmailMessage.id
        }
      })

      if (existingEmail) {
        console.log(`⏭️ Email ${gmailMessage.id} already processed, skipping`)
        continue // Skip already processed emails
      }

      // Process new email
      const subject = gmailMessage.payload?.headers?.find(h => h.name === 'Subject')?.value || 'No Subject'
      const from = gmailMessage.payload?.headers?.find(h => h.name === 'From')?.value || 'Unknown Sender'
      const isImportant = gmailMessage.labelIds?.includes('IMPORTANT') || false
      
      // Get email body
      let body = ''
      if (gmailMessage.payload?.parts) {
        const textPart = gmailMessage.payload.parts.find(part => part.mimeType === 'text/plain')
        if (textPart?.body?.data) {
          body = Buffer.from(textPart.body.data, 'base64').toString('utf-8')
        }
      } else if (gmailMessage.payload?.body?.data) {
        body = Buffer.from(gmailMessage.payload.body.data, 'base64').toString('utf-8')
      }

      // Save email to database
      const dbEmail = await prisma.email.create({
        data: {
          userId: user.id,
          gmailMessageId: gmailMessage.id,
          threadId: gmailMessage.threadId || '',
          subject,
          from,
          body: body.substring(0, 2000), // Limit body length
          isRead: false,
          isImportant,
          receivedAt: new Date(parseInt(gmailMessage.internalDate || '0'))
        }
      })

      // Send immediate Telegram notification
      console.log(`🚨 IMMEDIATE NOTIFICATION for new email: ${subject}`)

      const notificationSent = await sendEmailNotification(user.id, {
        id: dbEmail.id,
        subject,
        from,
        body,
        isImportant,
        receivedAt: new Date(parseInt(gmailMessage.internalDate || '0'))
      })

      if (notificationSent) {
        console.log(`✅ IMMEDIATE notification sent successfully for: ${subject}`)
        processedCount++
      } else {
        console.log(`❌ Failed to send immediate notification for: ${subject}`)
      }
    }

    return processedCount

  } catch (error) {
    console.error(`Error syncing immediate emails for user ${user.email}:`, error)
    return 0
  }
}
