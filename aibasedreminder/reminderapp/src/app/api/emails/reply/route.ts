import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { validateRequest, emailReplySchema } from '@/utils/validation'
import { requireGoogle } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { getGoogleServices } from '@/lib/google'
import { prisma } from '@/lib/prisma'

export const POST = requireGoogle(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const body = await request.json()
    const validation = validateRequest(emailReplySchema, body)
    
    if (!validation.success) {
      return errorResponse(validation.error)
    }

    const { to, cc, subject, body: emailBody, threadId, originalEmailId } = validation.data

    let originalEmail = null
    if (originalEmailId) {
      originalEmail = await prisma.email.findFirst({
        where: {
          id: originalEmailId,
          userId: user.id
        }
      })

      if (!originalEmail) {
        return errorResponse('Original email not found', 404)
      }
    }

    const { gmail } = await getGoogleServices(user.id)

    const emailContent = {
      to,
      cc: cc || [],
      subject,
      body: emailBody
    }

    await gmail.sendReply(
      originalEmail?.gmailMessageId || '',
      threadId || originalEmail?.threadId || '',
      emailContent
    )

    if (originalEmail && !originalEmail.isRead) {
      await prisma.email.update({
        where: { id: originalEmail.id },
        data: { isRead: true }
      })
    }

    console.log(`Email reply sent by user ${user.email} to ${to.join(', ')}`)

    return successResponse({
      sent: true,
      to,
      subject,
      threadId: threadId || originalEmail?.threadId,
      originalEmailId
    }, 'Email reply sent successfully')

  } catch (error) {
    console.error('Send email reply error:', error)
    return serverErrorResponse('Failed to send email reply')
  }
})
