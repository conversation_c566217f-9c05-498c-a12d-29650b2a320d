import { NextRequest } from 'next/server'
import { successResponse, errorResponse, notFoundResponse, serverErrorResponse } from '@/utils/response'
import { requireGoogle } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { getGoogleServices } from '@/lib/google'
import { prisma } from '@/lib/prisma'

interface RouteParams {
  params: {
    emailId: string
  }
}

export const GET = requireGoogle(async (request: NextRequest, user, { params }: RouteParams) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { emailId } = params

    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: user.id
      },
      include: {
        reminders: {
          orderBy: { scheduledFor: 'asc' }
        }
      }
    })

    if (!email) {
      return notFoundResponse('Email not found')
    }

    const { searchParams } = new URL(request.url)
    const fetchFromGmail = searchParams.get('fresh') === 'true'

    let gmailMessage = null
    if (fetchFromGmail) {
      try {
        const { gmail } = await getGoogleServices(user.id)
      
      } catch (error) {
        console.warn('Failed to fetch fresh email from Gmail:', error)
      }
    }

    return successResponse({
      email,
      gmailMessage
    }, 'Email retrieved successfully')

  } catch (error) {
    console.error('Get email error:', error)
    return serverErrorResponse('Failed to fetch email')
  }
})

export const PUT = requireGoogle(async (request: NextRequest, user, { params }: RouteParams) => {
  try {
   
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { emailId } = params

   
    const existingEmail = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: user.id
      }
    })

    if (!existingEmail) {
      return notFoundResponse('Email not found')
    }

   
    const body = await request.json()
    const { isRead, isImportant } = body

   
    const updatedEmail = await prisma.email.update({
      where: { id: emailId },
      data: {
        ...(isRead !== undefined && { isRead }),
        ...(isImportant !== undefined && { isImportant }),
        updatedAt: new Date()
      },
      include: {
        reminders: true
      }
    })

   
    if (isRead !== undefined) {
      try {
        const { gmail } = await getGoogleServices(user.id)
        
        console.log(`Email ${emailId} marked as ${isRead ? 'read' : 'unread'}`)
      } catch (error) {
        console.warn('Failed to update email status in Gmail:', error)
      }
    }

    return successResponse({
      email: updatedEmail
    }, 'Email updated successfully')

  } catch (error) {
    console.error('Update email error:', error)
    return serverErrorResponse('Failed to update email')
  }
})

export const DELETE = requireGoogle(async (request: NextRequest, user, { params }: RouteParams) => {
  try {
 
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { emailId } = params

    
    const existingEmail = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: user.id
      }
    })

    if (!existingEmail) {
      return notFoundResponse('Email not found')
    }

   
    await prisma.email.delete({
      where: { id: emailId }
    })

    try {
      const { gmail } = await getGoogleServices(user.id)
      console.log(`Email ${emailId} deleted from database`)
    } catch (error) {
      console.warn('Failed to delete email from Gmail:', error)
    }

    return successResponse({
      deleted: true,
      emailId
    }, 'Email deleted successfully')

  } catch (error) {
    console.error('Delete email error:', error)
    return serverErrorResponse('Failed to delete email')
  }
})
