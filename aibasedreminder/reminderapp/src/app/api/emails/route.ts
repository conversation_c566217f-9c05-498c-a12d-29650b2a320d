import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireGoogle } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { getGoogleServices } from '@/lib/google'
import { prisma } from '@/lib/prisma'

export const GET = requireGoogle(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || 'is:unread'
    const maxResults = parseInt(searchParams.get('maxResults') || '20') // Reduced for faster loading
    const syncToDatabase = searchParams.get('sync') === 'true'
    const includeRead = searchParams.get('includeRead') === 'true'
    const labelIds = searchParams.get('labelIds')?.split(',')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    const { gmail } = await getGoogleServices(user.id)

    const gmailMessages = await gmail.getMessages(query, maxResults)

    if (syncToDatabase) {
      await syncEmailsToDatabase(user.id, gmailMessages)
    }

    const whereClause: any = { userId: user.id }
    
    if (!includeRead) {
      whereClause.isRead = false
    }

    const dbEmails = await prisma.email.findMany({
      where: whereClause,
      include: {
        reminders: {
          orderBy: { scheduledFor: 'asc' }
        }
      },
      orderBy: { receivedAt: 'desc' },
      take: limit,
      skip: offset
    })

    const totalCount = await prisma.email.count({ where: whereClause })

    const categorizedEmails = categorizeEmails(dbEmails)

    return successResponse({
      emails: dbEmails,
      gmailMessages: gmailMessages.slice(0, maxResults),
      categories: categorizedEmails,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      },
      query,
      synced: syncToDatabase
    }, 'Emails retrieved successfully')

  } catch (error) {
    console.error('Get emails error:', error)
    return serverErrorResponse('Failed to fetch emails')
  }
})

export const POST = requireGoogle(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const body = await request.json()
    const { to, cc, bcc, subject, htmlBody, textBody, threadId, replyToMessageId } = body

    if (!to || !Array.isArray(to) || to.length === 0) {
      return errorResponse('Recipients (to) are required')
    }

    if (!subject) {
      return errorResponse('Subject is required')
    }

    if (!htmlBody && !textBody) {
      return errorResponse('Email body (htmlBody or textBody) is required')
    }

    const { gmail } = await getGoogleServices(user.id)

    const emailContent = {
      to,
      cc: cc || [],
      subject,
      body: htmlBody || textBody
    }

    await gmail.sendReply(replyToMessageId || '', threadId || '', emailContent)

    console.log(`Email sent by user ${user.email} to ${to.join(', ')}`)

    return successResponse({
      sent: true,
      to,
      subject,
      threadId
    }, 'Email sent successfully')

  } catch (error) {
    console.error('Send email error:', error)
    return serverErrorResponse('Failed to send email')
  }
})

async function syncEmailsToDatabase(userId: string, gmailMessages: any[]) {
  try {
    for (const gmailMessage of gmailMessages) {
      const headers = gmailMessage.payload?.headers || []
      const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
      const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown Sender'
      const to = headers.find((h: any) => h.name === 'To')?.value?.split(',').map((email: string) => email.trim()) || []
      const cc = headers.find((h: any) => h.name === 'Cc')?.value?.split(',').map((email: string) => email.trim()) || []
      const receivedAt = new Date(parseInt(gmailMessage.internalDate))

      let body = ''
      if (gmailMessage.payload?.body?.data) {
        body = Buffer.from(gmailMessage.payload.body.data, 'base64').toString()
      } else if (gmailMessage.payload?.parts) {
        const textPart = gmailMessage.payload.parts.find((part: any) => part.mimeType === 'text/plain')
        if (textPart?.body?.data) {
          body = Buffer.from(textPart.body.data, 'base64').toString()
        }
      }

      const isRead = !gmailMessage.labelIds?.includes('UNREAD')
      const isImportant = gmailMessage.labelIds?.includes('IMPORTANT') || false
      const hasAttachments = gmailMessage.payload?.parts?.some((part: any) => part.filename) || false

      await prisma.email.upsert({
        where: {
          gmailMessageId: gmailMessage.id
        },
        update: {
          subject,
          from,
          to,
          cc,
          body,
          isRead,
          isImportant,
          hasAttachments,
          updatedAt: new Date()
        },
        create: {
          userId,
          gmailMessageId: gmailMessage.id,
          threadId: gmailMessage.threadId,
          subject,
          from,
          to,
          cc,
          body,
          isRead,
          isImportant,
          hasAttachments,
          receivedAt
        }
      })
    }
  } catch (error) {
    console.error('Sync emails to database error:', error)
    throw error
  }
}

function categorizeEmails(emails: any[]) {
  const categories = {
    urgent: [] as any[],
    important: [] as any[],
    normal: [] as any[],
    lowPriority: [] as any[]
  }

  emails.forEach(email => {
    const now = new Date()
    const emailAge = now.getTime() - new Date(email.receivedAt).getTime()
    const hoursOld = emailAge / (1000 * 60 * 60)

    if (email.isImportant || email.from.includes('boss') || email.subject.toLowerCase().includes('urgent')) {
      categories.urgent.push(email)
    } else if (hoursOld > 24 && !email.isRead) {
      categories.important.push(email)
    } else if (hoursOld > 72) {
      categories.lowPriority.push(email)
    } else {
      categories.normal.push(email)
    }
  })

  return categories
}
