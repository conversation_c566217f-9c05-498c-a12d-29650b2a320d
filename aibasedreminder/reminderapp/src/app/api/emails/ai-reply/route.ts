import { NextRequest } from 'next/server'
import { successResponse, errorResponse, forbiddenResponse, notFoundResponse, serverErrorResponse } from '@/utils/response'
import { requirePro } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { generateEmailReply } from '@/lib/openai'
import { prisma } from '@/lib/prisma'

export const POST = requirePro(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const body = await request.json()
    const { emailId, tone = 'professional', length = 'medium', context, customInstructions } = body

    if (!emailId) {
      return errorResponse('Email ID is required')
    }

    const originalEmail = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: user.id
      }
    })

    if (!originalEmail) {
      return notFoundResponse('Email not found')
    }

    const emailData = {
      originalEmail: {
        subject: originalEmail.subject,
        from: originalEmail.from,
        body: originalEmail.body,
        receivedAt: originalEmail.receivedAt.toISOString()
      },
      context: context || '',
      tone: tone as 'professional' | 'casual' | 'friendly' | 'formal',
      length: length as 'short' | 'medium' | 'long'
    }

    if (customInstructions) {
      emailData.context += `\n\nAdditional instructions: ${customInstructions}`
    }

    const aiReply = await generateEmailReply(emailData)

    console.log(`AI reply generated for user ${user.email}, email ${emailId}`)

    const suggestion = await prisma.reminder.create({
      data: {
        userId: user.id,
        emailId: originalEmail.id,
        type: 'EMAIL',
        title: `AI Reply Suggestion: ${originalEmail.subject}`,
        description: `AI-generated reply suggestion with ${aiReply.confidence}% confidence`,
        scheduledFor: new Date(), 
        status: 'PENDING'
      }
    })

    return successResponse({
      suggestion: {
        id: suggestion.id,
        subject: aiReply.subject,
        body: aiReply.body,
        confidence: aiReply.confidence,
        tone,
        length,
        originalEmailId: emailId
      },
      originalEmail: {
        id: originalEmail.id,
        subject: originalEmail.subject,
        from: originalEmail.from,
        receivedAt: originalEmail.receivedAt
      },
      usage: {
        tone,
        length,
        hasContext: !!context,
        hasCustomInstructions: !!customInstructions
      }
    }, 'AI reply suggestion generated successfully')

  } catch (error) {
    console.error('AI reply generation error:', error)
    return serverErrorResponse('Failed to generate AI reply suggestion')
  }
})

export const GET = requirePro(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    const suggestions = await prisma.reminder.findMany({
      where: {
        userId: user.id,
        type: 'EMAIL',
        title: { startsWith: 'AI Reply Suggestion:' }
      },
      include: {
        email: {
          select: {
            id: true,
            subject: true,
            from: true,
            receivedAt: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset
    })

    const totalCount = await prisma.reminder.count({
      where: {
        userId: user.id,
        type: 'EMAIL',
        title: { startsWith: 'AI Reply Suggestion:' }
      }
    })

    return successResponse({
      suggestions,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      }
    }, 'AI reply suggestions retrieved successfully')

  } catch (error) {
    console.error('Get AI reply suggestions error:', error)
    return serverErrorResponse('Failed to get AI reply suggestions')
  }
})
