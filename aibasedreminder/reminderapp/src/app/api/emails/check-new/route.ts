import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireGoogle } from '@/middleware/auth'
import { getGoogleServices } from '@/lib/google'
import { prisma } from '@/lib/prisma'
import { sendReminderNotification } from '@/lib/telegram'

export const POST = requireGoogle(async (request: NextRequest, user) => {
  try {
    console.log('🔍 Checking for new emails for user:', user.email)
    
    const { gmail } = await getGoogleServices(user.id)
    
    // Get recent unread emails (last 1 hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    const query = `is:unread after:${Math.floor(oneHourAgo.getTime() / 1000)}`
    
    console.log('📧 Gmail query:', query)
    const gmailMessages = await gmail.getMessages(query, 10)
    console.log(`📨 Found ${gmailMessages.length} recent unread emails`)
    
    let newEmailsCount = 0
    let notificationsSent = 0
    
    for (const gmailMessage of gmailMessages) {
      try {
        // Check if we already have this email
        const existingEmail = await prisma.email.findUnique({
          where: { gmailMessageId: gmailMessage.id }
        })
        
        if (existingEmail) {
          console.log(`📧 Email ${gmailMessage.id} already exists, skipping`)
          continue
        }
        
        // Parse email data
        const subject = gmailMessage.payload?.headers?.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
        const from = gmailMessage.payload?.headers?.find((h: any) => h.name === 'From')?.value || 'Unknown Sender'
        const to = gmailMessage.payload?.headers?.find((h: any) => h.name === 'To')?.value?.split(',') || []
        const cc = gmailMessage.payload?.headers?.find((h: any) => h.name === 'Cc')?.value?.split(',') || []
        const receivedAt = new Date(parseInt(gmailMessage.internalDate))
        
        // Extract body
        let body = ''
        if (gmailMessage.payload?.body?.data) {
          body = Buffer.from(gmailMessage.payload.body.data, 'base64').toString('utf-8')
        } else if (gmailMessage.payload?.parts) {
          const textPart = gmailMessage.payload.parts.find((part: any) => part.mimeType === 'text/plain')
          if (textPart?.body?.data) {
            body = Buffer.from(textPart.body.data, 'base64').toString('utf-8')
          }
        }
        
        const isRead = !gmailMessage.labelIds?.includes('UNREAD')
        const isImportant = gmailMessage.labelIds?.includes('IMPORTANT') || false
        const hasAttachments = gmailMessage.payload?.parts?.some((part: any) => part.filename) || false
        
        // Create email in database
        const newEmail = await prisma.email.create({
          data: {
            userId: user.id,
            gmailMessageId: gmailMessage.id,
            threadId: gmailMessage.threadId,
            subject,
            from,
            to,
            cc,
            body: body.substring(0, 5000), // Limit body length
            isRead,
            isImportant,
            hasAttachments,
            receivedAt,
            reminderSent: false
          }
        })
        
        newEmailsCount++
        console.log(`✅ Created new email: ${subject} from ${from}`)
        
        // Send immediate Telegram notification
        const notification = await sendReminderNotification(user.id, {
          id: `email-${newEmail.id}`,
          title: isImportant ? `🚨 Important Email: ${subject}` : `📧 New Email: ${subject}`,
          description: `From: ${from}\n\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}`,
          type: 'email',
          scheduledFor: new Date()
        })
        
        if (notification) {
          notificationsSent++
          console.log(`📱 Sent Telegram notification for email: ${subject}`)
          
          // Mark as notification sent
          await prisma.email.update({
            where: { id: newEmail.id },
            data: { reminderSent: true }
          })
        }
        
      } catch (error) {
        console.error(`❌ Error processing email ${gmailMessage.id}:`, error)
      }
    }
    
    console.log(`✅ Email check complete: ${newEmailsCount} new emails, ${notificationsSent} notifications sent`)
    
    return successResponse({
      newEmailsFound: newEmailsCount,
      notificationsSent,
      totalChecked: gmailMessages.length,
      timestamp: new Date().toISOString()
    }, `Found ${newEmailsCount} new emails, sent ${notificationsSent} notifications`)
    
  } catch (error) {
    console.error('❌ Check new emails error:', error)
    return serverErrorResponse('Failed to check for new emails')
  }
})
