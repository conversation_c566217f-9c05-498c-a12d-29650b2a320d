import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireGoogle } from '@/middleware/auth'
import { prisma } from '@/lib/prisma'

export const GET = requireGoogle(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category') || 'all'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    console.log(`📧 Fetching emails for user ${user.email}, category: ${category}, page: ${page}`)

    // Build where clause based on category
    let whereClause: any = { userId: user.id }
    
    switch (category) {
      case 'unread':
        whereClause.isRead = false
        break
      case 'starred':
        whereClause.isStarred = true
        break
      case 'important':
        whereClause.isImportant = true
        break
      case 'spam':
        whereClause.category = 'spam'
        break
      case 'promotions':
        whereClause.category = 'promotions'
        break
      case 'social':
        whereClause.category = 'social'
        break
      case 'updates':
        whereClause.category = 'updates'
        break
      case 'inbox':
        whereClause.category = 'inbox'
        break
      // 'all' doesn't add any additional filters
    }

    // Get emails with pagination
    const [emails, totalCount] = await Promise.all([
      prisma.email.findMany({
        where: whereClause,
        orderBy: { receivedAt: 'desc' },
        skip: offset,
        take: limit,
        select: {
          id: true,
          gmailMessageId: true,
          threadId: true,
          subject: true,
          from: true,
          to: true,
          snippet: true,
          receivedAt: true,
          isRead: true,
          isStarred: true,
          isImportant: true,
          category: true,
          labels: true,
          lastReminderSent: true
        }
      }),
      prisma.email.count({ where: whereClause })
    ])

    // Get category counts for the sidebar
    const categoryCounts = await prisma.email.groupBy({
      by: ['category', 'isRead', 'isStarred', 'isImportant'],
      where: { userId: user.id },
      _count: true
    })

    // Process category counts
    const counts = {
      all: totalCount,
      unread: 0,
      starred: 0,
      important: 0,
      inbox: 0,
      spam: 0,
      promotions: 0,
      social: 0,
      updates: 0
    }

    categoryCounts.forEach(group => {
      if (!group.isRead) counts.unread += group._count
      if (group.isStarred) counts.starred += group._count
      if (group.isImportant) counts.important += group._count
      
      switch (group.category) {
        case 'inbox':
          counts.inbox += group._count
          break
        case 'spam':
          counts.spam += group._count
          break
        case 'promotions':
          counts.promotions += group._count
          break
        case 'social':
          counts.social += group._count
          break
        case 'updates':
          counts.updates += group._count
          break
      }
    })

    const totalPages = Math.ceil(totalCount / limit)

    return successResponse({
      emails,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      categoryCounts: counts,
      category
    }, 'Emails fetched successfully')

  } catch (error) {
    console.error('❌ Error fetching emails:', error)
    return serverErrorResponse(`Failed to fetch emails: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
})

export const POST = GET
