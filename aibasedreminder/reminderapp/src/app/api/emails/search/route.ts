import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireGoogle } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { prisma } from '@/lib/prisma'

export const GET = requireGoogle(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const from = searchParams.get('from')
    const subject = searchParams.get('subject')
    const isRead = searchParams.get('isRead')
    const isImportant = searchParams.get('isImportant')
    const hasAttachments = searchParams.get('hasAttachments')
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    if (!query && !from && !subject && isRead === null && isImportant === null) {
      return errorResponse('At least one search parameter is required')
    }

    const where: any = {
      userId: user.id
    }

    if (isRead !== null) where.isRead = isRead === 'true'
    if (isImportant !== null) where.isImportant = isImportant === 'true'
    if (hasAttachments !== null) where.hasAttachments = hasAttachments === 'true'

    if (dateFrom || dateTo) {
      where.receivedAt = {}
      if (dateFrom) where.receivedAt.gte = new Date(dateFrom)
      if (dateTo) where.receivedAt.lte = new Date(dateTo)
    }

    const textSearchConditions = []

    if (query) {
      const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0)
      
      for (const term of searchTerms) {
        textSearchConditions.push({
          OR: [
            { subject: { contains: term, mode: 'insensitive' } },
            { body: { contains: term, mode: 'insensitive' } },
            { from: { contains: term, mode: 'insensitive' } }
          ]
        })
      }
    }

    if (from) {
      textSearchConditions.push({
        from: { contains: from, mode: 'insensitive' }
      })
    }

    if (subject) {
      textSearchConditions.push({
        subject: { contains: subject, mode: 'insensitive' }
      })
    }

    if (textSearchConditions.length > 0) {
      where.AND = textSearchConditions
    }

    const [emails, totalCount] = await Promise.all([
      prisma.email.findMany({
        where,
        include: {
          reminders: {
            select: {
              id: true,
              status: true,
              scheduledFor: true
            },
            orderBy: { scheduledFor: 'asc' }
          }
        },
        orderBy: [
          { receivedAt: 'desc' }
        ],
        take: limit,
        skip: offset
      }),
      prisma.email.count({ where })
    ])

    const emailsWithScore = emails.map(email => {
      let relevanceScore = 0

      if (query) {
        const searchTerms = query.toLowerCase().split(' ')
        const subjectLower = email.subject.toLowerCase()
        const bodyLower = email.body.toLowerCase()
        const fromLower = email.from.toLowerCase()

        searchTerms.forEach(term => {
          if (subjectLower.includes(term)) relevanceScore += 10
          if (bodyLower.includes(term)) relevanceScore += 5
          if (fromLower.includes(term)) relevanceScore += 8
        })

        if (subjectLower === query.toLowerCase()) relevanceScore += 20
      }

      if (from && email.from.toLowerCase().includes(from.toLowerCase())) {
        relevanceScore += 15
      }

      if (subject && email.subject.toLowerCase().includes(subject.toLowerCase())) {
        relevanceScore += 15
      }

      if (email.isImportant) relevanceScore += 5

      if (!email.isRead) relevanceScore += 3

      return {
        ...email,
        relevanceScore
      }
    })

    emailsWithScore.sort((a, b) => {
      if (a.relevanceScore !== b.relevanceScore) {
        return b.relevanceScore - a.relevanceScore
      }
      return new Date(b.receivedAt).getTime() - new Date(a.receivedAt).getTime()
    })

    const results = {
      highRelevance: emailsWithScore.filter(e => e.relevanceScore >= 20),
      mediumRelevance: emailsWithScore.filter(e => e.relevanceScore >= 10 && e.relevanceScore < 20),
      lowRelevance: emailsWithScore.filter(e => e.relevanceScore > 0 && e.relevanceScore < 10),
      noScore: emailsWithScore.filter(e => e.relevanceScore === 0)
    }

    return successResponse({
      emails: emailsWithScore,
      results,
      searchParams: {
        query,
        from,
        subject,
        isRead,
        isImportant,
        hasAttachments,
        dateFrom,
        dateTo
      },
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      },
      stats: {
        totalFound: totalCount,
        highRelevance: results.highRelevance.length,
        mediumRelevance: results.mediumRelevance.length,
        lowRelevance: results.lowRelevance.length
      }
    }, 'Email search completed successfully')

  } catch (error) {
    console.error('Email search error:', error)
    return serverErrorResponse('Failed to search emails')
  }
})

export const POST = requireGoogle(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const {
      textQuery,
      senders,
      recipients,
      dateRange,
      readStatus,
      importance,
      attachments,
      reminderStatus,
      sortBy = 'relevance', 
      sortOrder = 'desc'
    } = body

    const where: any = {
      userId: user.id
    }

    if (dateRange) {
      where.receivedAt = {}
      if (dateRange.start) where.receivedAt.gte = new Date(dateRange.start)
      if (dateRange.end) where.receivedAt.lte = new Date(dateRange.end)
    }

    if (readStatus !== undefined) where.isRead = readStatus
    if (importance !== undefined) where.isImportant = importance
    if (attachments !== undefined) where.hasAttachments = attachments

    if (textQuery) {
      const searchTerms = textQuery.toLowerCase().split(' ').filter(term => term.length > 0)
      where.AND = searchTerms.map(term => ({
        OR: [
          { subject: { contains: term, mode: 'insensitive' } },
          { body: { contains: term, mode: 'insensitive' } },
          { from: { contains: term, mode: 'insensitive' } }
        ]
      }))
    }

    if (senders && senders.length > 0) {
      where.OR = senders.map((sender: string) => ({
        from: { contains: sender, mode: 'insensitive' }
      }))
    }

    if (recipients && recipients.length > 0) {
      where.OR = recipients.map((recipient: string) => ({
        OR: [
          { to: { has: recipient } },
          { cc: { has: recipient } }
        ]
      }))
    }

    let emails = await prisma.email.findMany({
      where,
      include: {
        reminders: true
      },
      orderBy: { receivedAt: 'desc' }
    })

    if (reminderStatus) {
      emails = emails.filter(email => {
        const hasReminders = email.reminders && email.reminders.length > 0
        if (reminderStatus === 'with_reminders') return hasReminders
        if (reminderStatus === 'without_reminders') return !hasReminders
        if (reminderStatus === 'completed_reminders') {
          return hasReminders && email.reminders.some(r => r.status === 'COMPLETED')
        }
        return true
      })
    }

    if (sortBy === 'date') {
      emails.sort((a, b) => {
        const comparison = new Date(a.receivedAt).getTime() - new Date(b.receivedAt).getTime()
        return sortOrder === 'desc' ? -comparison : comparison
      })
    } else if (sortBy === 'sender') {
      emails.sort((a, b) => {
        const comparison = a.from.localeCompare(b.from)
        return sortOrder === 'desc' ? -comparison : comparison
      })
    }

    return successResponse({
      emails,
      searchCriteria: body,
      totalFound: emails.length
    }, 'Advanced email search completed successfully')

  } catch (error) {
    console.error('Advanced email search error:', error)
    return serverErrorResponse('Failed to perform advanced email search')
  }
})
