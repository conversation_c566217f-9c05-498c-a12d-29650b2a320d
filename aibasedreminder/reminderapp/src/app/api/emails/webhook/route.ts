import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('📧 Gmail webhook triggered - checking for new emails immediately')
    
    // Trigger immediate email sync
    const response = await fetch('http://localhost:3001/api/emails/sync-immediate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()
    
    console.log('📧 Webhook email sync result:', result)

    return NextResponse.json({
      success: true,
      message: 'Email webhook processed successfully',
      syncResult: result
    })

  } catch (error) {
    console.error('Error processing email webhook:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to process email webhook'
    }, { status: 500 })
  }
}

// Handle Gmail push notifications
export async function GET(request: NextRequest) {
  return NextResponse.json({
    success: true,
    message: 'Email webhook endpoint is active'
  })
}
