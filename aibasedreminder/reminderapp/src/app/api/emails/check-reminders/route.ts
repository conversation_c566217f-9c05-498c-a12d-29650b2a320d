import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    console.log('⏰ Checking for emails that need reminders...')
    
    // Get all unread emails that haven't been reminded in the last 5 minutes
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    
    const emailsNeedingReminders = await prisma.email.findMany({
      where: {
        isRead: false,
        OR: [
          { lastReminderSent: null },
          { lastReminderSent: { lt: fiveMinutesAgo } }
        ]
      },
      include: {
        user: {
          include: {
            telegramUsers: {
              where: { isActive: true }
            }
          }
        }
      },
      take: 20 // Limit to prevent overwhelming
    })

    console.log(`📧 Found ${emailsNeedingReminders.length} emails needing reminders`)
    
    let remindersSent = 0

    for (const email of emailsNeedingReminders) {
      try {
        if (email.user.telegramUsers.length === 0) {
          continue // Skip if no Telegram users
        }

        // Send reminder to all user's Telegram accounts
        for (const telegramUser of email.user.telegramUsers) {
          const reminderMessage = `⏰ REMINDER: You still have an unread email!\n\n📧 From: ${email.from}\n📝 Subject: ${email.subject}\n⏰ Received: ${email.receivedAt.toLocaleString()}\n\n💬 Reply: reply:${email.id}:Your message here\n📱 Example: reply:${email.id}:Thanks for reaching out!\n\n📧 Or mark as read in Gmail to stop reminders.`
          
          // Use direct Telegram API call
          await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              chat_id: telegramUser.telegramId,
              text: reminderMessage
            })
          })
          
          remindersSent++
          console.log(`⏰ Sent reminder to Telegram user ${telegramUser.telegramId} for email ${email.id}`)
        }
        
        // Update last reminder time
        await prisma.email.update({
          where: { id: email.id },
          data: { lastReminderSent: new Date() }
        })
        
      } catch (emailError) {
        console.error(`❌ Error sending reminder for email ${email.id}:`, emailError)
      }
    }
    
    console.log(`🎉 Reminder check completed: ${remindersSent} reminders sent`)
    
    return successResponse({
      emailsChecked: emailsNeedingReminders.length,
      remindersSent,
      timestamp: new Date().toISOString()
    }, 'Reminder check completed successfully')
    
  } catch (error) {
    console.error('❌ Reminder check failed:', error)
    return serverErrorResponse(`Reminder check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const GET = POST
