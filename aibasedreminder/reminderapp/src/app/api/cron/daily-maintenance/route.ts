import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Running daily maintenance...')

    // Set Telegram webhook to ensure it's always active
    const webhookResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/setWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: `${process.env.NEXTAUTH_URL}/api/telegram/webhook`,
        allowed_updates: ['message', 'callback_query']
      })
    })

    const webhookResult = await webhookResponse.json()
    console.log('📱 Telegram webhook maintenance:', webhookResult)

    // Trigger initial continuous monitoring
    const monitoringResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/cron/continuous-monitor`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })

    let monitoringResult = { message: 'Continuous monitoring endpoint not available' }
    if (monitoringResponse.ok) {
      monitoringResult = await monitoringResponse.json()
    }

    console.log('📧 Daily continuous monitoring trigger:', monitoringResult)

    // Log instructions for external monitoring setup
    console.log('💡 For continuous email monitoring, set up external service to ping:')
    console.log(`   ${process.env.NEXTAUTH_URL}/api/cron/continuous-monitor every 5 minutes`)

    return successResponse({
      webhook: webhookResult,
      monitoring: monitoringResult,
      timestamp: new Date().toISOString(),
      instructions: 'Set up external monitoring service to ping /api/cron/continuous-monitor every 5 minutes'
    }, 'Daily maintenance completed')
    
  } catch (error) {
    console.error('❌ Daily maintenance failed:', error)
    return serverErrorResponse(`Daily maintenance failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
