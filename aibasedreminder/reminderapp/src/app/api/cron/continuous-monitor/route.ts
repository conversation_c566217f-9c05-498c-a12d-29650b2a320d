import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Starting continuous email monitoring...')

    // Add CORS headers for external triggers
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    }

    // Get all users with valid Google OAuth tokens and Telegram connections
    const users = await prisma.user.findMany({
      where: {
        oAuthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        },
        telegramUsers: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oAuthTokens: {
          where: {
            provider: 'google'
          }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with valid tokens and Telegram connections`)
    
    let totalNewEmails = 0
    let totalNotifications = 0

    for (const user of users) {
      try {
        console.log(`📧 Checking emails for user: ${user.email}`)
        
        const { gmail } = await getGoogleServices(user.id)
        
        // Get recent emails (last 5 minutes for continuous monitoring)
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
        const newEmailsQuery = `is:unread after:${Math.floor(fiveMinutesAgo.getTime() / 1000)}`
        const allUnreadQuery = `is:unread`

        // Get new emails first
        const newGmailMessages = await gmail.getMessages(newEmailsQuery, 10)
        // Get all unread emails for reminder checking
        const allUnreadMessages = await gmail.getMessages(allUnreadQuery, 30)
        console.log(`📨 Found ${newGmailMessages.length} new emails and ${allUnreadMessages.length} total unread emails for ${user.email}`)

        let userNewEmails = 0
        let userNotifications = 0

        // Process new emails first
        for (const gmailMessage of newGmailMessages) {
          try {
            // Check if we already have this email
            const existingEmail = await prisma.email.findUnique({
              where: { gmailMessageId: gmailMessage.id }
            })
            
            if (existingEmail) {
              continue // Skip if already processed
            }

            // Get email details
            const emailData = await gmail.getEmailDetails(gmailMessage.id)
            
            // Create new email record
            const newEmail = await prisma.email.create({
              data: {
                userId: user.id,
                gmailMessageId: gmailMessage.id,
                threadId: emailData.threadId,
                subject: emailData.subject,
                from: emailData.from,
                to: emailData.to,
                body: emailData.body,
                receivedAt: emailData.receivedAt,
                isRead: emailData.isRead,
                isImportant: emailData.isImportant,
                labels: JSON.stringify(emailData.labels || []),
                reminderSent: false
              }
            })

            userNewEmails++
            console.log(`✅ Created new email: ${emailData.subject} from ${emailData.from}`)
            
            // Send immediate Telegram notification
            for (const telegramUser of user.telegramUsers) {
              const notificationMessage = `📧 New Email Received!\n\n` +
                `📝 Subject: ${emailData.subject}\n` +
                `👤 From: ${emailData.from}\n` +
                `📅 Received: ${emailData.receivedAt.toLocaleString()}\n\n` +
                `${emailData.body.substring(0, 200)}${emailData.body.length > 200 ? '...' : ''}\n\n` +
                `💬 Reply: reply:${newEmail.id}:Your message here\n` +
                `📧 Or check Gmail: https://gmail.com`
              
              // Use direct Telegram API call for reliability
              await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  chat_id: telegramUser.telegramId,
                  text: notificationMessage,
                  parse_mode: 'HTML'
                })
              })
              
              userNotifications++
              console.log(`📱 Sent notification to Telegram user ${telegramUser.telegramId}`)
            }
            
          } catch (emailError) {
            console.error(`❌ Error processing email ${gmailMessage.id}:`, emailError)
          }
        }

        // Process recurring reminders for unread emails
        for (const gmailMessage of allUnreadMessages) {
          try {
            const existingEmail = await prisma.email.findUnique({
              where: { gmailMessageId: gmailMessage.id }
            })

            if (existingEmail && !existingEmail.isRead) {
              // Check if we need to send recurring reminder (every 5 minutes)
              const lastReminderTime = existingEmail.lastReminderSent
              const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)

              if (!lastReminderTime || lastReminderTime < fiveMinutesAgo) {
                console.log(`⏰ Sending recurring reminder for email: ${existingEmail.subject}`)

                // Send recurring reminder
                for (const telegramUser of user.telegramUsers) {
                  const reminderMessage = `⏰ REMINDER: You still have an unread email!\n\n📧 From: ${existingEmail.from}\n📝 Subject: ${existingEmail.subject}\n⏰ Received: ${existingEmail.receivedAt.toLocaleString()}\n\n💬 Reply: reply:${existingEmail.id}:Your message here\n📧 Or mark as read in Gmail to stop reminders.`

                  // Use direct Telegram API call for reliability
                  await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      chat_id: telegramUser.telegramId,
                      text: reminderMessage,
                      parse_mode: 'HTML'
                    })
                  })
                  userNotifications++
                }

                // Update last reminder time
                await prisma.email.update({
                  where: { id: existingEmail.id },
                  data: { lastReminderSent: new Date() }
                })
              }
            }
          } catch (reminderError) {
            console.error(`❌ Error processing reminder for email ${gmailMessage.id}:`, reminderError)
          }
        }
        
        totalNewEmails += userNewEmails
        totalNotifications += userNotifications
        
        console.log(`✅ User ${user.email}: ${userNewEmails} new emails, ${userNotifications} notifications sent`)
        
      } catch (userError) {
        console.error(`❌ Error checking emails for user ${user.email}:`, userError)
      }
    }
    
    console.log(`🎉 Continuous monitoring completed: ${totalNewEmails} new emails, ${totalNotifications} notifications sent`)

    const response = successResponse({
      usersChecked: users.length,
      newEmails: totalNewEmails,
      notificationsSent: totalNotifications,
      timestamp: new Date().toISOString(),
      monitoringType: 'continuous'
    }, 'Continuous email monitoring completed successfully')

    // Add headers for CORS and caching
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value)
    })

    return response
    
  } catch (error) {
    console.error('❌ Continuous email monitoring failed:', error)
    return serverErrorResponse(`Continuous monitoring failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Also allow POST for manual triggers
export const POST = GET
