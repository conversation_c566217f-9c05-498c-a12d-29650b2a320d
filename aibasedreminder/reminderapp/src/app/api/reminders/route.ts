import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { validateRequest, reminderCreateSchema } from '@/utils/validation'
import { requireAuth } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { prisma } from '@/lib/prisma'

export const GET = requireAuth(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const type = searchParams.get('type')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    const where: any = { userId: user.id }
    if (status) where.status = status.toUpperCase()
    if (type) where.type = type.toUpperCase()

    const reminders = await prisma.reminder.findMany({
      where,
      include: {
        event: true,
        email: true
      },
      orderBy: { scheduledFor: 'asc' },
      take: limit,
      skip: offset
    })

    const total = await prisma.reminder.count({ where })

    return successResponse({
      reminders,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    })

  } catch (error) {
    console.error('Get reminders error:', error)
    return serverErrorResponse('Failed to fetch reminders')
  }
})

export const POST = requireAuth(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const body = await request.json()
    const validation = validateRequest(reminderCreateSchema, body)
    
    if (!validation.success) {
      return errorResponse(validation.error)
    }

    const { type, title, description, scheduledFor, eventId, emailId } = validation.data

    const reminder = await prisma.reminder.create({
      data: {
        userId: user.id,
        type: type.toUpperCase() as any,
        title,
        description,
        scheduledFor: new Date(scheduledFor),
        eventId,
        emailId
      },
      include: {
        event: true,
        email: true
      }
    })

    return successResponse(reminder, 'Reminder created successfully')

  } catch (error) {
    console.error('Create reminder error:', error)
    return serverErrorResponse('Failed to create reminder')
  }
})
