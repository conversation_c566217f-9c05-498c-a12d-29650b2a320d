import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

// DEPLOYMENT HOOK - Activates bot automatically on every deployment
export async function GET(request: NextRequest) {
  try {
    console.log('🚀 DEPLOYMENT HOOK: Activating bot system...')

    // Wait a bit for the deployment to stabilize
    await new Promise(resolve => setTimeout(resolve, 3000))

    // Activate the auto-activate system
    const activateResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/auto-activate`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })

    if (activateResponse.ok) {
      const result = await activateResponse.json()
      console.log('✅ DEPLOYMENT HOOK: Bot activated successfully')
      
      return successResponse({
        deploymentHook: true,
        botActivated: true,
        timestamp: new Date().toISOString(),
        activationResult: result
      }, 'Deployment hook executed - bot is now active')
    } else {
      console.error('❌ DEPLOYMENT HOOK: Failed to activate bot')
      return serverErrorResponse('Failed to activate bot via deployment hook')
    }

  } catch (error) {
    console.error('❌ DEPLOYMENT HOOK error:', error)
    return serverErrorResponse(`Deployment hook failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// POST endpoint for Vercel deployment webhooks
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 VERCEL DEPLOYMENT WEBHOOK: Received deployment notification')

    const body = await request.json()
    console.log('📦 Deployment data:', body)

    // Wait for deployment to complete
    await new Promise(resolve => setTimeout(resolve, 10000)) // 10 seconds

    // Activate the bot system
    const activateResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/auto-activate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })

    if (activateResponse.ok) {
      console.log('✅ VERCEL WEBHOOK: Bot activated after deployment')
      
      return successResponse({
        vercelWebhook: true,
        botActivated: true,
        timestamp: new Date().toISOString()
      }, 'Vercel webhook processed - bot activated')
    } else {
      console.error('❌ VERCEL WEBHOOK: Failed to activate bot')
      return serverErrorResponse('Failed to activate bot via Vercel webhook')
    }

  } catch (error) {
    console.error('❌ VERCEL WEBHOOK error:', error)
    return serverErrorResponse(`Vercel webhook failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
