import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'

// Working continuous reminder system - sends reminders every 5 minutes for unread emails
export async function GET() {
  try {
    console.log('🔄 Running WORKING continuous reminder system...')

    // Get all unread emails that need reminders (5+ minutes old)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    
    const unreadEmails = await prisma.email.findMany({
      where: {
        isRead: false,
        createdAt: {
          lt: fiveMinutesAgo // Only emails older than 5 minutes
        }
      },
      include: {
        user: {
          include: {
            telegramUsers: {
              where: { isActive: true }
            }
          }
        }
      },
      orderBy: {
        receivedAt: 'desc'
      },
      take: 50 // Limit to prevent overwhelming
    })

    console.log(`📧 Found ${unreadEmails.length} unread emails needing reminders`)

    let remindersSent = 0

    for (const email of unreadEmails) {
      try {
        // Check if we should send a reminder (every 5 minutes)
        const lastUpdate = email.updatedAt || email.createdAt
        const timeSinceLastUpdate = Date.now() - lastUpdate.getTime()

        // Only send reminder if 5 minutes have passed since last update
        if (timeSinceLastUpdate < 5 * 60 * 1000) {
          console.log(`⏰ Skipping email ${email.id} - last update too recent`)
          continue
        }

        // Send reminder to all active Telegram users for this email's user
        for (const telegramUser of email.user.telegramUsers) {
          try {
            const reminderMessage = `🔔 EMAIL REMINDER!\n\n` +
              `📝 Subject: ${email.subject}\n` +
              `👤 From: ${email.from}\n` +
              `📅 Received: ${email.receivedAt.toLocaleString()}\n` +
              `⏰ This email still needs your attention!\n\n` +
              `📄 Preview:\n${email.body.substring(0, 200)}${email.body.length > 200 ? '...' : ''}\n\n` +
              `💬 Please respond or mark as read to stop reminders!`

            // Create action buttons
            const keyboard = {
              inline_keyboard: [
                [
                  { text: "💬 Reply to this Email", callback_data: `reply_to_${email.id}` },
                  { text: "✅ Mark as Read", callback_data: `mark_read_${email.id}` }
                ],
                [
                  { text: "⭐ Star Email", callback_data: `star_email_${email.id}` },
                  { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${email.id}` }
                ]
              ]
            }

            console.log(`🔔 Sending reminder to Telegram user ${telegramUser.telegramId}`)

            const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                chat_id: telegramUser.telegramId,
                text: reminderMessage,
                reply_markup: keyboard
              })
            })
            
            const result = await response.json()
            
            if (result.ok) {
              remindersSent++
              console.log(`🔔 ✅ Reminder sent successfully to ${telegramUser.telegramId}`)
              
              // Update email timestamp to prevent spam
              await prisma.email.update({
                where: { id: email.id },
                data: { updatedAt: new Date() }
              })
              
            } else {
              console.error(`🔔 ❌ Failed to send reminder:`, result)
              
              // Try fallback simple message
              const fallbackResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  chat_id: telegramUser.telegramId,
                  text: `🔔 REMINDER: Unread email from ${email.from}\nSubject: ${email.subject}\n\nUse /emails to view and respond.`
                })
              })
              
              const fallbackResult = await fallbackResponse.json()
              if (fallbackResult.ok) {
                remindersSent++
                console.log(`🔔 ✅ Fallback reminder sent to ${telegramUser.telegramId}`)
                
                // Update timestamp even for fallback
                await prisma.email.update({
                  where: { id: email.id },
                  data: { updatedAt: new Date() }
                })
              }
            }

          } catch (notificationError) {
            console.error(`🔔 ❌ Network error sending reminder:`, notificationError)
          }

          // Add delay between notifications to prevent flooding
          await new Promise(resolve => setTimeout(resolve, 1000))
        }

        // Add delay between emails to prevent overwhelming
        await new Promise(resolve => setTimeout(resolve, 500))

      } catch (emailError) {
        console.error(`❌ Error processing email ${email.id}:`, emailError)
      }
    }

    console.log(`🔔 Working reminders complete: ${remindersSent} reminders sent`)

    return successResponse({
      remindersSent,
      emailsChecked: unreadEmails.length,
      timestamp: new Date().toISOString()
    }, `Sent ${remindersSent} reminders for ${unreadEmails.length} unread emails`)

  } catch (error) {
    console.error('❌ Working reminder system error:', error)
    return serverErrorResponse(`Working reminder system failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Test endpoint
export async function POST() {
  try {
    console.log('🧪 Testing WORKING continuous reminder system...')
    
    // Call the GET function to test
    const result = await GET()
    return result
    
  } catch (error) {
    console.error('❌ Test working reminder error:', error)
    return serverErrorResponse(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
