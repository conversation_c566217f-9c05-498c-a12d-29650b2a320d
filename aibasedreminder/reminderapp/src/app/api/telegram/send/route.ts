import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireAuth } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { sendMessage } from '@/lib/telegram'
import { prisma } from '@/lib/prisma'

export const POST = requireAuth(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const body = await request.json()
    const { message, parseMode = 'HTML', replyMarkup } = body

    if (!message) {
      return errorResponse('Message is required')
    }

    const telegramUsers = await prisma.telegramUser.findMany({
      where: {
        userId: user.id,
        isActive: true
      }
    })

    if (telegramUsers.length === 0) {
      return errorResponse('No linked Telegram account found', 404)
    }

    const results = []

    for (const telegramUser of telegramUsers) {
      try {
        const messageId = await sendMessage(
          telegramUser.telegramId,
          message,
          {
            parseMode,
            replyMarkup
          }
        )

        results.push({
          telegramId: telegramUser.telegramId,
          username: telegramUser.username,
          success: true,
          messageId
        })
      } catch (error) {
        console.error(`Failed to send message to ${telegramUser.telegramId}:`, error)
        results.push({
          telegramId: telegramUser.telegramId,
          username: telegramUser.username,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    return successResponse({
      sent: successCount > 0,
      results,
      summary: {
        total: results.length,
        success: successCount,
        failed: failureCount
      }
    }, `Message sent to ${successCount} of ${results.length} Telegram accounts`)

  } catch (error) {
    console.error('Send Telegram message error:', error)
    return serverErrorResponse('Failed to send Telegram message')
  }
})
