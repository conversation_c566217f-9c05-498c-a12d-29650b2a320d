import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { sendMessage } from '@/lib/telegram'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { chatId, message } = body

    if (!chatId || !message) {
      return errorResponse('chatId and message are required')
    }

    console.log(`🧪 Testing Telegram message to ${chatId}: ${message}`)
    
    const messageId = await sendMessage(chatId, message)
    
    if (messageId) {
      return successResponse({ 
        sent: true,
        messageId,
        chatId 
      }, 'Test message sent successfully')
    } else {
      return errorResponse('Failed to send test message')
    }
  } catch (error) {
    console.error('❌ Error sending test message:', error)
    return serverErrorResponse('Test message failed')
  }
}

export async function GET() {
  return successResponse({ status: 'Telegram test endpoint ready' }, 'Ready to send test messages')
}
