import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET() {
  try {
    console.log('🚨 FORCE NOTIFY ALL - Processing ALL emails and sending notifications regardless of status')

    // Get all users with active Telegram connections and valid Google tokens
    const users = await prisma.user.findMany({
      where: {
        oauthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        },
        telegramUsers: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with active Telegram and valid Google tokens`)

    let totalEmailsProcessed = 0
    let totalNotificationsSent = 0

    for (const user of users) {
      try {
        console.log(`📧 FORCE PROCESSING ALL emails for: ${user.email}`)

        const { gmail } = await getGoogleServices(user.id)

        // Get ALL unread emails (no limits)
        const unreadMessages = await gmail.getMessages('is:unread', 100)
        console.log(`📨 Found ${unreadMessages.length} unread emails for ${user.email}`)

        let emailsProcessed = 0
        let notificationsSent = 0

        for (const message of unreadMessages) {
          try {
            // Extract email data
            const headers = message.payload?.headers || []
            const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
            const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown'
            const to = headers.find((h: any) => h.name === 'To')?.value || ''
            const dateHeader = headers.find((h: any) => h.name === 'Date')?.value
            const receivedAt = dateHeader ? new Date(dateHeader) : new Date()
            const body = message.snippet || 'No content'

            console.log(`🚨 FORCE PROCESSING: "${subject}" from "${from}"`)

            // Check if email exists in database
            let emailRecord = await prisma.email.findUnique({
              where: { gmailMessageId: message.id }
            })

            // If doesn't exist, create it
            if (!emailRecord) {
              emailRecord = await prisma.email.create({
                data: {
                  userId: user.id,
                  gmailMessageId: message.id,
                  threadId: message.threadId || message.id,
                  subject: subject,
                  from: from,
                  to: to,
                  body: body.substring(0, 1000),
                  receivedAt: receivedAt,
                  isRead: false,
                  isImportant: false,
                  labels: message.labelIds || []
                }
              })
              console.log(`✅ CREATED new email record: ${subject}`)
            } else {
              console.log(`📧 Using existing email record: ${subject}`)
            }

            emailsProcessed++
            totalEmailsProcessed++

            // FORCE SEND NOTIFICATION regardless of previous notifications
            for (const telegramUser of user.telegramUsers) {
              try {
                const timestamp = new Date().toLocaleString()
                const notificationMessage = `🚨 EMAIL NOTIFICATION TEST!\n\n` +
                  `📝 Subject: ${subject}\n` +
                  `👤 From: ${from}\n` +
                  `📅 Received: ${receivedAt.toLocaleString()}\n` +
                  `⚡ Status: UNREAD - Needs Response\n` +
                  `🔍 Test Time: ${timestamp}\n\n` +
                  `📄 Preview:\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}\n\n` +
                  `🎯 THIS IS A FORCED TEST NOTIFICATION!\n` +
                  `If you see this, your system is working! 🎉`

                const keyboard = {
                  inline_keyboard: [
                    [
                      { text: "💬 Reply to Email", callback_data: `reply_to_${emailRecord.id}` },
                      { text: "✅ Mark as Read", callback_data: `mark_read_${emailRecord.id}` }
                    ],
                    [
                      { text: "⭐ Star Email", callback_data: `star_email_${emailRecord.id}` },
                      { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${emailRecord.id}` }
                    ]
                  ]
                }

                console.log(`🚨 FORCE SENDING notification to Telegram user ${telegramUser.telegramId}`)

                const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    chat_id: telegramUser.telegramId,
                    text: notificationMessage,
                    reply_markup: keyboard
                  })
                })

                const result = await response.json()
                if (result.ok) {
                  notificationsSent++
                  totalNotificationsSent++
                  console.log(`🚨 ✅ FORCE NOTIFICATION sent successfully for: ${subject} to ${telegramUser.telegramId}`)
                } else {
                  console.error(`🚨 ❌ Failed to send force notification:`, result)
                  
                  // Try simple fallback message
                  const fallbackResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      chat_id: telegramUser.telegramId,
                      text: `🚨 FORCE TEST: Email from ${from}\nSubject: ${subject}\n\nIf you see this, your Telegram bot is working! ✅`
                    })
                  })
                  
                  const fallbackResult = await fallbackResponse.json()
                  if (fallbackResult.ok) {
                    notificationsSent++
                    totalNotificationsSent++
                    console.log(`🚨 ✅ FALLBACK notification sent to ${telegramUser.telegramId}`)
                  }
                }

              } catch (notificationError) {
                console.error(`🚨 ❌ Error sending force notification:`, notificationError)
              }

              // Add delay between notifications
              await new Promise(resolve => setTimeout(resolve, 1000))
            }

            // Add delay between emails
            await new Promise(resolve => setTimeout(resolve, 500))

          } catch (emailError) {
            console.error(`❌ Error force processing email ${message.id}:`, emailError)
          }
        }

        console.log(`✅ User ${user.email}: ${emailsProcessed} emails processed, ${notificationsSent} notifications sent`)

        // Add delay between users
        await new Promise(resolve => setTimeout(resolve, 2000))

      } catch (userError) {
        console.error(`❌ Error processing user ${user.email}:`, userError)
      }
    }

    console.log(`🎉 FORCE NOTIFY ALL FINISHED: ${totalEmailsProcessed} emails processed, ${totalNotificationsSent} notifications sent`)

    return successResponse({
      usersProcessed: users.length,
      totalEmailsProcessed,
      totalNotificationsSent,
      timestamp: new Date().toISOString(),
      message: "FORCED all email notifications - check your Telegram now!"
    }, `Force notification sent ${totalNotificationsSent} notifications for ${totalEmailsProcessed} emails`)

  } catch (error) {
    console.error('❌ Force notify all failed:', error)
    return serverErrorResponse(`Force notify all failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
