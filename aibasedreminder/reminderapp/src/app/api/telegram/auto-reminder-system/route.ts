import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET() {
  try {
    console.log('🤖 AUTO REMINDER SYSTEM - Processing ALL emails and sending notifications')

    // Get all users with active Telegram connections and valid Google tokens
    const users = await prisma.$queryRaw`
      SELECT
        u.id, u.email, u.name,
        tu."telegramId", tu."isActive" as telegram_active,
        ot."accessToken", ot."refreshToken", ot."expiresAt"
      FROM users u
      JOIN telegram_users tu ON u.id = tu."userId"
      JOIN oauth_tokens ot ON u.id = ot."userId"
      WHERE tu."isActive" = true
        AND ot.provider = 'google'
        AND ot."expiresAt" > NOW()
    ` as any[]

    console.log(`👥 Found ${users.length} users with active Telegram and valid Google tokens`)

    let totalEmailsProcessed = 0
    let totalNotificationsSent = 0
    let totalNewEmails = 0

    for (const user of users) {
      try {
        console.log(`📧 Processing emails for: ${user.email}`)

        const { gmail } = await getGoogleServices(user.id)

        // Get ALL unread emails
        const unreadMessages = await gmail.getMessages('is:unread', 100)
        console.log(`📨 Found ${unreadMessages.length} unread emails for ${user.email}`)

        let userNewEmails = 0
        let userNotifications = 0

        for (const message of unreadMessages) {
          try {
            // Extract email data
            const headers = message.payload?.headers || []
            const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
            const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown'
            const to = headers.find((h: any) => h.name === 'To')?.value || ''
            const dateHeader = headers.find((h: any) => h.name === 'Date')?.value
            const receivedAt = dateHeader ? new Date(dateHeader) : new Date()
            const body = message.snippet || 'No content'

            console.log(`📧 Processing: "${subject}" from "${from}"`)

            // Check if email exists in database
            let emailRecord = await prisma.email.findUnique({
              where: { gmailMessageId: message.id }
            })

            // If doesn't exist, create it
            if (!emailRecord) {
              emailRecord = await prisma.email.create({
                data: {
                  userId: user.id,
                  gmailMessageId: message.id,
                  threadId: message.threadId || message.id,
                  subject: subject,
                  from: from,
                  to: to,
                  body: body.substring(0, 1000),
                  receivedAt: receivedAt,
                  isRead: false,
                  isImportant: false,
                  labels: message.labelIds || []
                }
              })
              console.log(`✅ CREATED new email record: ${subject}`)
              userNewEmails++
              totalNewEmails++
            } else {
              console.log(`📧 Email already exists: ${subject}`)
            }

            totalEmailsProcessed++

            // ALWAYS SEND NOTIFICATION for unread emails
            try {
              const timestamp = new Date().toLocaleString()
              const isUrgent = subject.toLowerCase().includes('urgent') || 
                              subject.toLowerCase().includes('important') ||
                              from.toLowerCase().includes('boss')

              const notificationMessage = `${isUrgent ? '🚨 URGENT' : '📧'} EMAIL NOTIFICATION!\n\n` +
                `📝 Subject: ${subject}\n` +
                `👤 From: ${from}\n` +
                `📅 Received: ${receivedAt.toLocaleString()}\n` +
                `⚡ Status: UNREAD - Needs Response\n` +
                `🕐 Checked: ${timestamp}\n\n` +
                `📄 Preview:\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}\n\n` +
                `💬 Please respond or mark as read!`

              const keyboard = {
                inline_keyboard: [
                  [
                    { text: "💬 Reply to Email", callback_data: `reply_to_${emailRecord.id}` },
                    { text: "✅ Mark as Read", callback_data: `mark_read_${emailRecord.id}` }
                  ],
                  [
                    { text: "⭐ Star Email", callback_data: `star_email_${emailRecord.id}` },
                    { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${emailRecord.id}` }
                  ],
                  [
                    { text: "📧 View All Emails", callback_data: "view_all_emails" },
                    { text: "⚙️ Settings", callback_data: "email_settings" }
                  ]
                ]
              }

              console.log(`📱 Sending notification to Telegram user ${user.telegramId}`)

              const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  chat_id: user.telegramId,
                  text: notificationMessage,
                  reply_markup: keyboard,
                  parse_mode: 'HTML'
                })
              })

              const result = await response.json()
              if (result.ok) {
                userNotifications++
                totalNotificationsSent++
                console.log(`📱 ✅ Notification sent successfully for: ${subject}`)
                
                // Update email timestamp
                await prisma.$executeRaw`
                  UPDATE emails SET "updatedAt" = NOW() WHERE id = ${emailRecord.id}
                `
                
              } else {
                console.error(`📱 ❌ Failed to send notification:`, result)
                
                // Try simple fallback message
                const fallbackResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    chat_id: user.telegramId,
                    text: `📧 New Email: ${subject}\nFrom: ${from}\n\nClick here to view: https://aibasedreminder.vercel.app/emails`
                  })
                })
                
                const fallbackResult = await fallbackResponse.json()
                if (fallbackResult.ok) {
                  userNotifications++
                  totalNotificationsSent++
                  console.log(`📱 ✅ Fallback notification sent for: ${subject}`)
                }
              }

            } catch (notificationError) {
              console.error(`📱 ❌ Error sending notification:`, notificationError)
            }

            // Add delay between notifications
            await new Promise(resolve => setTimeout(resolve, 1000))

          } catch (emailError) {
            console.error(`❌ Error processing email ${message.id}:`, emailError)
          }
        }

        console.log(`✅ User ${user.email}: ${userNewEmails} new emails, ${userNotifications} notifications sent`)

        // Add delay between users
        await new Promise(resolve => setTimeout(resolve, 2000))

      } catch (userError) {
        console.error(`❌ Error processing user ${user.email}:`, userError)
      }
    }

    console.log(`🎉 AUTO REMINDER SYSTEM FINISHED: ${totalNewEmails} new emails, ${totalNotificationsSent} notifications sent`)

    return successResponse({
      usersProcessed: users.length,
      totalEmailsProcessed,
      totalNewEmails,
      totalNotificationsSent,
      timestamp: new Date().toISOString(),
      message: "Auto reminder system completed - check your Telegram!"
    }, `Auto reminder system sent ${totalNotificationsSent} notifications for ${totalNewEmails} new emails`)

  } catch (error) {
    console.error('❌ Auto reminder system failed:', error)
    return serverErrorResponse(`Auto reminder system failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
