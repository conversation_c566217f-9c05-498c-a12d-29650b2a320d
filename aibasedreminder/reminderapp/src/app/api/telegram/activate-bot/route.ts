import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

// SIMPLE BOT ACTIVATION - For users who need manual activation
export async function GET(request: NextRequest) {
  try {
    console.log('🚀 USER ACTIVATION: Activating bot for user...')

    // Activate all systems
    const results = await Promise.allSettled([
      activateAutoSystem(),
      activateHealthMonitor(),
      forceWebhookSetup()
    ])

    const successful = results.filter(r => r.status === 'fulfilled').length
    
    return successResponse({
      userActivation: true,
      systemsActivated: successful,
      totalSystems: 3,
      timestamp: new Date().toISOString(),
      message: 'Your Telegram bot is now active and autonomous!'
    }, `Bot activated successfully - ${successful}/3 systems running`)

  } catch (error) {
    console.error('❌ User activation error:', error)
    return serverErrorResponse(`Bot activation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function activateAutoSystem() {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/auto-activate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      console.log('✅ Auto-activate system started')
      return true
    } else {
      console.error('❌ Failed to start auto-activate system')
      return false
    }
  } catch (error) {
    console.error('❌ Auto-activate error:', error)
    return false
  }
}

async function activateHealthMonitor() {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/health-monitor`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      console.log('✅ Health monitor started')
      return true
    } else {
      console.error('❌ Failed to start health monitor')
      return false
    }
  } catch (error) {
    console.error('❌ Health monitor error:', error)
    return false
  }
}

async function forceWebhookSetup() {
  try {
    const webhookUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`
    
    // Delete existing webhook
    await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/deleteWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })

    // Wait for cleanup
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Set new webhook
    const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/setWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message', 'callback_query'],
        drop_pending_updates: true,
        max_connections: 100
      })
    })

    const result = await response.json()
    
    if (result.ok) {
      console.log('✅ Webhook setup successful')
      return true
    } else {
      console.error('❌ Webhook setup failed:', result)
      return false
    }
    
  } catch (error) {
    console.error('❌ Webhook setup error:', error)
    return false
  }
}

// Test endpoint
export async function POST(request: NextRequest) {
  try {
    // Test bot responsiveness
    const botResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getMe`)
    const botResult = await botResponse.json()
    
    const webhookResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getWebhookInfo`)
    const webhookResult = await webhookResponse.json()
    
    return successResponse({
      test: true,
      bot: botResult,
      webhook: webhookResult,
      timestamp: new Date().toISOString()
    }, 'Bot test completed')
    
  } catch (error) {
    return serverErrorResponse(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
