import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

// Self-healing webhook system
let selfHealingActive = false
let healingInterval: NodeJS.Timeout | null = null
let lastWebhookCheck = 0

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Starting SELF-HEALING webhook system...')

    if (selfHealingActive) {
      console.log('⚠️ Self-healing already active')
      return successResponse({
        selfHealing: true,
        alreadyActive: true,
        timestamp: new Date().toISOString()
      }, 'Self-healing system already active')
    }

    // Start self-healing system
    await startSelfHealingSystem()

    return successResponse({
      selfHealing: true,
      started: true,
      timestamp: new Date().toISOString(),
      message: 'Self-healing webhook system started - bot will maintain itself automatically!'
    }, 'Self-healing webhook system activated')

  } catch (error) {
    console.error('❌ Failed to start self-healing system:', error)
    return serverErrorResponse(`Failed to start self-healing system: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function startSelfHealingSystem() {
  if (selfHealingActive) {
    return
  }

  console.log('🚀 Activating PERMANENT self-healing system...')
  selfHealingActive = true

  // Initial webhook setup
  await ensureWebhookActive()

  // Set up continuous self-healing (every 3 minutes)
  healingInterval = setInterval(async () => {
    try {
      console.log('🔍 Self-healing check...')
      await ensureWebhookActive()
      await triggerEmailCheck()
    } catch (error) {
      console.error('❌ Self-healing check failed:', error)
    }
  }, 3 * 60 * 1000) // 3 minutes

  console.log('✅ Self-healing system is now ACTIVE and will maintain the bot automatically!')
}

async function ensureWebhookActive() {
  try {
    const now = Date.now()
    
    // Only check webhook every 3 minutes to avoid spam
    if (now - lastWebhookCheck < 3 * 60 * 1000) {
      return
    }
    
    lastWebhookCheck = now
    
    console.log('🔍 Checking webhook status...')
    
    // Check current webhook info
    const webhookInfoResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getWebhookInfo`)
    const webhookInfo = await webhookInfoResponse.json()
    
    const expectedUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`
    
    if (!webhookInfo.result || webhookInfo.result.url !== expectedUrl) {
      console.log('🔧 Webhook needs reactivation, fixing...')
      await reactivateWebhook()
    } else {
      console.log('✅ Webhook is healthy:', webhookInfo.result.url)
    }
    
  } catch (error) {
    console.error('❌ Webhook health check error:', error)
    // Always try to reactivate on error
    await reactivateWebhook()
  }
}

async function reactivateWebhook() {
  try {
    console.log('🔄 REACTIVATING webhook automatically...')
    
    // Delete old webhook first
    await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/deleteWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })

    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Set new webhook
    const webhookUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`
    
    const webhookResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/setWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message', 'callback_query'],
        drop_pending_updates: true,
        max_connections: 100
      })
    })
    
    const result = await webhookResponse.json()
    
    if (result.ok) {
      console.log('✅ Webhook REACTIVATED successfully!')
    } else {
      console.error('❌ Webhook reactivation failed:', result)
    }
    
  } catch (error) {
    console.error('❌ Failed to reactivate webhook:', error)
  }
}

async function triggerEmailCheck() {
  try {
    console.log('📧 Triggering email check...')
    
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/check-emails-now`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      const result = await response.json()
      console.log('📧 Email check completed:', result.data?.newEmails || 0, 'new emails')
    } else {
      console.log('⚠️ Email check endpoint not ready yet')
    }
  } catch (error) {
    console.log('⚠️ Email check not available yet:', error.message)
  }
}

// Stop self-healing (for debugging)
export async function DELETE(request: NextRequest) {
  try {
    if (healingInterval) {
      clearInterval(healingInterval)
      healingInterval = null
    }
    
    selfHealingActive = false
    
    return successResponse({
      selfHealing: false,
      stopped: true,
      timestamp: new Date().toISOString()
    }, 'Self-healing system stopped')
    
  } catch (error) {
    return serverErrorResponse(`Failed to stop self-healing: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Also allow POST
export const POST = GET
