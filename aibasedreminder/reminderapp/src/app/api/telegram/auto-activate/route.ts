import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

// AUTO-ACTIVATION system that starts immediately on deployment
let autoActivated = false
let autoEmailInterval: NodeJS.Timeout | null = null
let autoReminderInterval: NodeJS.Timeout | null = null
let autoWebhookInterval: NodeJS.Timeout | null = null

// Auto-activate immediately when this module loads
setTimeout(async () => {
  if (!autoActivated) {
    console.log('🚀 AUTO-ACTIVATING Telegram bot system...')
    await autoActivateSystem()
  }
}, 5000) // 5 seconds after deployment

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 Manual activation of auto-activate system...')

    if (autoActivated) {
      return successResponse({
        autoActivated: true,
        alreadyActive: true,
        timestamp: new Date().toISOString()
      }, 'Auto-activate system already running')
    }

    await autoActivateSystem()

    return successResponse({
      autoActivated: true,
      started: true,
      timestamp: new Date().toISOString(),
      message: 'Auto-activate system started - bot is now fully autonomous!'
    }, 'Auto-activate system activated')

  } catch (error) {
    console.error('❌ Failed to start auto-activate system:', error)
    return serverErrorResponse(`Failed to start auto-activate system: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function autoActivateSystem() {
  if (autoActivated) {
    return
  }

  console.log('🚀 Starting AUTO-ACTIVATE system...')
  autoActivated = true

  // IMMEDIATELY force webhook setup
  await forceWebhookSetupAuto()

  // Set up webhook monitoring every 2 minutes - AUTO
  autoWebhookInterval = setInterval(async () => {
    try {
      console.log('🔍 Auto webhook check...')
      await ensureWebhookWorkingAuto()
    } catch (error) {
      console.error('❌ Auto webhook check failed:', error)
      await forceWebhookSetupAuto()
    }
  }, 2 * 60 * 1000) // 2 minutes

  // Set up email checking every 30 seconds - AUTO (more frequent for immediate detection)
  autoEmailInterval = setInterval(async () => {
    try {
      console.log('📧 Auto email check...')
      await checkEmailsAuto()
    } catch (error) {
      console.error('❌ Auto email check failed:', error)
    }
  }, 30 * 1000) // 30 seconds for faster email detection

  // Set up continuous reminders every 5 minutes - AUTO (as requested by user)
  autoReminderInterval = setInterval(async () => {
    try {
      console.log('🔔 Auto reminder check...')
      await sendRemindersAuto()
    } catch (error) {
      console.error('❌ Auto reminder check failed:', error)
    }
  }, 5 * 60 * 1000) // 5 minutes for continuous reminders

  // Run initial checks immediately
  await checkEmailsAuto()
  await sendRemindersAuto()

  console.log('✅ AUTO-ACTIVATE system is now FULLY ACTIVE!')
}

async function forceWebhookSetupAuto() {
  try {
    console.log('🔧 AUTO webhook setup...')
    
    const webhookUrl = `https://aibasedreminder.vercel.app/api/telegram/webhook`
    console.log('🔗 Auto Webhook URL:', webhookUrl)

    // Delete existing webhook
    const deleteResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/deleteWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })
    
    const deleteResult = await deleteResponse.json()
    console.log('🗑️ Auto webhook deletion:', deleteResult)

    // Wait for cleanup
    await new Promise(resolve => setTimeout(resolve, 3000))

    // Set new webhook with maximum reliability
    const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/setWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message', 'callback_query'],
        drop_pending_updates: true,
        max_connections: 100,
        secret_token: 'auto_webhook_2024'
      })
    })

    const result = await response.json()
    console.log('📱 Auto webhook setup result:', result)

    if (result.ok) {
      console.log('✅ Auto webhook FORCED successfully!')
    } else {
      console.error('❌ Auto webhook setup failed:', result)
      // Retry after 30 seconds
      setTimeout(() => forceWebhookSetupAuto(), 30000)
    }

  } catch (error) {
    console.error('❌ Auto webhook setup error:', error)
    // Retry after 30 seconds
    setTimeout(() => forceWebhookSetupAuto(), 30000)
  }
}

async function ensureWebhookWorkingAuto() {
  try {
    // Check webhook info
    const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getWebhookInfo`)
    const result = await response.json()
    
    const expectedUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`
    
    if (!result.result || result.result.url !== expectedUrl) {
      console.log('🔧 Auto webhook not working, fixing...')
      await forceWebhookSetupAuto()
    } else {
      console.log('✅ Auto webhook is working:', result.result.url)
    }
    
  } catch (error) {
    console.error('❌ Auto webhook check error:', error)
    await forceWebhookSetupAuto()
  }
}

async function checkEmailsAuto() {
  try {
    console.log('📧 Running auto email check...')
    
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/check-emails-now`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      const result = await response.json()
      console.log('📧 Auto email check result:', {
        usersChecked: result.data?.usersChecked || 0,
        newEmails: result.data?.newEmails || 0,
        notificationsSent: result.data?.notificationsSent || 0
      })
      
      if (result.data?.newEmails > 0 && result.data?.notificationsSent === 0) {
        console.log('⚠️ Found emails but no notifications sent - fixing webhook')
        await forceWebhookSetupAuto()
      }
    } else {
      console.error('❌ Auto email check failed:', response.status)
    }
    
  } catch (error) {
    console.error('❌ Auto email check error:', error)
  }
}

async function sendRemindersAuto() {
  try {
    console.log('🔔 Running auto reminders...')
    
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/continuous-reminders`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      const result = await response.json()
      console.log('🔔 Auto reminders result:', {
        emailsChecked: result.data?.emailsChecked || 0,
        remindersSent: result.data?.remindersSent || 0
      })
    } else {
      console.error('❌ Auto reminders failed:', response.status)
    }
    
  } catch (error) {
    console.error('❌ Auto reminders error:', error)
  }
}

// Test endpoint
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing AUTO-ACTIVATE system...')
    
    // Force immediate activation
    await autoActivateSystem()
    
    return successResponse({
      tested: true,
      autoActivated: true,
      timestamp: new Date().toISOString()
    }, 'Auto-activate system tested and activated')
    
  } catch (error) {
    return serverErrorResponse(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Health check endpoint
export async function PUT(request: NextRequest) {
  return successResponse({
    autoActivated,
    intervals: {
      email: autoEmailInterval !== null,
      reminder: autoReminderInterval !== null,
      webhook: autoWebhookInterval !== null
    },
    timestamp: new Date().toISOString()
  }, 'Auto-activate system health check')
}
