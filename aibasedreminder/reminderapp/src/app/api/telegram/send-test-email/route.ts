import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import nodemailer from 'nodemailer'

export async function POST(request: NextRequest) {
  try {
    const { targetEmail } = await request.json()
    
    if (!targetEmail) {
      return serverErrorResponse('Target email address is required')
    }

    console.log(`📧 SENDING REAL TEST EMAIL to: ${targetEmail}`)

    // Create Gmail transporter using your configured SMTP
    const transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: '<EMAIL>', // Your Gmail account
        pass: 'mvat fzjz rjbn bvwq' // Your app password
      }
    })

    // Generate unique test email content
    const timestamp = new Date().toLocaleString()
    const testId = Math.random().toString(36).substring(7)

    const mailOptions = {
      from: '<EMAIL>',
      to: targetEmail,
      subject: `🚨 URGENT: Test Email for Telegram Bot - ${testId}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 2px solid #ff6b6b; border-radius: 10px; background-color: #fff5f5;">
          <h2 style="color: #ff6b6b; text-align: center;">🚨 URGENT TEST EMAIL 🚨</h2>
          
          <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff6b6b;">
            <h3 style="color: #333; margin-top: 0;">📱 Telegram Bot Test</h3>
            <p style="color: #666; font-size: 16px; line-height: 1.6;">
              This is a <strong>REAL TEST EMAIL</strong> sent to verify your Telegram bot email notification system.
            </p>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
              <p style="margin: 5px 0;"><strong>📧 Target Email:</strong> ${targetEmail}</p>
              <p style="margin: 5px 0;"><strong>⏰ Sent At:</strong> ${timestamp}</p>
              <p style="margin: 5px 0;"><strong>🔍 Test ID:</strong> ${testId}</p>
            </div>
            
            <h4 style="color: #ff6b6b;">🎯 Expected Behavior:</h4>
            <ul style="color: #666; line-height: 1.8;">
              <li>✅ You should receive an <strong>INSTANT Telegram notification</strong></li>
              <li>🔔 Continuous reminders every <strong>5 minutes</strong> until you respond</li>
              <li>💬 Interactive buttons to Reply, Mark as Read, Star, or Stop Reminders</li>
              <li>📱 Ability to respond directly from Telegram</li>
            </ul>
            
            <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
              <h4 style="color: #28a745; margin-top: 0;">✅ If you receive this in Telegram:</h4>
              <p style="color: #666; margin-bottom: 0;">Your email notification system is working perfectly! 🎉</p>
            </div>
            
            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
              <h4 style="color: #856404; margin-top: 0;">⚠️ If you DON'T receive this in Telegram:</h4>
              <p style="color: #666; margin-bottom: 0;">There may be an issue with the email processing system that needs to be fixed.</p>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #999; font-size: 14px;">
              This email was sent by your AI Reminder System for testing purposes.<br>
              Test ID: ${testId} | Timestamp: ${timestamp}
            </p>
          </div>
        </div>
      `
    }

    // Send the email
    const info = await transporter.sendMail(mailOptions)
    
    console.log(`✅ REAL TEST EMAIL SENT successfully to ${targetEmail}`)
    console.log(`📧 Message ID: ${info.messageId}`)

    return successResponse({
      emailSent: true,
      targetEmail,
      messageId: info.messageId,
      testId,
      timestamp,
      nextSteps: [
        "1. Check your Telegram bot for instant notification",
        "2. If no notification, run email check manually",
        "3. Expect continuous reminders every 5 minutes",
        "4. Test reply functionality from Telegram"
      ]
    }, `Real test email sent successfully to ${targetEmail}`)

  } catch (error) {
    console.error('❌ Failed to send test email:', error)
    return serverErrorResponse(`Failed to send test email: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// GET endpoint to send test emails to both connected accounts
export async function GET() {
  try {
    console.log('📧 SENDING TEST EMAILS TO ALL CONNECTED ACCOUNTS')

    const connectedEmails = [
      '<EMAIL>',
      '<EMAIL>'
    ]

    const results = []

    for (const email of connectedEmails) {
      try {
        // Create a mock request for the POST function
        const mockRequest = {
          json: async () => ({ targetEmail: email })
        } as NextRequest

        const result = await POST(mockRequest)
        results.push({
          email,
          success: true,
          result: await result.json()
        })

        // Wait 2 seconds between emails to prevent spam
        await new Promise(resolve => setTimeout(resolve, 2000))

      } catch (emailError) {
        results.push({
          email,
          success: false,
          error: emailError instanceof Error ? emailError.message : 'Unknown error'
        })
      }
    }

    return successResponse({
      emailsSent: results.filter(r => r.success).length,
      totalEmails: connectedEmails.length,
      results,
      timestamp: new Date().toISOString()
    }, `Sent test emails to ${results.filter(r => r.success).length} out of ${connectedEmails.length} connected accounts`)

  } catch (error) {
    console.error('❌ Failed to send test emails:', error)
    return serverErrorResponse(`Failed to send test emails: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
