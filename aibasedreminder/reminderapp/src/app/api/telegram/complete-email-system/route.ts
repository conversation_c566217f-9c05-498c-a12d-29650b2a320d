import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET() {
  try {
    console.log('🚀 COMPLETE EMAIL SYSTEM - Processing all emails and sending notifications')

    // Get users with active Telegram connections and valid Google tokens
    const users = await prisma.$queryRaw`
      SELECT 
        u.id, u.email,
        tu."telegramId", tu."isActive" as telegram_active,
        ot."accessToken", ot."refreshToken", ot."expiresAt"
      FROM users u
      JOIN "TelegramUser" tu ON u.id = tu."userId"
      JOIN "OAuthToken" ot ON u.id = ot."userId"
      WHERE tu."isActive" = true 
        AND ot.provider = 'google'
        AND ot."expiresAt" > NOW()
    ` as any[]

    console.log(`👥 Found ${users.length} users with active Telegram and valid Google tokens`)

    let totalEmailsProcessed = 0
    let totalNotificationsSent = 0

    for (const user of users) {
      try {
        console.log(`📧 Processing emails for: ${user.email}`)

        const { gmail } = await getGoogleServices(user.id)

        // Get ALL unread emails
        const unreadMessages = await gmail.getMessages('is:unread', 50)
        console.log(`📨 Found ${unreadMessages.length} unread emails for ${user.email}`)

        // Get existing emails to avoid duplicates
        const existingEmails = await prisma.$queryRaw`
          SELECT "gmailMessageId" FROM emails WHERE "userId" = ${user.id}
        `
        const existingIds = new Set((existingEmails as any[]).map(e => e.gmailMessageId))

        let emailsCreated = 0
        let notificationsSent = 0

        for (const message of unreadMessages) {
          try {
            // Skip if already exists
            if (existingIds.has(message.id)) {
              console.log(`⏭️ Skipping existing email: ${message.id}`)
              continue
            }

            // Extract email data
            const headers = message.payload?.headers || []
            const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
            const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown'
            const to = headers.find((h: any) => h.name === 'To')?.value || ''
            const dateHeader = headers.find((h: any) => h.name === 'Date')?.value
            const receivedAt = dateHeader ? new Date(dateHeader) : new Date()
            const body = message.snippet || 'No content'

            console.log(`🆕 Processing: "${subject}" from "${from}"`)

            // Create email using raw SQL (bypasses Prisma schema issues)
            const emailResult = await prisma.$executeRaw`
              INSERT INTO emails (
                id, "userId", "gmailMessageId", "threadId", subject, "from", 
                "to", body, "receivedAt", "isRead", "isImportant", labels,
                "createdAt", "updatedAt"
              ) VALUES (
                gen_random_uuid(), ${user.id}, ${message.id}, 
                ${message.threadId || message.id}, ${subject}, ${from},
                ${to}, ${body.substring(0, 1000)}, ${receivedAt}, false, false, 
                ${JSON.stringify(message.labelIds || [])}, NOW(), NOW()
              )
            `

            if (emailResult) {
              emailsCreated++
              totalEmailsProcessed++
              console.log(`✅ Created email in database: ${subject}`)

              // Send immediate Telegram notification
              const notificationMessage = `🚨 NEW EMAIL RECEIVED!\n\n` +
                `📝 Subject: ${subject}\n` +
                `👤 From: ${from}\n` +
                `📅 Received: ${receivedAt.toLocaleString()}\n` +
                `⚡ Status: UNREAD - Needs Response\n\n` +
                `📄 Preview:\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}\n\n` +
                `🔔 You'll get reminders every 5 minutes until you respond!`

              // Get the created email ID for buttons
              const createdEmail = await prisma.$queryRaw`
                SELECT id FROM emails WHERE "gmailMessageId" = ${message.id} LIMIT 1
              `
              const emailId = (createdEmail as any[])[0]?.id

              const keyboard = {
                inline_keyboard: [
                  [
                    { text: "💬 Reply to Email", callback_data: `reply_to_${emailId}` },
                    { text: "✅ Mark as Read", callback_data: `mark_read_${emailId}` }
                  ],
                  [
                    { text: "⭐ Star Email", callback_data: `star_email_${emailId}` },
                    { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${emailId}` }
                  ]
                ]
              }

              try {
                const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    chat_id: user.telegramId,
                    text: notificationMessage,
                    reply_markup: keyboard
                  })
                })

                const result = await response.json()
                if (result.ok) {
                  notificationsSent++
                  totalNotificationsSent++
                  console.log(`📱 ✅ Notification sent for: ${subject}`)
                } else {
                  console.error(`📱 ❌ Failed to send notification:`, result)
                }

              } catch (notificationError) {
                console.error(`📱 ❌ Error sending notification:`, notificationError)
              }

              // Add delay between notifications to prevent flooding
              await new Promise(resolve => setTimeout(resolve, 1000))
            }

          } catch (emailError) {
            console.error(`❌ Error processing email ${message.id}:`, emailError)
          }
        }

        console.log(`✅ User ${user.email}: ${emailsCreated} emails created, ${notificationsSent} notifications sent`)

        // Add delay between users
        await new Promise(resolve => setTimeout(resolve, 2000))

      } catch (userError) {
        console.error(`❌ Error processing user ${user.email}:`, userError)
      }
    }

    console.log(`🎉 COMPLETE EMAIL SYSTEM FINISHED: ${totalEmailsProcessed} emails processed, ${totalNotificationsSent} notifications sent`)

    return successResponse({
      usersProcessed: users.length,
      totalEmailsProcessed,
      totalNotificationsSent,
      timestamp: new Date().toISOString()
    }, `Complete email system processed ${totalEmailsProcessed} emails and sent ${totalNotificationsSent} notifications`)

  } catch (error) {
    console.error('❌ Complete email system failed:', error)
    return serverErrorResponse(`Complete email system failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
