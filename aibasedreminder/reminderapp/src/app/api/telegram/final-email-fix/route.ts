import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 FINAL EMAIL FIX - PROCESSING ALL EMAILS')

    // Get the user with active Telegram connection
    const user = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
        telegramUsers: {
          some: { isActive: true }
        }
      },
      include: {
        telegramUsers: { where: { isActive: true } },
        oauthTokens: { where: { provider: 'google' } }
      }
    })

    if (!user || !user.telegramUsers.length) {
      return serverErrorResponse('User or Telegram connection not found')
    }

    console.log(`👤 Processing emails for: ${user.email}`)
    console.log(`📱 Active Telegram users: ${user.telegramUsers.length}`)

    const { gmail } = await getGoogleServices(user.id)

    // Get ALL unread emails
    const unreadMessages = await gmail.getMessages('is:unread', 50)
    console.log(`📨 Found ${unreadMessages.length} unread emails`)

    // Get existing emails to avoid duplicates
    const existingEmails = await prisma.$queryRaw`
      SELECT "gmailMessageId" FROM emails WHERE "userId" = ${user.id}
    `
    const existingIds = new Set((existingEmails as any[]).map(e => e.gmailMessageId))
    console.log(`💾 Found ${existingIds.size} existing emails in database`)

    let emailsCreated = 0
    let notificationsSent = 0
    let processedEmails = []

    for (const message of unreadMessages) {
      try {
        // Skip if already exists
        if (existingIds.has(message.id)) {
          console.log(`⏭️ Skipping existing email: ${message.id}`)
          continue
        }

        // Extract email data
        const headers = message.payload?.headers || []
        const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
        const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown'
        const to = headers.find((h: any) => h.name === 'To')?.value || ''
        const dateHeader = headers.find((h: any) => h.name === 'Date')?.value
        const receivedAt = dateHeader ? new Date(dateHeader) : new Date()
        const body = message.snippet || 'No content'

        console.log(`🆕 Processing: "${subject}" from "${from}"`)

        // Create email using raw SQL (bypasses Prisma schema issues)
        const emailResult = await prisma.$executeRaw`
          INSERT INTO emails (
            id, "userId", "gmailMessageId", "threadId", subject, "from", 
            "to", body, "receivedAt", "isRead", "isImportant", labels,
            "createdAt", "updatedAt"
          ) VALUES (
            gen_random_uuid(), ${user.id}, ${message.id}, 
            ${message.threadId || message.id}, ${subject}, ${from},
            ${to}, ${body.substring(0, 1000)}, ${receivedAt}, false, false, 
            ${JSON.stringify(message.labelIds || [])}, NOW(), NOW()
          )
          RETURNING id
        `

        if (emailResult) {
          emailsCreated++
          console.log(`✅ Created email in database: ${subject}`)

          // Get the created email ID for notifications
          const createdEmail = await prisma.$queryRaw`
            SELECT id FROM emails WHERE "gmailMessageId" = ${message.id} LIMIT 1
          `
          const emailId = (createdEmail as any[])[0]?.id

          processedEmails.push({
            subject,
            from,
            messageId: message.id,
            emailId,
            receivedAt: receivedAt.toISOString()
          })

          // Send Telegram notifications
          for (const telegramUser of user.telegramUsers) {
            try {
              const notificationMessage = `🚨 EMAIL NOTIFICATION SYSTEM FIXED!\n\n` +
                `📝 Subject: ${subject}\n` +
                `👤 From: ${from}\n` +
                `📅 Received: ${receivedAt.toLocaleString()}\n` +
                `⚡ Status: UNREAD - Needs Response\n\n` +
                `📄 Preview:\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}\n\n` +
                `🎉 Your email notifications are now working!\n` +
                `💬 You'll get reminders every 5 minutes until you respond.`

              const keyboard = {
                inline_keyboard: [
                  [
                    { text: "💬 Reply to Email", callback_data: `reply_to_${emailId}` },
                    { text: "✅ Mark as Read", callback_data: `mark_read_${emailId}` }
                  ],
                  [
                    { text: "⭐ Star Email", callback_data: `star_email_${emailId}` },
                    { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${emailId}` }
                  ]
                ]
              }

              const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  chat_id: telegramUser.telegramId,
                  text: notificationMessage,
                  reply_markup: keyboard
                })
              })

              const result = await response.json()
              if (result.ok) {
                notificationsSent++
                console.log(`📱 ✅ Notification sent for: ${subject}`)
              } else {
                console.error(`📱 ❌ Failed to send notification:`, result)
              }

              // Add delay between notifications to prevent flooding
              await new Promise(resolve => setTimeout(resolve, 1000))

            } catch (notificationError) {
              console.error(`📱 ❌ Error sending notification:`, notificationError)
            }
          }
        }

      } catch (emailError) {
        console.error(`❌ Error processing email ${message.id}:`, emailError)
      }
    }

    console.log(`🎉 FINAL FIX COMPLETE: ${emailsCreated} emails created, ${notificationsSent} notifications sent`)

    return successResponse({
      totalUnreadEmails: unreadMessages.length,
      existingInDatabase: existingIds.size,
      emailsCreated,
      notificationsSent,
      processedEmails: processedEmails.slice(0, 10), // Show first 10
      timestamp: new Date().toISOString()
    }, `Email system fixed! Created ${emailsCreated} emails and sent ${notificationsSent} notifications`)

  } catch (error) {
    console.error('❌ Final email fix failed:', error)
    return serverErrorResponse(`Final email fix failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
