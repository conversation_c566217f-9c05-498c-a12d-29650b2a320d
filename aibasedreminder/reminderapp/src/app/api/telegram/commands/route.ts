import { NextRequest, NextResponse } from 'next/server'
import { setBotCommands } from '@/lib/telegram'

export async function POST(request: NextRequest) {
  try {
    const success = await setBotCommands()
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Bot commands registered successfully'
      })
    } else {
      return NextResponse.json({
        success: false,
        message: 'Failed to register bot commands'
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Error registering bot commands:', error)
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 })
  }
}
