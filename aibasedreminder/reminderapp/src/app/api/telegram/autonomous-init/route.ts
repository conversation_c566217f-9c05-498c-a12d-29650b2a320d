import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

// Global autonomous monitoring state
let autonomousMonitoring = false
let monitoringInterval: NodeJS.Timeout | null = null

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 Starting autonomous Telegram bot system...')

    // FORCE webhook setup to ensure immediate responses
    const webhookUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`

    // First delete any existing webhook
    await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/deleteWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })

    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Set up new webhook with immediate response settings
    const webhookResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/setWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message', 'callback_query'],
        drop_pending_updates: true, // Clear any pending updates
        max_connections: 100,
        secret_token: process.env.TELEGRAM_WEBHOOK_SECRET || 'autonomous_bot_secret'
      })
    })

    const webhookResult = await webhookResponse.json()
    console.log('📱 Telegram webhook setup:', webhookResult)

    // Test bot immediately
    const botInfoResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getMe`)
    const botInfo = await botInfoResponse.json()
    console.log('🤖 Bot info:', botInfo)

    // Start autonomous email monitoring
    await startAutonomousEmailMonitoring()

    return successResponse({
      autonomous: true,
      webhook: webhookResult,
      bot: botInfo,
      monitoring: autonomousMonitoring,
      timestamp: new Date().toISOString()
    }, 'Autonomous Telegram bot system started successfully')

  } catch (error) {
    console.error('❌ Failed to start autonomous bot system:', error)
    return serverErrorResponse(`Failed to start autonomous system: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Start autonomous email monitoring that runs independently
async function startAutonomousEmailMonitoring() {
  if (autonomousMonitoring) {
    console.log('⚠️ Autonomous monitoring already running')
    return
  }

  console.log('🔄 Starting autonomous email monitoring...')
  autonomousMonitoring = true

  // Check emails every 2 minutes autonomously
  monitoringInterval = setInterval(async () => {
    try {
      await checkEmailsAutonomously()
    } catch (error) {
      console.error('❌ Autonomous email check failed:', error)
    }
  }, 2 * 60 * 1000) // 2 minutes

  // Run initial check immediately
  await checkEmailsAutonomously()
}

// Autonomous email checking function
async function checkEmailsAutonomously() {
  try {
    console.log('🔍 Running autonomous email check...')

    // Get all users with valid Google OAuth tokens and active Telegram connections
    const users = await prisma.user.findMany({
      where: {
        oAuthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        },
        telegramUsers: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oAuthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with valid tokens and Telegram connections`)

    let totalNewEmails = 0
    let totalNotifications = 0

    for (const user of users) {
      try {
        console.log(`📧 Checking emails for user: ${user.email}`)
        
        const { gmail } = await getGoogleServices(user.id)
        
        // Get recent unread emails (last 3 minutes to ensure we don't miss any)
        const threeMinutesAgo = new Date(Date.now() - 3 * 60 * 1000)
        const newEmailsQuery = `is:unread after:${Math.floor(threeMinutesAgo.getTime() / 1000)}`
        const allUnreadQuery = `is:unread`

        // Get new emails and all unread for reminders
        const newGmailMessages = await gmail.getMessages(newEmailsQuery, 20)
        const allUnreadMessages = await gmail.getMessages(allUnreadQuery, 50)
        
        console.log(`📨 Found ${newGmailMessages.length} new emails and ${allUnreadMessages.length} total unread emails for ${user.email}`)

        let userNewEmails = 0
        let userNotifications = 0

        // Process new emails
        for (const gmailMessage of newGmailMessages) {
          try {
            // Check if we already have this email
            const existingEmail = await prisma.email.findUnique({
              where: { gmailMessageId: gmailMessage.id }
            })
            
            if (existingEmail) {
              continue // Skip if already processed
            }

            // Get email details
            const emailData = await gmail.getEmailDetails(gmailMessage.id)
            
            // Create new email record
            const newEmail = await prisma.email.create({
              data: {
                userId: user.id,
                gmailMessageId: gmailMessage.id,
                threadId: emailData.threadId,
                subject: emailData.subject,
                from: emailData.from,
                to: emailData.to,
                body: emailData.body,
                receivedAt: emailData.receivedAt,
                isRead: emailData.isRead,
                isImportant: emailData.isImportant,
                labels: JSON.stringify(emailData.labels || []),
                reminderSent: false
              }
            })

            userNewEmails++
            console.log(`✅ Created new email: ${emailData.subject} from ${emailData.from}`)
            
            // Send immediate Telegram notification with reply buttons
            for (const telegramUser of user.telegramUsers) {
              const notificationMessage = `🚨 NEW EMAIL RECEIVED!\n\n` +
                `📝 Subject: ${emailData.subject}\n` +
                `👤 From: ${emailData.from}\n` +
                `📅 Received: ${emailData.receivedAt.toLocaleString()}\n\n` +
                `${emailData.body.substring(0, 200)}${emailData.body.length > 200 ? '...' : ''}\n\n` +
                `💬 Click below to reply or use /reply command`

              // Create reply button
              const keyboard = {
                inline_keyboard: [
                  [
                    { text: "💬 Reply to this Email", callback_data: `reply_to_${newEmail.id}` },
                    { text: "✅ Mark as Read", callback_data: `mark_read_${newEmail.id}` }
                  ],
                  [
                    { text: "⭐ Star Email", callback_data: `star_email_${newEmail.id}` },
                    { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${newEmail.id}` }
                  ]
                ]
              }
              
              // Use direct Telegram API call for maximum reliability
              await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  chat_id: telegramUser.telegramId,
                  text: notificationMessage,
                  reply_markup: keyboard,
                  parse_mode: 'HTML'
                })
              })
              
              userNotifications++
              console.log(`📱 Sent notification to Telegram user ${telegramUser.telegramId}`)
            }
            
          } catch (emailError) {
            console.error(`❌ Error processing email ${gmailMessage.id}:`, emailError)
          }
        }

        // Process recurring reminders for unread emails (every 5 minutes)
        for (const gmailMessage of allUnreadMessages) {
          try {
            const existingEmail = await prisma.email.findUnique({
              where: { gmailMessageId: gmailMessage.id }
            })

            if (existingEmail && !existingEmail.isRead) {
              // Check if we need to send recurring reminder (every 5 minutes)
              const lastReminderTime = existingEmail.lastReminderSent
              const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)

              if (!lastReminderTime || lastReminderTime < fiveMinutesAgo) {
                console.log(`⏰ Sending recurring reminder for email: ${existingEmail.subject}`)

                // Send recurring reminder with reply option
                for (const telegramUser of user.telegramUsers) {
                  const reminderMessage = `⏰ REMINDER: Unread Email!\n\n` +
                    `📧 From: ${existingEmail.from}\n` +
                    `📝 Subject: ${existingEmail.subject}\n` +
                    `⏰ Received: ${existingEmail.receivedAt.toLocaleString()}\n\n` +
                    `💬 Click below to reply or mark as read`

                  const keyboard = {
                    inline_keyboard: [
                      [
                        { text: "💬 Reply Now", callback_data: `reply_to_${existingEmail.id}` },
                        { text: "✅ Mark as Read", callback_data: `mark_read_${existingEmail.id}` }
                      ],
                      [
                        { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${existingEmail.id}` }
                      ]
                    ]
                  }

                  // Use direct Telegram API call
                  await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      chat_id: telegramUser.telegramId,
                      text: reminderMessage,
                      reply_markup: keyboard,
                      parse_mode: 'HTML'
                    })
                  })
                  userNotifications++
                }

                // Update last reminder time
                await prisma.email.update({
                  where: { id: existingEmail.id },
                  data: { lastReminderSent: new Date() }
                })
              }
            }
          } catch (reminderError) {
            console.error(`❌ Error processing reminder for email ${gmailMessage.id}:`, reminderError)
          }
        }
        
        totalNewEmails += userNewEmails
        totalNotifications += userNotifications
        
        console.log(`✅ User ${user.email}: ${userNewEmails} new emails, ${userNotifications} notifications sent`)
        
      } catch (userError) {
        console.error(`❌ Error checking emails for user ${user.email}:`, userError)
      }
    }
    
    console.log(`🎉 Autonomous email check completed: ${totalNewEmails} new emails, ${totalNotifications} notifications sent`)

  } catch (error) {
    console.error('❌ Autonomous email check failed:', error)
  }
}

// Stop autonomous monitoring
export async function POST(request: NextRequest) {
  try {
    console.log('🛑 Stopping autonomous monitoring...')
    
    if (monitoringInterval) {
      clearInterval(monitoringInterval)
      monitoringInterval = null
    }
    
    autonomousMonitoring = false
    
    return successResponse({
      autonomous: false,
      stopped: true,
      timestamp: new Date().toISOString()
    }, 'Autonomous monitoring stopped')

  } catch (error) {
    console.error('❌ Failed to stop autonomous monitoring:', error)
    return serverErrorResponse(`Failed to stop monitoring: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
