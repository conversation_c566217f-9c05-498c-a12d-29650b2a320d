import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 DEBUGGING EMAIL SYSTEM...')

    // Get users with valid Google OAuth tokens and active Telegram connections
    const users = await prisma.user.findMany({
      where: {
        oauthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        },
        telegramUsers: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with valid tokens and Telegram connections`)

    let debugResults = []

    for (const user of users) {
      try {
        console.log(`🔍 Debugging email system for user: ${user.email}`)

        const userDebug = {
          user: user.email,
          telegramUsers: user.telegramUsers.length,
          googleTokens: user.oauthTokens.length,
          tokenExpiry: user.oauthTokens[0]?.expiresAt,
          steps: []
        }

        // Step 1: Test Google Services Connection
        userDebug.steps.push('🔑 Testing Google Services connection...')
        const { gmail } = await getGoogleServices(user.id)
        userDebug.steps.push('✅ Google Services connected successfully')

        // Step 2: Test Gmail API - Get ALL unread emails
        userDebug.steps.push('📧 Fetching ALL unread emails from Gmail...')
        const allUnreadMessages = await gmail.getMessages('is:unread', 50)
        userDebug.steps.push(`📨 Found ${allUnreadMessages.length} total unread emails in Gmail`)

        // Step 3: Show sample email details if any exist
        if (allUnreadMessages.length > 0) {
          const sampleEmail = allUnreadMessages[0]
          const headers = sampleEmail.payload?.headers || []
          const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
          const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown Sender'
          const date = headers.find((h: any) => h.name === 'Date')?.value || 'No Date'
          
          userDebug.steps.push(`📧 Sample unread email: "${subject}" from "${from}" (${date})`)
          userDebug.steps.push(`🆔 Gmail Message ID: ${sampleEmail.id}`)
        }

        // Step 4: Check what's in our database
        userDebug.steps.push('💾 Checking emails in database...')
        const dbEmails = await prisma.email.findMany({
          where: { userId: user.id },
          orderBy: { createdAt: 'desc' },
          take: 10,
          select: {
            id: true,
            gmailMessageId: true,
            subject: true,
            from: true,
            isRead: true,
            createdAt: true
          }
        })
        userDebug.steps.push(`💾 Found ${dbEmails.length} emails in database for this user`)

        // Step 5: Compare Gmail vs Database
        if (allUnreadMessages.length > 0) {
          const gmailIds = allUnreadMessages.map(msg => msg.id)
          const dbIds = dbEmails.map(email => email.gmailMessageId)
          const newEmailIds = gmailIds.filter(id => !dbIds.includes(id))
          
          userDebug.steps.push(`🆕 Found ${newEmailIds.length} emails in Gmail that are NOT in database`)
          
          if (newEmailIds.length > 0) {
            userDebug.steps.push(`🚨 NEW EMAILS DETECTED: ${newEmailIds.slice(0, 3).join(', ')}`)
          } else {
            userDebug.steps.push(`ℹ️ All Gmail emails are already in database`)
          }
        }

        // Step 6: Check recent database activity
        const recentEmails = dbEmails.filter(email => 
          email.createdAt > new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        )
        userDebug.steps.push(`📅 Found ${recentEmails.length} emails added to database in last 24 hours`)

        debugResults.push(userDebug)

      } catch (userError) {
        console.error(`❌ Error debugging user ${user.email}:`, userError)
        debugResults.push({
          user: user.email,
          error: userError instanceof Error ? userError.message : 'Unknown error',
          steps: ['❌ Failed to debug user']
        })
      }
    }

    console.log('🔍 EMAIL SYSTEM DEBUG COMPLETE')

    return successResponse({
      debugResults,
      summary: {
        usersChecked: users.length,
        timestamp: new Date().toISOString()
      }
    }, 'Email system debug completed')

  } catch (error) {
    console.error('❌ Email system debug failed:', error)
    return serverErrorResponse(`Email system debug failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Also allow POST
export const POST = GET
