import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

// INDEPENDENT autonomous system that works regardless of website state
let independentActive = false
let emailCheckInterval: NodeJS.Timeout | null = null
let reminderInterval: NodeJS.Timeout | null = null
let webhookMonitorInterval: NodeJS.Timeout | null = null

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 Starting INDEPENDENT autonomous system...')

    if (independentActive) {
      console.log('⚠️ Independent autonomous system already active')
      return successResponse({
        independent: true,
        alreadyActive: true,
        timestamp: new Date().toISOString()
      }, 'Independent autonomous system already active')
    }

    // Start the INDEPENDENT autonomous system
    await startIndependentSystem()

    return successResponse({
      independent: true,
      started: true,
      timestamp: new Date().toISOString(),
      message: 'INDEPENDENT autonomous system started - works regardless of website!'
    }, 'Independent autonomous system activated')

  } catch (error) {
    console.error('❌ Failed to start independent system:', error)
    return serverErrorResponse(`Failed to start independent system: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function startIndependentSystem() {
  if (independentActive) {
    return
  }

  console.log('🚀 Activating INDEPENDENT autonomous system...')
  independentActive = true

  // FORCE webhook setup immediately
  await forceWebhookSetup()

  // Set up webhook monitoring every 3 minutes - INDEPENDENT OF WEBSITE
  webhookMonitorInterval = setInterval(async () => {
    try {
      console.log('🔍 Independent webhook check...')
      await ensureWebhookWorking()
    } catch (error) {
      console.error('❌ Independent webhook check failed:', error)
      await forceWebhookSetup()
    }
  }, 3 * 60 * 1000) // 3 minutes

  // Set up email checking every 2 minutes - INDEPENDENT OF WEBSITE
  emailCheckInterval = setInterval(async () => {
    try {
      console.log('📧 Independent email check...')
      await checkEmailsIndependently()
    } catch (error) {
      console.error('❌ Independent email check failed:', error)
    }
  }, 2 * 60 * 1000) // 2 minutes

  // Set up continuous reminders every 5 minutes - INDEPENDENT OF WEBSITE
  reminderInterval = setInterval(async () => {
    try {
      console.log('🔔 Independent reminder check...')
      await sendRemindersIndependently()
    } catch (error) {
      console.error('❌ Independent reminder check failed:', error)
    }
  }, 5 * 60 * 1000) // 5 minutes

  // Run initial checks
  await checkEmailsIndependently()
  await sendRemindersIndependently()

  console.log('✅ INDEPENDENT autonomous system is now ACTIVE!')
}

async function forceWebhookSetup() {
  try {
    console.log('🔧 INDEPENDENT webhook setup...')
    
    const webhookUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`
    console.log('🔗 Webhook URL:', webhookUrl)

    // Delete existing webhook
    await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/deleteWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })

    // Wait for cleanup
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Set new webhook
    const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/setWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message', 'callback_query'],
        drop_pending_updates: true,
        max_connections: 100
      })
    })

    const result = await response.json()
    console.log('📱 Independent webhook setup result:', result)

    if (result.ok) {
      console.log('✅ Independent webhook FORCED successfully!')
    } else {
      console.error('❌ Independent webhook setup failed:', result)
      throw new Error(`Webhook setup failed: ${result.description}`)
    }

  } catch (error) {
    console.error('❌ Independent webhook setup error:', error)
    throw error
  }
}

async function ensureWebhookWorking() {
  try {
    // Check webhook info
    const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getWebhookInfo`)
    const result = await response.json()
    
    const expectedUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`
    
    if (!result.result || result.result.url !== expectedUrl) {
      console.log('🔧 Independent webhook not working, fixing...')
      await forceWebhookSetup()
    } else {
      console.log('✅ Independent webhook is working:', result.result.url)
    }
    
  } catch (error) {
    console.error('❌ Independent webhook check error:', error)
    await forceWebhookSetup()
  }
}

async function checkEmailsIndependently() {
  try {
    console.log('📧 Running independent email check...')
    
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/check-emails-now`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      const result = await response.json()
      console.log('📧 Independent email check result:', {
        usersChecked: result.data?.usersChecked || 0,
        newEmails: result.data?.newEmails || 0,
        notificationsSent: result.data?.notificationsSent || 0
      })
      
      if (result.data?.newEmails > 0 && result.data?.notificationsSent === 0) {
        console.log('⚠️ Found emails but no notifications sent - fixing webhook')
        await forceWebhookSetup()
      }
    } else {
      console.error('❌ Independent email check failed:', response.status)
    }
    
  } catch (error) {
    console.error('❌ Independent email check error:', error)
  }
}

async function sendRemindersIndependently() {
  try {
    console.log('🔔 Running independent reminders...')
    
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/continuous-reminders`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      const result = await response.json()
      console.log('🔔 Independent reminders result:', {
        emailsChecked: result.data?.emailsChecked || 0,
        remindersSent: result.data?.remindersSent || 0
      })
    } else {
      console.error('❌ Independent reminders failed:', response.status)
    }
    
  } catch (error) {
    console.error('❌ Independent reminders error:', error)
  }
}

// Test the system
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing INDEPENDENT system...')
    
    // Force webhook setup
    await forceWebhookSetup()
    
    // Check emails immediately
    await checkEmailsIndependently()
    
    // Send test reminders
    await sendRemindersIndependently()
    
    return successResponse({
      tested: true,
      timestamp: new Date().toISOString()
    }, 'Independent system tested')
    
  } catch (error) {
    return serverErrorResponse(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Stop the system
export async function DELETE(request: NextRequest) {
  try {
    if (emailCheckInterval) {
      clearInterval(emailCheckInterval)
      emailCheckInterval = null
    }
    
    if (reminderInterval) {
      clearInterval(reminderInterval)
      reminderInterval = null
    }
    
    if (webhookMonitorInterval) {
      clearInterval(webhookMonitorInterval)
      webhookMonitorInterval = null
    }
    
    independentActive = false
    
    return successResponse({
      independent: false,
      stopped: true,
      timestamp: new Date().toISOString()
    }, 'Independent system stopped')
    
  } catch (error) {
    return serverErrorResponse(`Failed to stop: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
