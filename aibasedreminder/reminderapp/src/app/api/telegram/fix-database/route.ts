import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 FIXING DATABASE SCHEMA...')

    // First, let's see what columns actually exist in the emails table
    console.log('1️⃣ Checking current database schema...')
    
    try {
      const emailTableInfo = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'emails'
        ORDER BY ordinal_position
      `
      console.log('📊 Current email table schema:', emailTableInfo)
    } catch (schemaError) {
      console.error('❌ Error checking schema:', schemaError)
    }

    // Try to create a simple email with only the fields that definitely exist
    console.log('2️⃣ Testing basic email creation...')
    
    const user = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    })

    if (!user) {
      return serverErrorResponse('User not found')
    }

    // Test 1: Minimal email creation
    let testResults = []
    
    try {
      console.log('🧪 Test 1: Minimal fields only')
      const testEmail1 = await prisma.email.create({
        data: {
          userId: user.id,
          gmailMessageId: 'test_' + Date.now() + '_1',
          threadId: 'thread_test_1',
          subject: 'Test Email 1',
          from: '<EMAIL>',
          body: 'Test body 1',
          receivedAt: new Date(),
          isRead: false
        }
      })
      testResults.push({
        test: 'Minimal fields',
        success: true,
        emailId: testEmail1.id
      })
      console.log('✅ Test 1 succeeded')
    } catch (error1) {
      testResults.push({
        test: 'Minimal fields',
        success: false,
        error: error1 instanceof Error ? error1.message : 'Unknown error'
      })
      console.error('❌ Test 1 failed:', error1)
    }

    // Test 2: Add more fields gradually
    try {
      console.log('🧪 Test 2: More fields')
      const testEmail2 = await prisma.email.create({
        data: {
          userId: user.id,
          gmailMessageId: 'test_' + Date.now() + '_2',
          threadId: 'thread_test_2',
          subject: 'Test Email 2',
          from: '<EMAIL>',
          to: '<EMAIL>',
          body: 'Test body 2',
          receivedAt: new Date(),
          isRead: false,
          isImportant: false,
          labels: '[]'
        }
      })
      testResults.push({
        test: 'More fields',
        success: true,
        emailId: testEmail2.id
      })
      console.log('✅ Test 2 succeeded')
    } catch (error2) {
      testResults.push({
        test: 'More fields',
        success: false,
        error: error2 instanceof Error ? error2.message : 'Unknown error'
      })
      console.error('❌ Test 2 failed:', error2)
    }

    // Test 3: Try without problematic fields
    try {
      console.log('🧪 Test 3: Without remindersEnabled field')
      const testEmail3 = await prisma.$executeRaw`
        INSERT INTO emails (
          id, "userId", "gmailMessageId", "threadId", subject, "from", 
          body, "receivedAt", "isRead", "createdAt", "updatedAt"
        ) VALUES (
          gen_random_uuid(), ${user.id}, ${'test_' + Date.now() + '_3'}, 
          'thread_test_3', 'Test Email 3', '<EMAIL>',
          'Test body 3', NOW(), false, NOW(), NOW()
        )
      `
      testResults.push({
        test: 'Raw SQL without remindersEnabled',
        success: true,
        result: testEmail3
      })
      console.log('✅ Test 3 succeeded')
    } catch (error3) {
      testResults.push({
        test: 'Raw SQL without remindersEnabled',
        success: false,
        error: error3 instanceof Error ? error3.message : 'Unknown error'
      })
      console.error('❌ Test 3 failed:', error3)
    }

    // Now try to create a real email from Gmail data
    console.log('3️⃣ Testing with real Gmail data...')
    
    try {
      const { getGoogleServices } = await import('@/lib/google')
      const { gmail } = await getGoogleServices(user.id)
      
      const unreadMessages = await gmail.getMessages('is:unread', 1)
      
      if (unreadMessages.length > 0) {
        const message = unreadMessages[0]
        const headers = message.payload?.headers || []
        const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
        const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown'
        const dateHeader = headers.find((h: any) => h.name === 'Date')?.value
        const receivedAt = dateHeader ? new Date(dateHeader) : new Date()
        const body = message.snippet || 'No content'
        
        console.log(`📧 Testing with real email: "${subject}" from "${from}"`)
        
        // Try creating with raw SQL to bypass Prisma schema issues
        const realEmailResult = await prisma.$executeRaw`
          INSERT INTO emails (
            id, "userId", "gmailMessageId", "threadId", subject, "from", 
            body, "receivedAt", "isRead", "createdAt", "updatedAt"
          ) VALUES (
            gen_random_uuid(), ${user.id}, ${message.id + '_real'}, 
            ${message.threadId || message.id}, ${subject}, ${from},
            ${body.substring(0, 1000)}, ${receivedAt}, false, NOW(), NOW()
          )
        `
        
        testResults.push({
          test: 'Real Gmail email with raw SQL',
          success: true,
          result: realEmailResult,
          emailData: { subject, from, messageId: message.id }
        })
        console.log('✅ Real email test succeeded')
      }
    } catch (realError) {
      testResults.push({
        test: 'Real Gmail email',
        success: false,
        error: realError instanceof Error ? realError.message : 'Unknown error'
      })
      console.error('❌ Real email test failed:', realError)
    }

    return successResponse({
      testResults,
      timestamp: new Date().toISOString()
    }, 'Database fix testing completed')

  } catch (error) {
    console.error('❌ Database fix failed:', error)
    return serverErrorResponse(`Database fix failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
