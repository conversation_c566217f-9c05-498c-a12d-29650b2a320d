import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { getTelegramBotStatus } from '@/lib/telegram'

export async function GET(request: NextRequest) {
  try {
    console.log('📊 Checking Telegram bot status...')
    
    const status = await getTelegramBotStatus()
    
    return successResponse({
      ...status,
      timestamp: new Date().toISOString()
    }, 'Telegram bot status retrieved successfully')
  } catch (error) {
    console.error('❌ Error getting Telegram bot status:', error)
    return serverErrorResponse('Failed to get Telegram bot status')
  }
}
