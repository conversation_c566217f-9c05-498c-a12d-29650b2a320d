import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 TESTING EMAIL DETECTION SYSTEM...')

    // Get all users with valid Google OAuth tokens and active Telegram connections
    const users = await prisma.user.findMany({
      where: {
        oauthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        },
        telegramUsers: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with valid tokens and Telegram connections`)

    let testResults = []

    for (const user of users) {
      try {
        console.log(`🔍 Testing email detection for user: ${user.email}`)

        const { gmail } = await getGoogleServices(user.id)

        // Test 1: Get ALL unread emails
        console.log(`📧 Test 1: Fetching ALL unread emails...`)
        const allUnreadMessages = await gmail.getMessages('is:unread', 50)
        console.log(`📨 Found ${allUnreadMessages.length} total unread emails`)

        // Test 2: Get recent emails (last hour)
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
        const recentQuery = `is:unread after:${Math.floor(oneHourAgo.getTime() / 1000)}`
        console.log(`📧 Test 2: Recent emails query: ${recentQuery}`)
        const recentMessages = await gmail.getMessages(recentQuery, 20)
        console.log(`📨 Found ${recentMessages.length} recent unread emails`)

        // Test 3: Check what's in our database
        const dbEmails = await prisma.email.findMany({
          where: { userId: user.id },
          orderBy: { createdAt: 'desc' },
          take: 10
        })
        console.log(`💾 Found ${dbEmails.length} emails in database for this user`)

        // Test 4: Compare Gmail vs Database
        const gmailIds = allUnreadMessages.map(msg => msg.id)
        const dbIds = dbEmails.map(email => email.gmailMessageId)
        const newEmailIds = gmailIds.filter(id => !dbIds.includes(id))
        console.log(`🆕 Found ${newEmailIds.length} emails in Gmail that are NOT in database`)

        // Test 5: Show sample email details
        if (allUnreadMessages.length > 0) {
          const sampleEmail = allUnreadMessages[0]
          const headers = sampleEmail.payload?.headers || []
          const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
          const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown Sender'
          console.log(`📧 Sample email: "${subject}" from "${from}"`)
        }

        testResults.push({
          user: user.email,
          totalUnread: allUnreadMessages.length,
          recentUnread: recentMessages.length,
          inDatabase: dbEmails.length,
          newEmails: newEmailIds.length,
          telegramUsers: user.telegramUsers.length,
          sampleEmail: allUnreadMessages.length > 0 ? {
            id: allUnreadMessages[0].id,
            subject: allUnreadMessages[0].payload?.headers?.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
          } : null
        })

      } catch (userError) {
        console.error(`❌ Error testing user ${user.email}:`, userError)
        testResults.push({
          user: user.email,
          error: userError instanceof Error ? userError.message : 'Unknown error'
        })
      }
    }

    console.log('🧪 EMAIL DETECTION TEST COMPLETE')

    return successResponse({
      testResults,
      summary: {
        usersChecked: users.length,
        timestamp: new Date().toISOString()
      }
    }, 'Email detection test completed')

  } catch (error) {
    console.error('❌ Email detection test failed:', error)
    return serverErrorResponse(`Email detection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Also allow POST
export const POST = GET
