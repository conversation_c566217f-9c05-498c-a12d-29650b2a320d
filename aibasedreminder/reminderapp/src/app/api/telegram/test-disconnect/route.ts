import { NextRequest, NextResponse } from 'next/server'
import { handleDisconnectCommand } from '@/lib/telegram'

export async function POST(request: NextRequest) {
  try {
    const { chatId } = await request.json()

    if (!chatId) {
      return NextResponse.json({
        success: false,
        message: 'Chat ID is required'
      }, { status: 400 })
    }

    console.log(`🧪 Testing disconnect command for chat ${chatId}`)

    // Call the disconnect command handler directly
    await handleDisconnectCommand(chatId)

    return NextResponse.json({
      success: true,
      message: 'Disconnect command processed successfully'
    })
  } catch (error) {
    console.error('Error testing disconnect:', error)
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
