import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { initializeTelegramBot, restartTelegramBot, getTelegramBotStatus } from '@/lib/telegram'

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Initializing Telegram bot...')

    const body = await request.json().catch(() => ({}))
    const { restart = false } = body

    const bot = restart ? await restartTelegramBot() : await initializeTelegramBot()

    // Wait a moment for polling to start
    await new Promise(resolve => setTimeout(resolve, 2000))

    const status = await getTelegramBotStatus()

    return successResponse({
      initialized: true,
      botInfo: {
        polling: status.polling,
        hasWebHook: !!process.env.TELEGRAM_WEBHOOK_URL,
        ...status.botInfo
      }
    }, 'Telegram bot initialized successfully')
  } catch (error) {
    console.error('❌ Error initializing Telegram bot:', error)
    return serverErrorResponse('Failed to initialize Telegram bot')
  }
}

export async function GET() {
  return successResponse({ status: 'Telegram bot initialization endpoint' }, 'Ready to initialize bot')
}
