import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 ERROR DEBUGGING STARTED')

    // Get the specific user
    const user = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    if (!user || !user.oauthTokens.length) {
      return serverErrorResponse('No user or tokens found')
    }

    console.log(`👤 Error debugging for: ${user.email}`)
    console.log(`📱 Active Telegram users: ${user.telegramUsers.length}`)

    const { gmail } = await getGoogleServices(user.id)

    // Get ALL unread emails
    console.log('📧 Getting ALL unread emails...')
    const unreadMessages = await gmail.getMessages('is:unread', 5) // Just get 5 for testing
    console.log(`📨 Found ${unreadMessages.length} unread emails`)

    // Get existing emails from database
    const existingEmails = await prisma.email.findMany({
      where: { userId: user.id },
      select: { 
        id: true,
        gmailMessageId: true, 
        subject: true,
        createdAt: true
      }
    })
    const existingIds = new Set(existingEmails.map(e => e.gmailMessageId))
    console.log(`💾 Found ${existingEmails.length} existing emails in database`)

    // Get database schema
    console.log('🔍 Checking database schema...')
    const emailTableInfo = await prisma.$queryRaw`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'emails'
    `
    console.log('📊 Email table schema:', emailTableInfo)

    // Try to create a test email with detailed error catching
    let errorDetails = []
    let successDetails = []

    for (const message of unreadMessages.slice(0, 1)) { // Just try the first one
      try {
        console.log(`🧪 Testing email creation for: ${message.id}`)
        
        // Extract basic info
        const headers = message.payload?.headers || []
        const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
        const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown'
        const to = headers.find((h: any) => h.name === 'To')?.value || ''
        const dateHeader = headers.find((h: any) => h.name === 'Date')?.value
        const receivedAt = dateHeader ? new Date(dateHeader) : new Date()
        const body = message.snippet || 'No content available'
        
        console.log(`📧 Email details: "${subject}" from "${from}"`)
        
        // Try different approaches to create the email
        
        // Approach 1: Minimal fields
        try {
          console.log('🧪 Approach 1: Minimal fields')
          const email1 = await prisma.email.create({
            data: {
              userId: user.id,
              gmailMessageId: message.id + '_test1',
              subject: 'Test Email 1',
              from: '<EMAIL>',
              body: 'Test body',
              isRead: false
            }
          })
          successDetails.push({
            approach: 'Minimal fields',
            emailId: email1.id,
            gmailMessageId: email1.gmailMessageId
          })
          console.log('✅ Approach 1 succeeded')
        } catch (error1) {
          console.error('❌ Approach 1 failed:', error1)
          errorDetails.push({
            approach: 'Minimal fields',
            error: error1 instanceof Error ? error1.message : 'Unknown error'
          })
        }
        
        // Approach 2: All fields
        try {
          console.log('🧪 Approach 2: All fields')
          const email2 = await prisma.email.create({
            data: {
              userId: user.id,
              gmailMessageId: message.id + '_test2',
              threadId: message.threadId || message.id + '_test2',
              subject: subject,
              from: from,
              to: to,
              body: body.substring(0, 1000),
              receivedAt: receivedAt,
              isRead: false,
              isImportant: false,
              labels: JSON.stringify(message.labelIds || [])
            }
          })
          successDetails.push({
            approach: 'All fields',
            emailId: email2.id,
            gmailMessageId: email2.gmailMessageId
          })
          console.log('✅ Approach 2 succeeded')
        } catch (error2) {
          console.error('❌ Approach 2 failed:', error2)
          errorDetails.push({
            approach: 'All fields',
            error: error2 instanceof Error ? error2.message : 'Unknown error'
          })
        }
        
        // Approach 3: Different field types
        try {
          console.log('🧪 Approach 3: Different field types')
          const email3 = await prisma.email.create({
            data: {
              userId: user.id,
              gmailMessageId: message.id + '_test3',
              subject: 'Test Email 3',
              from: '<EMAIL>',
              to: JSON.stringify(['<EMAIL>']),
              body: 'Test body',
              receivedAt: new Date(),
              isRead: false,
              isImportant: false,
              labels: '[]'
            }
          })
          successDetails.push({
            approach: 'Different field types',
            emailId: email3.id,
            gmailMessageId: email3.gmailMessageId
          })
          console.log('✅ Approach 3 succeeded')
        } catch (error3) {
          console.error('❌ Approach 3 failed:', error3)
          errorDetails.push({
            approach: 'Different field types',
            error: error3 instanceof Error ? error3.message : 'Unknown error'
          })
        }
        
      } catch (error) {
        console.error(`❌ Error testing email creation:`, error)
        errorDetails.push({
          approach: 'Overall test',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return successResponse({
      unreadEmails: unreadMessages.length,
      existingEmails: existingEmails.length,
      databaseSchema: emailTableInfo,
      errorDetails,
      successDetails,
      timestamp: new Date().toISOString()
    }, 'Error debugging completed')

  } catch (error) {
    console.error('❌ Error debugging failed:', error)
    return serverErrorResponse(`Error debugging failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
