import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { sendMessage } from '@/lib/telegram'
import { getGoogleServices } from '@/lib/google'

// In-memory user state management for reply conversations
interface UserReplyState {
  emailId: string
  emailSubject: string
  emailFrom: string
  timestamp: number
}

const userReplyStates = new Map<string, UserReplyState>()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('📱 Telegram webhook received:', JSON.stringify(body, null, 2))

    // Process updates IMMEDIATELY and SYNCHRONOUSLY
    let processed = false

    if (body.message) {
      console.log('🔄 Processing message immediately...')
      await handleMessage(body.message)
      processed = true
    } else if (body.callback_query) {
      console.log('🔄 Processing callback query immediately...')
      await handleCallbackQuery(body.callback_query)
      processed = true
    }

    // Trigger email check in background (non-blocking)
    triggerEmailCheckAsync().catch(error => {
      console.error('❌ Background email check failed:', error)
    })

    // Return success response
    return new Response(JSON.stringify({
      ok: true,
      processed: processed,
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('❌ Telegram webhook error:', error)

    // Still return 200 to prevent Telegram retries
    return new Response(JSON.stringify({
      ok: true,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

// ENHANCED self-triggering email check system
let lastEmailCheck = 0
let emailCheckInProgress = false

async function triggerEmailCheckAsync() {
  try {
    // Prevent multiple simultaneous checks
    if (emailCheckInProgress) {
      console.log('⏳ Email check already in progress, skipping...')
      return
    }

    // Check emails every 1 minute for better responsiveness
    const now = Date.now()
    const oneMinute = 1 * 60 * 1000

    if (now - lastEmailCheck < oneMinute) {
      console.log(`⏰ Email check too recent (${Math.round((now - lastEmailCheck) / 1000)}s ago), skipping...`)
      return
    }

    emailCheckInProgress = true
    lastEmailCheck = now
    console.log('🔍 Triggering ENHANCED email check...')

    // Call the email monitoring function directly
    await performEmailCheck()

  } catch (error) {
    console.error('❌ Self-triggering email check failed:', error)
  } finally {
    emailCheckInProgress = false
  }
}

// Autonomous email checking function (moved from autonomous-init)
async function performEmailCheck() {
  try {
    console.log('🔍 Running email check...')

    // Get all users with valid Google OAuth tokens and active Telegram connections
    const users = await prisma.user.findMany({
      where: {
        oauthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        },
        telegramUsers: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with valid tokens and Telegram connections`)

    let totalNewEmails = 0
    let totalNotifications = 0

    for (const user of users) {
      try {
        console.log(`📧 Checking emails for user: ${user.email}`)

        const { gmail } = await getGoogleServices(user.id)

        // Get recent unread emails (simplified for free plan)
        const unreadMessages = await gmail.getMessages('is:unread', 10)

        console.log(`📨 Found ${unreadMessages.length} unread emails for ${user.email}`)

        let userNewEmails = 0
        let userNotifications = 0

        // Process unread emails (check for new ones)
        for (const gmailMessage of unreadMessages) {
          try {
            // Check if we already have this email
            const existingEmail = await prisma.email.findUnique({
              where: { gmailMessageId: gmailMessage.id }
            })

            if (existingEmail) {
              continue // Skip if already processed
            }

            // Extract basic info from the message
            const headers = gmailMessage.payload?.headers || []
            const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
            const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown Sender'
            const to = headers.find((h: any) => h.name === 'To')?.value || ''
            const dateHeader = headers.find((h: any) => h.name === 'Date')?.value
            const receivedAt = dateHeader ? new Date(dateHeader) : new Date()

            // Extract body text (simplified)
            let body = gmailMessage.snippet || 'No content available'

            // Create new email record
            const newEmail = await prisma.email.create({
              data: {
                userId: user.id,
                gmailMessageId: gmailMessage.id,
                threadId: gmailMessage.threadId || gmailMessage.id,
                subject: subject,
                from: from,
                to: to,
                body: body.substring(0, 1000), // Limit body length
                receivedAt: receivedAt,
                isRead: false,
                isImportant: false,
                labels: JSON.stringify(gmailMessage.labelIds || [])
              }
            })

            userNewEmails++
            console.log(`✅ Created new email: ${subject} from ${from}`)

            // Send ENHANCED Telegram notification with reply buttons
            for (const telegramUser of user.telegramUsers) {
              const notificationMessage = `🚨 NEW EMAIL RECEIVED!\n\n` +
                `📝 Subject: ${subject}\n` +
                `👤 From: ${from}\n` +
                `📅 Received: ${receivedAt.toLocaleString()}\n\n` +
                `📄 Preview:\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}\n\n` +
                `💬 Click "Reply" below to respond with input field!`

              // Create ENHANCED reply button with better UX
              const keyboard = {
                inline_keyboard: [
                  [
                    { text: "💬 Reply to this Email", callback_data: `reply_to_${newEmail.id}` },
                    { text: "✅ Mark as Read", callback_data: `mark_read_${newEmail.id}` }
                  ],
                  [
                    { text: "⭐ Star Email", callback_data: `star_email_${newEmail.id}` },
                    { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${newEmail.id}` }
                  ],
                  [
                    { text: "📧 View All Emails", callback_data: "view_emails" },
                    { text: "🔄 Check for More", callback_data: "check_more_emails" }
                  ]
                ]
              }

              // Use direct Telegram API call with enhanced error handling
              try {
                const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    chat_id: telegramUser.telegramId,
                    text: notificationMessage,
                    reply_markup: keyboard,
                    parse_mode: 'HTML'
                  })
                })

                const result = await response.json()
                if (result.ok) {
                  userNotifications++
                  console.log(`📱 ✅ Sent notification to Telegram user ${telegramUser.telegramId}`)
                } else {
                  console.error(`📱 ❌ Failed to send notification:`, result)
                }
              } catch (notificationError) {
                console.error(`📱 ❌ Error sending notification to ${telegramUser.telegramId}:`, notificationError)
              }
            }

          } catch (emailError) {
            console.error(`❌ Error processing email ${gmailMessage.id}:`, emailError)
          }
        }

        totalNewEmails += userNewEmails
        totalNotifications += userNotifications

        console.log(`✅ User ${user.email}: ${userNewEmails} new emails, ${userNotifications} notifications sent`)

      } catch (userError) {
        console.error(`❌ Error checking emails for user ${user.email}:`, userError)
      }
    }

    console.log(`🎉 Email check completed: ${totalNewEmails} new emails, ${totalNotifications} notifications sent`)

  } catch (error) {
    console.error('❌ Email check failed:', error)
  }
}



export async function handleMessage(message: any) {
  const chatId = message.chat.id
  const text = message.text?.trim()
  const userId = message.from.id

  console.log(`📨 Processing message IMMEDIATELY from ${userId}: "${text}"`)

  if (!text) return

  try {
    // Check if user is in reply mode FIRST
    const userKey = userId.toString()
    const replyState = userReplyStates.get(userKey)

    if (replyState && !text.startsWith('/')) {
      // User is replying to an email - IMMEDIATE processing
      console.log(`💬 Processing email reply immediately: "${text}"`)
      await handleDirectEmailReply(chatId, userId, text, replyState)
      return
    }

    if (text === '/start') {
      await sendMessage(chatId, 
        `🤖 Welcome to ReminderAPP Bot!\n\n` +
        `I can help you receive instant notifications for:\n` +
        `📧 New emails\n` +
        `📅 Calendar events\n` +
        `⏰ Custom reminders\n\n` +
        `To get started:\n` +
        `1. Sign in to your ReminderAPP dashboard\n` +
        `2. Go to Settings → Telegram\n` +
        `3. Generate a link code\n` +
        `4. Send me: /link YOUR_CODE\n\n` +
        `Commands:\n` +
        `/start - Show this welcome message\n` +
        `/link CODE - Link your account\n` +
        `/status - Check your account status\n` +
        `/emails - View recent emails\n` +
        `/reply - Reply to emails (interactive)\n` +
        `/cancel - Cancel current reply\n` +
        `/disconnect - Disconnect your account\n` +
        `/help - Show available commands`
      )
    } else if (text === '/help') {
      await sendMessage(chatId,
        `🆘 Available Commands:\n\n` +
        `/start - Welcome message and setup guide\n` +
        `/link CODE - Link your Telegram to ReminderAPP\n` +
        `/status - Check your account linking status\n` +
        `/emails - View your recent emails\n` +
        `/reply - Reply to an email (interactive)\n` +
        `/cancel - Cancel current reply\n` +
        `/disconnect - Disconnect your linked account\n` +
        `/help - Show this help message\n\n` +
        `📧 Email Management:\n` +
        `• Get instant notifications for new emails\n` +
        `• View and categorize your emails\n` +
        `• Reply directly from Telegram with simple text\n` +
        `• Click email buttons to start replying\n\n` +
        `💬 How to Reply:\n` +
        `1. Use /reply to see unread emails\n` +
        `2. Click on an email to reply to\n` +
        `3. Simply type your response (no special format)\n` +
        `4. Your reply will be sent via Gmail\n\n` +
        `Need more help? Visit your ReminderAPP dashboard for detailed instructions.`
      )
    } else if (text === '/status') {
      const telegramUser = await prisma.telegramUser.findUnique({
        where: { telegramId: userId.toString() },
        include: { user: true }
      })

      const userKey = userId.toString()
      const replyState = userReplyStates.get(userKey)
      let replyStatus = ''

      if (replyState) {
        replyStatus = `\n💬 Reply Mode: ACTIVE\n` +
          `📧 Replying to: ${replyState.emailSubject}\n` +
          `👤 From: ${replyState.emailFrom}\n` +
          `⏰ Started: ${new Date(replyState.timestamp).toLocaleString()}\n` +
          `❌ Use /cancel to exit reply mode\n`
      }

      if (telegramUser) {
        await sendMessage(chatId,
          `✅ Account Status: LINKED\n\n` +
          `📧 Email: ${telegramUser.user.email}\n` +
          `👤 Name: ${telegramUser.user.name || 'Not set'}\n` +
          `🔗 Linked: ${telegramUser.createdAt.toLocaleDateString()}\n` +
          `📱 Active: ${telegramUser.isActive ? 'Yes' : 'No'}${replyStatus}\n\n` +
          `You'll receive notifications for new emails and reminders!`
        )
      } else {
        await sendMessage(chatId,
          `❌ Account Status: NOT LINKED\n\n` +
          `To link your account:\n` +
          `1. Go to ReminderAPP dashboard\n` +
          `2. Navigate to Settings → Telegram\n` +
          `3. Generate a link code\n` +
          `4. Send me: /link YOUR_CODE`
        )
      }
    } else if (text === '/disconnect') {
      // Handle disconnect command
      const telegramUser = await prisma.telegramUser.findUnique({
        where: { telegramId: userId.toString() },
        include: { user: true }
      })

      if (!telegramUser) {
        await sendMessage(chatId,
          `❌ No account linked!\n\n` +
          `You don't have any account linked to this Telegram.\n` +
          `Use /link CODE to link your account first.`
        )
        return
      }

      // Send confirmation message with inline keyboard
      const confirmationMessage =
        `⚠️ Disconnect Account?\n\n` +
        `📧 Email: ${telegramUser.user.email}\n` +
        `👤 Name: ${telegramUser.user.name || 'Not set'}\n\n` +
        `Are you sure you want to disconnect this account?\n` +
        `You'll stop receiving notifications until you link again.`

      const keyboard = {
        inline_keyboard: [
          [
            { text: "✅ Yes, Disconnect", callback_data: `disconnect_confirm_${telegramUser.id}` },
            { text: "❌ Cancel", callback_data: "disconnect_cancel" }
          ]
        ]
      }

      await sendMessage(chatId, confirmationMessage, { replyMarkup: keyboard })
    } else if (text.startsWith('/link ')) {
      const code = text.replace('/link ', '').trim().toUpperCase()
      
      if (code.length !== 8) {
        await sendMessage(chatId,
          `❌ Invalid link code format!\n\n` +
          `Please use: /link YOUR_8_DIGIT_CODE\n` +
          `Example: /link ABC12345`
        )
        return
      }

      // Find the link code in database
      const linkCode = await prisma.telegramLinkCode.findUnique({
        where: { code },
        include: { user: true }
      })

      if (!linkCode) {
        await sendMessage(chatId,
          `❌ Invalid or expired link code!\n\n` +
          `Please generate a new code from your ReminderAPP dashboard.`
        )
        return
      }

      if (linkCode.isUsed) {
        await sendMessage(chatId,
          `❌ This link code has already been used!\n\n` +
          `Please generate a new code from your dashboard.`
        )
        return
      }

      if (linkCode.expiresAt < new Date()) {
        await sendMessage(chatId,
          `❌ This link code has expired!\n\n` +
          `Please generate a new code from your dashboard.`
        )
        return
      }

      // Check if user is already linked
      const existingTelegramUser = await prisma.telegramUser.findUnique({
        where: { telegramId: userId.toString() }
      })

      if (existingTelegramUser) {
        await sendMessage(chatId,
          `⚠️ Your Telegram is already linked to another account!\n\n` +
          `If you want to link to a different account, please contact support.`
        )
        return
      }

      // Create the Telegram user link
      await prisma.telegramUser.create({
        data: {
          userId: linkCode.userId,
          telegramId: userId.toString(),
          username: message.from.username || null,
          firstName: message.from.first_name,
          lastName: message.from.last_name || null,
          isActive: true
        }
      })

      // Mark the link code as used
      await prisma.telegramLinkCode.update({
        where: { id: linkCode.id },
        data: { isUsed: true }
      })

      await sendMessage(chatId,
        `🎉 Successfully linked your Telegram account!\n\n` +
        `📧 Email: ${linkCode.user.email}\n` +
        `👤 Name: ${linkCode.user.name || 'Not set'}\n\n` +
        `You'll now receive instant notifications for:\n` +
        `📧 New emails\n` +
        `📅 Calendar events\n` +
        `⏰ Custom reminders\n\n` +
        `Welcome to ReminderAPP! 🚀`
      )

      console.log(`✅ Successfully linked Telegram user ${userId} to account ${linkCode.user.email}`)
    } else if (text === '/emails') {
      await handleEmailsCommand(chatId, userId)
    } else if (text === '/reply') {
      await handleReplyCommand(chatId, userId)
    } else if (text.startsWith('reply:')) {
      await handleEmailReply(chatId, userId, text)
    } else if (text === '/cancel') {
      await handleCancelReply(chatId, userId)
    } else {
      await sendMessage(chatId,
        `🤔 I don't understand that command.\n\n` +
        `Try one of these:\n` +
        `/start - Get started\n` +
        `/help - Show available commands\n` +
        `/status - Check account status\n` +
        `/disconnect - Disconnect account\n` +
        `/link CODE - Link your account`
      )
    }
  } catch (error) {
    console.error('❌ Error handling message:', error)
    await sendMessage(chatId,
      `❌ Sorry, something went wrong processing your request.\n\n` +
      `Please try again in a moment.`
    )
  }
}

export async function handleCallbackQuery(callbackQuery: any) {
  const chatId = callbackQuery.message.chat.id
  const data = callbackQuery.data
  const userId = callbackQuery.from.id

  console.log(`🔘 Processing callback query IMMEDIATELY: ${data}`)

  try {
    // Answer the callback query IMMEDIATELY to remove loading state
    await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        callback_query_id: callbackQuery.id,
        text: 'Processing your request...'
      })
    })

    if (data === 'disconnect_cancel') {
      await sendMessage(chatId,
        `✅ Disconnect cancelled.\n\n` +
        `Your account remains linked and active.`
      )
    } else if (data.startsWith('disconnect_confirm_')) {
      const telegramUserId = data.replace('disconnect_confirm_', '')

      // Find and delete the telegram user connection
      const telegramUser = await prisma.telegramUser.findUnique({
        where: { id: telegramUserId },
        include: { user: true }
      })

      if (!telegramUser) {
        await sendMessage(chatId,
          `❌ Account connection not found.\n\n` +
          `It may have already been disconnected.`
        )
        return
      }

      // Delete the telegram user connection
      await prisma.telegramUser.delete({
        where: { id: telegramUserId }
      })

      await sendMessage(chatId,
        `✅ Account Disconnected Successfully!\n\n` +
        `📧 ${telegramUser.user.email} has been disconnected from this Telegram.\n\n` +
        `You'll no longer receive notifications.\n` +
        `To reconnect, use /link with a new code from your dashboard.`
      )

      console.log(`✅ Successfully disconnected Telegram user ${userId} from account ${telegramUser.user.email}`)
    } else if (data.startsWith('mark_read_')) {
      const emailId = data.replace('mark_read_', '')
      await handleMarkEmailAsRead(chatId, userId, emailId)
    } else if (data.startsWith('star_email_')) {
      const emailId = data.replace('star_email_', '')
      await handleStarEmail(chatId, userId, emailId)
    } else if (data.startsWith('stop_reminders_')) {
      const emailId = data.replace('stop_reminders_', '')
      await handleStopEmailReminders(chatId, userId, emailId)
    } else if (data.startsWith('reply_to_')) {
      const emailId = data.replace('reply_to_', '')
      await handleReplyToEmail(chatId, userId, emailId)
    } else if (data === 'view_emails') {
      await handleEmailsCommand(chatId, userId)
    } else if (data === 'reply_another') {
      await handleReplyCommand(chatId, userId)
    } else if (data === 'check_more_emails') {
      await handleCheckMoreEmails(chatId, userId)
    } else {
      await sendMessage(chatId, `Callback received: ${data}`)
    }
  } catch (error) {
    console.error('❌ Error handling callback query:', error)
    await sendMessage(chatId,
      `❌ Sorry, something went wrong processing your request.\n\n` +
      `Please try again in a moment.`
    )
  }
}

async function handleMarkEmailAsRead(chatId: string, telegramUserId: number, emailId: string) {
  try {
    // Find the telegram user and associated email
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: telegramUserId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId, `❌ Account not linked. Use /link to connect your account.`)
      return
    }

    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: telegramUser.userId
      }
    })

    if (!email) {
      await sendMessage(chatId, `❌ Email not found or access denied.`)
      return
    }

    // Mark email as read in database
    await prisma.email.update({
      where: { id: emailId },
      data: { isRead: true }
    })

    // Cancel any pending reminders for this email
    await prisma.reminder.updateMany({
      where: {
        emailId: emailId,
        status: 'PENDING'
      },
      data: {
        status: 'CANCELLED'
      }
    })

    await sendMessage(chatId,
      `✅ Email marked as read!\n\n` +
      `📧 "${email.subject}"\n\n` +
      `All reminders for this email have been stopped.`
    )

    console.log(`✅ Email ${emailId} marked as read by Telegram user ${telegramUserId}`)
  } catch (error) {
    console.error('Error marking email as read:', error)
    await sendMessage(chatId, `❌ Failed to mark email as read. Please try again.`)
  }
}

async function handleStarEmail(chatId: string, telegramUserId: number, emailId: string) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: telegramUserId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId, `❌ Account not linked. Use /link to connect your account.`)
      return
    }

    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: telegramUser.userId
      }
    })

    if (!email) {
      await sendMessage(chatId, `❌ Email not found or access denied.`)
      return
    }

    // Note: This would require Gmail API integration to actually star the email
    // For now, we'll just acknowledge the action
    await sendMessage(chatId,
      `⭐ Star Email Action\n\n` +
      `📧 "${email.subject}"\n\n` +
      `To star this email, please open Gmail directly.\n` +
      `Gmail integration for starring emails will be added in a future update.`
    )

    console.log(`⭐ Star email request for ${emailId} by Telegram user ${telegramUserId}`)
  } catch (error) {
    console.error('Error starring email:', error)
    await sendMessage(chatId, `❌ Failed to star email. Please try again.`)
  }
}

async function handleStopEmailReminders(chatId: string, telegramUserId: number, emailId: string) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: telegramUserId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId, `❌ Account not linked. Use /link to connect your account.`)
      return
    }

    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: telegramUser.userId
      }
    })

    if (!email) {
      await sendMessage(chatId, `❌ Email not found or access denied.`)
      return
    }

    // Cancel all pending reminders for this email
    const cancelledReminders = await prisma.reminder.updateMany({
      where: {
        emailId: emailId,
        status: 'PENDING'
      },
      data: {
        status: 'CANCELLED'
      }
    })

    await sendMessage(chatId,
      `🔕 Reminders Stopped!\n\n` +
      `📧 "${email.subject}"\n\n` +
      `Cancelled ${cancelledReminders.count} pending reminder(s).\n` +
      `You won't receive more notifications for this email.`
    )

    console.log(`🔕 Stopped ${cancelledReminders.count} reminders for email ${emailId} by Telegram user ${telegramUserId}`)
  } catch (error) {
    console.error('Error stopping email reminders:', error)
    await sendMessage(chatId, `❌ Failed to stop reminders. Please try again.`)
  }
}

// Handle /emails command - show recent emails
async function handleEmailsCommand(chatId: number, userId: number) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: userId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId.toString(),
        `❌ Account not linked!\n\n` +
        `Please link your account first using /link CODE`
      )
      return
    }

    // Get recent emails
    const emails = await prisma.email.findMany({
      where: { userId: telegramUser.userId },
      orderBy: { receivedAt: 'desc' },
      take: 10
    })

    if (emails.length === 0) {
      await sendMessage(chatId.toString(),
        `📭 No emails found!\n\n` +
        `Your recent emails will appear here once they're synced.`
      )
      return
    }

    let message = `📧 Your Recent Emails (${emails.length}):\n\n`

    emails.forEach((email, index) => {
      const status = email.isRead ? '✅' : '📩'
      const date = email.receivedAt.toLocaleDateString()
      message += `${status} ${index + 1}. ${email.subject}\n`
      message += `   From: ${email.from}\n`
      message += `   Date: ${date}\n`
      if (!email.isRead) {
        message += `   📱 Reply: /reply ${email.id}\n`
      }
      message += `\n`
    })

    message += `💡 Use /reply <email_id> to respond to any email!`

    await sendMessage(chatId.toString(), message)
  } catch (error) {
    console.error('Error handling emails command:', error)
    await sendMessage(chatId.toString(), `❌ Failed to fetch emails. Please try again.`)
  }
}

// Handle /reply command - show interactive reply options
async function handleReplyCommand(chatId: number, userId: number) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: userId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId.toString(),
        `❌ Account not linked!\n\n` +
        `Please link your account first using /link CODE`
      )
      return
    }

    // Get recent unread emails
    const unreadEmails = await prisma.email.findMany({
      where: {
        userId: telegramUser.userId,
        isRead: false
      },
      orderBy: { receivedAt: 'desc' },
      take: 5
    })

    if (unreadEmails.length === 0) {
      await sendMessage(chatId.toString(),
        `📭 No unread emails to reply to!\n\n` +
        `Use /emails to see all your recent emails.`
      )
      return
    }

    let message = `📧 Select an email to reply to:\n\n`

    // Create interactive buttons for each email
    const keyboard = {
      inline_keyboard: unreadEmails.map((email) => [
        {
          text: `📩 ${email.subject.substring(0, 30)}${email.subject.length > 30 ? '...' : ''}`,
          callback_data: `reply_to_${email.id}`
        }
      ])
    }

    unreadEmails.forEach((email, index) => {
      const date = email.receivedAt.toLocaleDateString()
      message += `${index + 1}. ${email.subject}\n`
      message += `   From: ${email.from}\n`
      message += `   Date: ${date}\n\n`
    })

    message += `💡 Click on an email above to start replying, or use:\n`
    message += `reply:<email_id>:Your message\n\n`
    message += `Example: reply:${unreadEmails[0].id}:Thank you!`

    // Send message with interactive buttons
    await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: chatId.toString(),
        text: message,
        reply_markup: keyboard,
        parse_mode: 'HTML'
      })
    })

  } catch (error) {
    console.error('Error handling reply command:', error)
    await sendMessage(chatId.toString(), `❌ Failed to show reply options. Please try again.`)
  }
}

// Handle check more emails button
async function handleCheckMoreEmails(chatId: number, userId: number) {
  try {
    await sendMessage(chatId.toString(),
      `🔄 Checking for new emails...\n\n` +
      `This will trigger an immediate email check for your account.`
    )

    // Trigger immediate email check
    await triggerEmailCheckAsync()

    await sendMessage(chatId.toString(),
      `✅ Email check completed!\n\n` +
      `If there were new emails, you should have received notifications.`
    )
  } catch (error) {
    console.error('Error checking more emails:', error)
    await sendMessage(chatId.toString(), `❌ Failed to check emails. Please try again.`)
  }
}

// Handle reply to specific email - set user in reply mode with FORCE INPUT FIELD
async function handleReplyToEmail(chatId: number, userId: number, emailId: string) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: userId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId.toString(),
        `❌ Account not linked!\n\n` +
        `Please link your account first using /link CODE`
      )
      return
    }

    // Get the specific email
    const email = await prisma.email.findUnique({
      where: {
        id: emailId,
        userId: telegramUser.userId
      }
    })

    if (!email) {
      await sendMessage(chatId.toString(), `❌ Email not found or you don't have access to it.`)
      return
    }

    // Set user in reply mode
    const userKey = userId.toString()
    userReplyStates.set(userKey, {
      emailId: email.id,
      emailSubject: email.subject,
      emailFrom: email.from,
      timestamp: Date.now()
    })

    // Show email details and FORCE INPUT with reply keyboard
    const emailBody = email.body.length > 200 ? email.body.substring(0, 200) + '...' : email.body

    const message = `📧 REPLY MODE ACTIVATED\n\n` +
      `📝 Subject: ${email.subject}\n` +
      `👤 From: ${email.from}\n` +
      `📅 Date: ${email.receivedAt.toLocaleDateString()}\n\n` +
      `📄 Original message:\n${emailBody}\n\n` +
      `✍️ **TYPE YOUR REPLY NOW:**`

    // Send message with FORCE REPLY to show input field - ENHANCED VERSION
    const forceReplyResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: chatId.toString(),
        text: message,
        reply_markup: {
          force_reply: true,
          input_field_placeholder: "✍️ Type your email reply here and press send...",
          selective: true
        },
        parse_mode: 'Markdown'
      })
    })

    const forceReplyResult = await forceReplyResponse.json()
    console.log('📱 Force reply result:', forceReplyResult)

    if (!forceReplyResult.ok) {
      console.error('❌ Force reply failed:', forceReplyResult)
      // Fallback: send simple message asking for reply
      await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: chatId.toString(),
          text: `⚠️ Input field not available. Please type your reply as a regular message.\n\nYour next message will be sent as a reply to this email.`
        })
      })
    }

    // Also send quick reply options
    const quickReplyKeyboard = {
      keyboard: [
        [{ text: "Thank you for your email!" }],
        [{ text: "I'll get back to you soon." }],
        [{ text: "Could you please provide more details?" }],
        [{ text: "Let's schedule a meeting." }],
        [{ text: "❌ Cancel Reply" }]
      ],
      resize_keyboard: true,
      one_time_keyboard: true,
      input_field_placeholder: "Type your custom reply or choose from options above"
    }

    await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: chatId.toString(),
        text: `💬 Quick Reply Options:\n(Or type your custom message)`,
        reply_markup: quickReplyKeyboard
      })
    })

    // Set timeout to clear reply state after 10 minutes
    setTimeout(() => {
      if (userReplyStates.has(userKey)) {
        userReplyStates.delete(userKey)
        sendMessage(chatId.toString(), `⏰ Reply timeout. Use /reply to start again.`)
      }
    }, 10 * 60 * 1000) // 10 minutes

  } catch (error) {
    console.error('Error handling reply to email:', error)
    await sendMessage(chatId.toString(), `❌ Failed to prepare reply. Please try again.`)
  }
}

// Handle cancel reply command
async function handleCancelReply(chatId: number, userId: number) {
  const userKey = userId.toString()
  const replyState = userReplyStates.get(userKey)

  if (replyState) {
    userReplyStates.delete(userKey)
    await sendMessage(chatId.toString(),
      `❌ Reply cancelled.\n\n` +
      `You are no longer replying to: "${replyState.emailSubject}"\n\n` +
      `Use /reply to start a new reply.`
    )
  } else {
    await sendMessage(chatId.toString(),
      `ℹ️ No active reply to cancel.\n\n` +
      `Use /reply to start replying to an email.`
    )
  }
}

// Handle direct email reply when user is in reply mode
async function handleDirectEmailReply(chatId: number, userId: number, replyText: string, replyState: UserReplyState) {
  try {
    const userKey = userId.toString()

    // Handle cancel command
    if (replyText.toLowerCase().includes('cancel') || replyText === '❌ Cancel Reply') {
      userReplyStates.delete(userKey)

      // Remove custom keyboard
      await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: chatId.toString(),
          text: `❌ Reply cancelled.\n\nYou are no longer replying to: "${replyState.emailSubject}"`,
          reply_markup: { remove_keyboard: true }
        })
      })
      return
    }

    // Clear the reply state immediately
    userReplyStates.delete(userKey)

    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: userId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId.toString(),
        `❌ Account not linked!\n\n` +
        `Please link your account first using /link CODE`
      )
      return
    }

    // Get the email
    const email = await prisma.email.findUnique({
      where: {
        id: replyState.emailId,
        userId: telegramUser.userId
      }
    })

    if (!email) {
      await sendMessage(chatId.toString(), `❌ Email not found or you don't have access to it.`)
      return
    }

    // Remove custom keyboard and send confirmation
    await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: chatId.toString(),
        text: `📤 Sending your reply...\n\n` +
          `📧 To: ${email.from}\n` +
          `📝 Subject: Re: ${email.subject}\n` +
          `💬 Your message: "${replyText.substring(0, 100)}${replyText.length > 100 ? '...' : ''}"\n\n` +
          `⏳ Please wait...`,
        reply_markup: { remove_keyboard: true }
      })
    })

    // Send the reply via Gmail API
    try {
      const { gmail } = await getGoogleServices(telegramUser.userId)

      // Send the reply using Gmail API
      await gmail.sendReply(email.gmailMessageId, email.threadId || email.gmailMessageId, {
        to: [email.from],
        subject: `Re: ${email.subject}`,
        body: replyText
      })

      // Mark email as read
      await prisma.email.update({
        where: { id: replyState.emailId },
        data: {
          isRead: true
        }
      })

      // Send success confirmation with action buttons
      const successKeyboard = {
        inline_keyboard: [
          [
            { text: "📧 View More Emails", callback_data: "view_emails" },
            { text: "💬 Reply to Another", callback_data: "reply_another" }
          ]
        ]
      }

      await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: chatId.toString(),
          text: `✅ Reply sent successfully!\n\n` +
            `📧 To: ${email.from}\n` +
            `📝 Subject: Re: ${email.subject}\n` +
            `📅 Sent: ${new Date().toLocaleString()}\n\n` +
            `The email has been marked as read.`,
          reply_markup: successKeyboard
        })
      })

      console.log(`✅ Email reply sent successfully via Telegram for email ${replyState.emailId}`)

    } catch (replyError) {
      console.error('Error sending reply:', replyError)

      // Send error message
      await sendMessage(chatId.toString(),
        `❌ Failed to send reply!\n\n` +
        `Error: ${replyError instanceof Error ? replyError.message : 'Unknown error'}\n\n` +
        `Please try again or check your email connection.`
      )
    }

  } catch (error) {
    console.error('Error handling direct email reply:', error)
    await sendMessage(chatId.toString(), `❌ Failed to send reply. Please try again.`)
  }
}

// Handle email reply - send reply via Gmail
async function handleEmailReply(chatId: number, userId: number, text: string) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: userId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId.toString(),
        `❌ Account not linked!\n\n` +
        `Please link your account first using /link CODE`
      )
      return
    }

    // Parse the reply format: reply:emailId:message
    const parts = text.split(':')
    if (parts.length < 3) {
      await sendMessage(chatId.toString(),
        `❌ Invalid reply format!\n\n` +
        `Use: reply:<email_id>:Your message\n` +
        `Example: reply:123:Thank you for your email!`
      )
      return
    }

    const emailId = parts[1]
    const replyMessage = parts.slice(2).join(':').trim()

    if (!replyMessage) {
      await sendMessage(chatId.toString(),
        `❌ Reply message cannot be empty!\n\n` +
        `Please include your message after the email ID.`
      )
      return
    }

    // Get the email
    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: telegramUser.userId
      }
    })

    if (!email) {
      await sendMessage(chatId.toString(),
        `❌ Email not found!\n\n` +
        `Please check the email ID and try again.`
      )
      return
    }

    // Send the reply via Gmail API
    try {
      const { gmail } = await getGoogleServices(telegramUser.userId)

      // Send status message
      await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: chatId.toString(),
          text: `📤 Sending your reply...\n\nTo: ${email.from}\nSubject: Re: ${email.subject}\nMessage: ${replyMessage}`,
          parse_mode: 'HTML'
        })
      })

      // Send the reply using Gmail API
      await gmail.sendReply(email.gmailMessageId, email.threadId || email.gmailMessageId, {
        to: [email.from],
        subject: `Re: ${email.subject}`,
        body: replyMessage
      })

      // Mark email as read
      await prisma.email.update({
        where: { id: emailId },
        data: {
          isRead: true
        }
      })

      // Send success confirmation
      await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: chatId.toString(),
          text: `✅ Reply sent successfully!\n\n📧 Your response has been sent to ${email.from}\n📝 Subject: Re: ${email.subject}\n\n🔕 Email marked as read - reminders stopped.`,
          parse_mode: 'HTML'
        })
      })

      console.log(`✅ Email reply sent successfully for email ${emailId}`)

    } catch (replyError) {
      console.error('Error sending reply:', replyError)

      // Send error message
      await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: chatId.toString(),
          text: `❌ Failed to send reply!\n\nError: ${replyError instanceof Error ? replyError.message : 'Unknown error'}\n\nPlease try again or reply directly in Gmail.`,
          parse_mode: 'HTML'
        })
      })
    }

  } catch (error) {
    console.error('Error handling email reply:', error)
    await sendMessage(chatId.toString(), `❌ Failed to send reply. Please try again.`)
  }
}

export async function GET() {
  try {
    // Check bot status
    const botResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getMe`)
    const botInfo = await botResponse.json()

    return successResponse({
      status: 'Telegram webhook endpoint active',
      bot: botInfo.ok ? {
        id: botInfo.result.id,
        username: botInfo.result.username,
        first_name: botInfo.result.first_name
      } : 'Bot not accessible',
      autonomous: true,
      timestamp: new Date().toISOString()
    }, 'Webhook endpoint is ready and autonomous')
  } catch (error) {
    return successResponse({
      status: 'Telegram webhook endpoint active',
      bot: 'Status check failed',
      autonomous: true,
      timestamp: new Date().toISOString()
    }, 'Webhook endpoint is ready')
  }
}
