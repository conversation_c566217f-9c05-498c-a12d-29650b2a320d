import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 FORCING Telegram bot to be active immediately...')

    // Get the correct webhook URL
    const webhookUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`
    console.log('🔗 Setting webhook URL:', webhookUrl)

    // FORCE delete any existing webhook
    const deleteResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/deleteWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })

    const deleteResult = await deleteResponse.json()
    console.log('🗑️ Webhook deletion:', deleteResult)

    // Wait 3 seconds for cleanup
    await new Promise(resolve => setTimeout(resolve, 3000))

    // FORCE set new webhook with GUARANTEED settings
    const webhookResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/setWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message', 'callback_query'],
        drop_pending_updates: true,
        max_connections: 100,
        secret_token: 'webhook_secret_2024'
      })
    })

    const webhookResult = await webhookResponse.json()
    console.log('📱 FORCED webhook setup:', webhookResult)

    if (!webhookResult.ok) {
      console.error('❌ Webhook setup failed:', webhookResult)
      throw new Error(`Webhook setup failed: ${webhookResult.description}`)
    }

    // Test bot immediately with getMe
    const botResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getMe`)
    const botInfo = await botResponse.json()
    console.log('🤖 Bot test:', botInfo)

    // Get webhook info to verify
    const webhookInfoResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getWebhookInfo`)
    const webhookInfo = await webhookInfoResponse.json()
    console.log('ℹ️ Webhook info:', webhookInfo)

    return successResponse({
      forced: true,
      webhook: webhookResult,
      bot: botInfo,
      webhookInfo: webhookInfo.result,
      timestamp: new Date().toISOString(),
      message: 'Bot FORCED to be active - should respond immediately now!'
    }, 'Telegram bot FORCED to active state')

  } catch (error) {
    console.error('❌ Failed to force bot active:', error)
    return serverErrorResponse(`Failed to force bot active: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Also allow POST
export const POST = GET
