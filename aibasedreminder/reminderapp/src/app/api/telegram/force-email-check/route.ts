import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET() {
  try {
    console.log('🚀 FORCE EMAIL CHECK - Processing ALL emails for ALL connected users')

    // Get all users with active Telegram connections and valid Google tokens
    const users = await prisma.user.findMany({
      where: {
        oauthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        },
        telegramUsers: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with active Telegram and valid Google tokens`)

    let totalEmailsProcessed = 0
    let totalNotificationsSent = 0

    for (const user of users) {
      try {
        console.log(`📧 FORCE PROCESSING emails for: ${user.email}`)

        const { gmail } = await getGoogleServices(user.id)

        // Get ALL unread emails (no time limit)
        const unreadMessages = await gmail.getMessages('is:unread', 100)
        console.log(`📨 Found ${unreadMessages.length} unread emails for ${user.email}`)

        // Get existing emails to avoid duplicates
        const existingEmails = await prisma.email.findMany({
          where: { userId: user.id },
          select: { gmailMessageId: true }
        })
        const existingIds = new Set(existingEmails.map(e => e.gmailMessageId))

        let emailsCreated = 0
        let notificationsSent = 0

        for (const message of unreadMessages) {
          try {
            // Skip if already exists
            if (existingIds.has(message.id)) {
              console.log(`⏭️ Skipping existing email: ${message.id}`)
              continue
            }

            // Extract email data
            const headers = message.payload?.headers || []
            const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
            const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown'
            const to = headers.find((h: any) => h.name === 'To')?.value || ''
            const dateHeader = headers.find((h: any) => h.name === 'Date')?.value
            const receivedAt = dateHeader ? new Date(dateHeader) : new Date()
            const body = message.snippet || 'No content'

            console.log(`🆕 FORCE PROCESSING: "${subject}" from "${from}"`)

            // Create email in database
            const newEmail = await prisma.email.create({
              data: {
                userId: user.id,
                gmailMessageId: message.id,
                threadId: message.threadId || message.id,
                subject: subject,
                from: from,
                to: to,
                body: body.substring(0, 1000),
                receivedAt: receivedAt,
                isRead: false,
                isImportant: false,
                labels: message.labelIds || []
              }
            })

            if (newEmail) {
              emailsCreated++
              totalEmailsProcessed++
              console.log(`✅ FORCE CREATED email in database: ${subject}`)

              // Send immediate Telegram notification to ALL active Telegram users
              for (const telegramUser of user.telegramUsers) {
                try {
                  const notificationMessage = `🚨 NEW EMAIL DETECTED!\n\n` +
                    `📝 Subject: ${subject}\n` +
                    `👤 From: ${from}\n` +
                    `📅 Received: ${receivedAt.toLocaleString()}\n` +
                    `⚡ Status: UNREAD - Needs Response\n\n` +
                    `📄 Preview:\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}\n\n` +
                    `🔔 You'll get reminders every 5 minutes until you respond!`

                  const keyboard = {
                    inline_keyboard: [
                      [
                        { text: "💬 Reply to Email", callback_data: `reply_to_${newEmail.id}` },
                        { text: "✅ Mark as Read", callback_data: `mark_read_${newEmail.id}` }
                      ],
                      [
                        { text: "⭐ Star Email", callback_data: `star_email_${newEmail.id}` },
                        { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${newEmail.id}` }
                      ]
                    ]
                  }

                  const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      chat_id: telegramUser.telegramId,
                      text: notificationMessage,
                      reply_markup: keyboard
                    })
                  })

                  const result = await response.json()
                  if (result.ok) {
                    notificationsSent++
                    totalNotificationsSent++
                    console.log(`📱 ✅ FORCE NOTIFICATION sent for: ${subject} to ${telegramUser.telegramId}`)
                  } else {
                    console.error(`📱 ❌ Failed to send notification:`, result)
                  }

                } catch (notificationError) {
                  console.error(`📱 ❌ Error sending notification:`, notificationError)
                }

                // Add delay between notifications to prevent flooding
                await new Promise(resolve => setTimeout(resolve, 1000))
              }
            }

          } catch (emailError) {
            console.error(`❌ Error processing email ${message.id}:`, emailError)
          }
        }

        console.log(`✅ User ${user.email}: ${emailsCreated} emails created, ${notificationsSent} notifications sent`)

        // Add delay between users
        await new Promise(resolve => setTimeout(resolve, 2000))

      } catch (userError) {
        console.error(`❌ Error processing user ${user.email}:`, userError)
      }
    }

    console.log(`🎉 FORCE EMAIL CHECK FINISHED: ${totalEmailsProcessed} emails processed, ${totalNotificationsSent} notifications sent`)

    return successResponse({
      usersProcessed: users.length,
      totalEmailsProcessed,
      totalNotificationsSent,
      timestamp: new Date().toISOString()
    }, `Force email check processed ${totalEmailsProcessed} emails and sent ${totalNotificationsSent} notifications`)

  } catch (error) {
    console.error('❌ Force email check failed:', error)
    return serverErrorResponse(`Force email check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
