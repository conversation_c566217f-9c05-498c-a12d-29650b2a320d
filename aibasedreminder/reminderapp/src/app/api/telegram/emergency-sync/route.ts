import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET(request: NextRequest) {
  try {
    console.log('🚨 EMERGENCY EMAIL SYNC TRIGGERED')

    // Get the specific user
    const user = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    if (!user || !user.oauthTokens.length) {
      return serverErrorResponse('No user or tokens found')
    }

    console.log(`👤 Emergency sync for: ${user.email}`)
    console.log(`📱 Active Telegram users: ${user.telegramUsers.length}`)

    const { gmail } = await getGoogleServices(user.id)

    // Get ALL unread emails
    console.log('📧 Getting ALL unread emails...')
    const unreadMessages = await gmail.getMessages('is:unread', 50)
    console.log(`📨 Found ${unreadMessages.length} unread emails`)

    // Get existing emails from database
    const existingEmails = await prisma.email.findMany({
      where: { userId: user.id },
      select: { gmailMessageId: true, subject: true }
    })
    const existingIds = new Set(existingEmails.map(e => e.gmailMessageId))
    console.log(`💾 Found ${existingEmails.length} existing emails in database`)

    let newEmailsCreated = 0
    let notificationsSent = 0
    let emailDetails = []

    for (const message of unreadMessages) {
      try {
        const isNew = !existingIds.has(message.id)
        
        const headers = message.payload?.headers || []
        const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
        const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown'
        
        emailDetails.push({
          id: message.id,
          subject,
          from,
          isNew,
          snippet: message.snippet
        })

        if (!isNew) {
          console.log(`⏭️ Email already exists: ${subject}`)
          continue
        }

        console.log(`🆕 Processing NEW email: ${subject} from ${from}`)

        // Create email in database
        const dateHeader = headers.find((h: any) => h.name === 'Date')?.value
        const receivedAt = dateHeader ? new Date(dateHeader) : new Date()
        const to = headers.find((h: any) => h.name === 'To')?.value || ''
        const body = message.snippet || 'No content available'

        const newEmail = await prisma.email.create({
          data: {
            userId: user.id,
            gmailMessageId: message.id,
            threadId: message.threadId || message.id,
            subject,
            from,
            to,
            body: body.substring(0, 1000),
            receivedAt,
            isRead: false,
            isImportant: false,
            labels: JSON.stringify(message.labelIds || [])
          }
        })

        newEmailsCreated++
        console.log(`✅ Created email in database: ${newEmail.id}`)

        // Send Telegram notification
        for (const telegramUser of user.telegramUsers) {
          const notificationMessage = `🚨 EMERGENCY SYNC - NEW EMAIL!\n\n` +
            `📝 Subject: ${subject}\n` +
            `👤 From: ${from}\n` +
            `📅 Received: ${receivedAt.toLocaleString()}\n` +
            `⚡ Status: UNREAD\n\n` +
            `📄 Preview:\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}\n\n` +
            `✅ Email notifications are now working!`

          const keyboard = {
            inline_keyboard: [
              [
                { text: "💬 Reply", callback_data: `reply_to_${newEmail.id}` },
                { text: "✅ Mark Read", callback_data: `mark_read_${newEmail.id}` }
              ],
              [
                { text: "⭐ Star", callback_data: `star_email_${newEmail.id}` },
                { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${newEmail.id}` }
              ]
            ]
          }

          try {
            const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                chat_id: telegramUser.telegramId,
                text: notificationMessage,
                reply_markup: keyboard
              })
            })

            const result = await response.json()
            if (result.ok) {
              notificationsSent++
              console.log(`📱 ✅ Notification sent to ${telegramUser.telegramId}`)
            } else {
              console.error(`📱 ❌ Failed to send notification:`, result)
            }
          } catch (error) {
            console.error(`📱 ❌ Error sending notification:`, error)
          }
        }

      } catch (error) {
        console.error(`❌ Error processing email ${message.id}:`, error)
      }
    }

    console.log(`🎉 EMERGENCY SYNC COMPLETE: ${newEmailsCreated} new emails, ${notificationsSent} notifications`)

    return successResponse({
      totalUnreadEmails: unreadMessages.length,
      existingInDatabase: existingEmails.length,
      newEmailsCreated,
      notificationsSent,
      emailDetails: emailDetails.slice(0, 10), // Show first 10 for debugging
      timestamp: new Date().toISOString()
    }, `Emergency sync complete: ${newEmailsCreated} new emails processed`)

  } catch (error) {
    console.error('❌ Emergency sync failed:', error)
    return serverErrorResponse(`Emergency sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
