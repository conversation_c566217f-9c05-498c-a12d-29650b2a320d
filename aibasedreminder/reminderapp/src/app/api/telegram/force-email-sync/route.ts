import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 FORCE EMAIL SYNC TRIGGERED')

    // Get users with valid Google OAuth tokens and active Telegram connections
    const users = await prisma.user.findMany({
      where: {
        oauthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        },
        telegramUsers: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    console.log(`👥 Found ${users.length} users for force sync`)

    let totalNewEmails = 0
    let totalNotifications = 0

    for (const user of users) {
      try {
        console.log(`🔄 Force syncing emails for user: ${user.email}`)

        const { gmail } = await getGoogleServices(user.id)

        // Get ALL unread emails (force sync)
        console.log(`📧 Fetching ALL unread emails for ${user.email}...`)
        const unreadMessages = await gmail.getMessages('is:unread', 50)
        console.log(`📨 Found ${unreadMessages.length} unread emails`)

        let userNewEmails = 0
        let userNotifications = 0

        for (const gmailMessage of unreadMessages) {
          try {
            // Check if we already have this email in database
            const existingEmail = await prisma.email.findUnique({
              where: { gmailMessageId: gmailMessage.id }
            })

            if (existingEmail) {
              console.log(`⏭️ Email already exists: ${existingEmail.subject}`)
              continue
            }

            console.log(`🆕 PROCESSING NEW EMAIL: ${gmailMessage.id}`)

            // Extract email details
            const headers = gmailMessage.payload?.headers || []
            const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
            const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown Sender'
            const to = headers.find((h: any) => h.name === 'To')?.value || ''
            const dateHeader = headers.find((h: any) => h.name === 'Date')?.value
            const receivedAt = dateHeader ? new Date(dateHeader) : new Date()
            
            let body = gmailMessage.snippet || 'No content available'
            
            // Create new email record
            const newEmail = await prisma.email.create({
              data: {
                userId: user.id,
                gmailMessageId: gmailMessage.id,
                threadId: gmailMessage.threadId || gmailMessage.id,
                subject: subject,
                from: from,
                to: to,
                body: body.substring(0, 1000),
                receivedAt: receivedAt,
                isRead: false,
                isImportant: false,
                labels: JSON.stringify(gmailMessage.labelIds || [])
              }
            })

            userNewEmails++
            console.log(`✅ Created new email: ${subject} from ${from}`)
            
            // Send IMMEDIATE Telegram notification
            for (const telegramUser of user.telegramUsers) {
              const notificationMessage = `🚨 INSTANT EMAIL ALERT!\n\n` +
                `📝 Subject: ${subject}\n` +
                `👤 From: ${from}\n` +
                `📅 Received: ${receivedAt.toLocaleString()}\n` +
                `⚡ Status: UNREAD - Needs Response\n\n` +
                `📄 Preview:\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}\n\n` +
                `💬 Reply now or you'll get reminders every 5 minutes!`

              const keyboard = {
                inline_keyboard: [
                  [
                    { text: "💬 Reply to this Email", callback_data: `reply_to_${newEmail.id}` },
                    { text: "✅ Mark as Read", callback_data: `mark_read_${newEmail.id}` }
                  ],
                  [
                    { text: "⭐ Star Email", callback_data: `star_email_${newEmail.id}` },
                    { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${newEmail.id}` }
                  ]
                ]
              }
              
              console.log(`📱 Sending FORCE notification to Telegram user ${telegramUser.telegramId}`)

              try {
                const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    chat_id: telegramUser.telegramId,
                    text: notificationMessage,
                    reply_markup: keyboard,
                    parse_mode: 'HTML'
                  })
                })

                const result = await response.json()

                if (result.ok) {
                  userNotifications++
                  console.log(`📱 ✅ FORCE notification sent successfully to ${telegramUser.telegramId}`)
                } else {
                  console.error(`📱 ❌ Failed to send FORCE notification:`, result)
                }
              } catch (notificationError) {
                console.error(`📱 ❌ Network error sending FORCE notification:`, notificationError)
              }
            }
            
          } catch (emailError) {
            console.error(`❌ Error processing email ${gmailMessage.id}:`, emailError)
          }
        }

        totalNewEmails += userNewEmails
        totalNotifications += userNotifications

        console.log(`📊 User ${user.email}: ${userNewEmails} new emails, ${userNotifications} notifications sent`)

      } catch (userError) {
        console.error(`❌ Error force syncing user ${user.email}:`, userError)
      }
    }

    console.log(`📊 FORCE EMAIL SYNC COMPLETE: ${totalNewEmails} new emails, ${totalNotifications} notifications sent`)

    return successResponse({
      usersChecked: users.length,
      newEmails: totalNewEmails,
      notificationsSent: totalNotifications,
      timestamp: new Date().toISOString()
    }, 'Force email sync completed successfully')

  } catch (error) {
    console.error('❌ Force email sync failed:', error)
    return serverErrorResponse(`Force email sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Also allow POST
export const POST = GET
