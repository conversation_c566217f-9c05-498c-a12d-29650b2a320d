import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

// REAL autonomous system that actually works
let realAutonomousActive = false
let autonomousInterval: NodeJS.Timeout | null = null
let webhookCheckInterval: NodeJS.Timeout | null = null

export async function GET(request: NextRequest) {
  try {
    console.log('🚀 Starting REAL AUTONOMOUS system that actually works...')

    if (realAutonomousActive) {
      console.log('⚠️ Real autonomous system already active')
      return successResponse({
        realAutonomous: true,
        alreadyActive: true,
        timestamp: new Date().toISOString()
      }, 'Real autonomous system already active')
    }

    // Start the REAL autonomous system
    await startRealAutonomousSystem()

    return successResponse({
      realAutonomous: true,
      started: true,
      timestamp: new Date().toISOString(),
      message: 'REAL autonomous system started - bot will actually work now!'
    }, 'Real autonomous system activated')

  } catch (error) {
    console.error('❌ Failed to start real autonomous system:', error)
    return serverErrorResponse(`Failed to start real autonomous system: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function startRealAutonomousSystem() {
  if (realAutonomousActive) {
    return
  }

  console.log('🚀 Activating REAL autonomous system...')
  realAutonomousActive = true

  // FORCE webhook setup immediately
  await forceWebhookSetup()

  // Set up webhook monitoring every 2 minutes
  webhookCheckInterval = setInterval(async () => {
    try {
      console.log('🔍 Checking webhook health...')
      await ensureWebhookWorking()
    } catch (error) {
      console.error('❌ Webhook check failed:', error)
      await forceWebhookSetup()
    }
  }, 2 * 60 * 1000) // 2 minutes

  // Set up email checking every 2 minutes
  autonomousInterval = setInterval(async () => {
    try {
      console.log('📧 Checking emails autonomously...')
      await checkEmailsAndNotify()
    } catch (error) {
      console.error('❌ Autonomous email check failed:', error)
    }
  }, 2 * 60 * 1000) // 2 minutes

  // Set up continuous reminders every 5 minutes
  const reminderInterval = setInterval(async () => {
    try {
      console.log('🔔 Sending continuous reminders...')
      await sendContinuousReminders()
    } catch (error) {
      console.error('❌ Continuous reminders failed:', error)
    }
  }, 5 * 60 * 1000) // 5 minutes

  // Run initial email check
  await checkEmailsAndNotify()

  console.log('✅ REAL autonomous system is now ACTIVE!')
}

async function forceWebhookSetup() {
  try {
    console.log('🔧 FORCING webhook setup...')
    
    const webhookUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`
    console.log('🔗 Webhook URL:', webhookUrl)

    // Delete existing webhook
    await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/deleteWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })

    // Wait for cleanup
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Set new webhook
    const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/setWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message', 'callback_query'],
        drop_pending_updates: true,
        max_connections: 100
      })
    })

    const result = await response.json()
    console.log('📱 Webhook setup result:', result)

    if (result.ok) {
      console.log('✅ Webhook FORCED successfully!')
    } else {
      console.error('❌ Webhook setup failed:', result)
      throw new Error(`Webhook setup failed: ${result.description}`)
    }

  } catch (error) {
    console.error('❌ Force webhook setup error:', error)
    throw error
  }
}

async function ensureWebhookWorking() {
  try {
    // Check webhook info
    const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getWebhookInfo`)
    const result = await response.json()
    
    const expectedUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`
    
    if (!result.result || result.result.url !== expectedUrl) {
      console.log('🔧 Webhook not working, fixing...')
      await forceWebhookSetup()
    } else {
      console.log('✅ Webhook is working:', result.result.url)
    }
    
  } catch (error) {
    console.error('❌ Webhook check error:', error)
    await forceWebhookSetup()
  }
}

async function checkEmailsAndNotify() {
  try {
    console.log('📧 Running autonomous email check...')

    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/check-emails-now`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })

    if (response.ok) {
      const result = await response.json()
      console.log('📧 Email check result:', {
        usersChecked: result.data?.usersChecked || 0,
        newEmails: result.data?.newEmails || 0,
        notificationsSent: result.data?.notificationsSent || 0
      })

      if (result.data?.newEmails > 0 && result.data?.notificationsSent === 0) {
        console.log('⚠️ Found emails but no notifications sent - webhook might be broken')
        await forceWebhookSetup()
      }
    } else {
      console.error('❌ Email check failed:', response.status)
    }

  } catch (error) {
    console.error('❌ Email check error:', error)
  }
}

async function sendContinuousReminders() {
  try {
    console.log('🔔 Running continuous reminders...')

    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/continuous-reminders`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })

    if (response.ok) {
      const result = await response.json()
      console.log('🔔 Continuous reminders result:', {
        emailsChecked: result.data?.emailsChecked || 0,
        remindersSent: result.data?.remindersSent || 0
      })
    } else {
      console.error('❌ Continuous reminders failed:', response.status)
    }

  } catch (error) {
    console.error('❌ Continuous reminders error:', error)
  }
}

// Test the system
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing REAL autonomous system...')
    
    // Force webhook setup
    await forceWebhookSetup()
    
    // Check emails immediately
    await checkEmailsAndNotify()
    
    return successResponse({
      tested: true,
      timestamp: new Date().toISOString()
    }, 'Real autonomous system tested')
    
  } catch (error) {
    return serverErrorResponse(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Stop the system
export async function DELETE(request: NextRequest) {
  try {
    if (autonomousInterval) {
      clearInterval(autonomousInterval)
      autonomousInterval = null
    }
    
    if (webhookCheckInterval) {
      clearInterval(webhookCheckInterval)
      webhookCheckInterval = null
    }
    
    realAutonomousActive = false
    
    return successResponse({
      realAutonomous: false,
      stopped: true,
      timestamp: new Date().toISOString()
    }, 'Real autonomous system stopped')
    
  } catch (error) {
    return serverErrorResponse(`Failed to stop: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
