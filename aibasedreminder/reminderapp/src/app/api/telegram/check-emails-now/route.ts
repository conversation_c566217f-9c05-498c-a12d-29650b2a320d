import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 MANUAL EMAIL CHECK TRIGGERED')

    // Get all users with valid Google OAuth tokens and active Telegram connections
    const users = await prisma.user.findMany({
      where: {
        oauthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        },
        telegramUsers: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with valid tokens and Telegram connections`)

    let totalNewEmails = 0
    let totalNotifications = 0

    for (const user of users) {
      try {
        console.log(`📧 Checking emails for user: ${user.email}`)

        const { gmail } = await getGoogleServices(user.id)

        // Get ALL unread emails (more reliable than time-based queries)
        console.log(`📧 Fetching all unread emails for ${user.email}...`)
        const unreadMessages = await gmail.getMessages('is:unread', 20)
        
        console.log(`📨 Found ${unreadMessages.length} unread emails for ${user.email}`)

        let userNewEmails = 0
        let userNotifications = 0

        // Process unread emails (check for new ones) - PREVENT DUPLICATES
        const processedEmails = new Set()

        for (const gmailMessage of unreadMessages) {
          try {
            // Skip if already processed in this batch
            if (processedEmails.has(gmailMessage.id)) {
              console.log(`⏭️ Skipping duplicate email in batch: ${gmailMessage.id}`)
              continue
            }

            // Check if we already have this email in database
            const existingEmail = await prisma.email.findUnique({
              where: { gmailMessageId: gmailMessage.id }
            })

            if (existingEmail) {
              console.log(`⏭️ Email already exists in database: ${gmailMessage.id} - Subject: ${existingEmail.subject}`)
              continue // Skip if already processed
            }

            console.log(`🆕 NEW EMAIL DETECTED: ${gmailMessage.id}`)

            // Mark as processed in this batch
            processedEmails.add(gmailMessage.id)

            // Extract basic info from the message
            const headers = gmailMessage.payload?.headers || []
            const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
            const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown Sender'
            const to = headers.find((h: any) => h.name === 'To')?.value || ''
            const dateHeader = headers.find((h: any) => h.name === 'Date')?.value
            const receivedAt = dateHeader ? new Date(dateHeader) : new Date()
            
            // Extract body text (simplified)
            let body = gmailMessage.snippet || 'No content available'
            
            // Create new email record (compatible with current schema)
            const newEmail = await prisma.email.create({
              data: {
                userId: user.id,
                gmailMessageId: gmailMessage.id,
                threadId: gmailMessage.threadId || gmailMessage.id,
                subject: subject,
                from: from,
                to: to,
                body: body.substring(0, 1000), // Limit body length
                receivedAt: receivedAt,
                isRead: false,
                isImportant: false,
                labels: JSON.stringify(gmailMessage.labelIds || [])
              }
            })

            userNewEmails++
            console.log(`✅ Created new email: ${subject} from ${from}`)
            
            // Send immediate Telegram notification with reply buttons - ONE BY ONE WITH DELAYS
            for (let i = 0; i < user.telegramUsers.length; i++) {
              const telegramUser = user.telegramUsers[i]

              // Add delay between notifications to prevent flooding
              if (i > 0) {
                await new Promise(resolve => setTimeout(resolve, 1000)) // 1 second delay
              }
              const notificationMessage = `🚨 INSTANT EMAIL ALERT!\n\n` +
                `📝 Subject: ${subject}\n` +
                `👤 From: ${from}\n` +
                `📅 Received: ${receivedAt.toLocaleString()}\n` +
                `⚡ Status: UNREAD - Needs Response\n\n` +
                `📄 Preview:\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}\n\n` +
                `💬 Reply now or you'll get reminders every 5 minutes!`

              // Create reply button
              const keyboard = {
                inline_keyboard: [
                  [
                    { text: "💬 Reply to this Email", callback_data: `reply_to_${newEmail.id}` },
                    { text: "✅ Mark as Read", callback_data: `mark_read_${newEmail.id}` }
                  ],
                  [
                    { text: "⭐ Star Email", callback_data: `star_email_${newEmail.id}` },
                    { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${newEmail.id}` }
                  ]
                ]
              }
              
              // Use direct Telegram API call with ENHANCED error handling
              console.log(`📱 Attempting to send notification to Telegram user ${telegramUser.telegramId}`)

              try {
                const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    chat_id: telegramUser.telegramId,
                    text: notificationMessage,
                    reply_markup: keyboard,
                    parse_mode: 'HTML'
                  })
                })

                const result = await response.json()
                console.log(`📱 Telegram API response:`, result)

                if (result.ok) {
                  userNotifications++
                  console.log(`📱 ✅ Successfully sent notification to Telegram user ${telegramUser.telegramId}`)
                } else {
                  console.error(`📱 ❌ Failed to send notification to ${telegramUser.telegramId}:`, result)

                  // Try sending a simple message without keyboard as fallback
                  const fallbackResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      chat_id: telegramUser.telegramId,
                      text: `🚨 NEW EMAIL: ${subject}\nFrom: ${from}\n\nBot is working! Use /reply to respond.`
                    })
                  })

                  const fallbackResult = await fallbackResponse.json()
                  if (fallbackResult.ok) {
                    userNotifications++
                    console.log(`📱 ✅ Sent fallback notification to ${telegramUser.telegramId}`)
                  } else {
                    console.error(`📱 ❌ Fallback notification also failed:`, fallbackResult)
                  }
                }
              } catch (notificationError) {
                console.error(`📱 ❌ Network error sending notification to ${telegramUser.telegramId}:`, notificationError)
              }
            }
            
          } catch (emailError) {
            console.error(`❌ Error processing email ${gmailMessage.id}:`, emailError)
          }
        }

        totalNewEmails += userNewEmails
        totalNotifications += userNotifications

        console.log(`📊 User ${user.email}: ${userNewEmails} new emails, ${userNotifications} notifications sent`)

      } catch (userError) {
        console.error(`❌ Error processing user ${user.email}:`, userError)
      }
    }

    console.log(`📊 MANUAL EMAIL CHECK COMPLETE: ${totalNewEmails} new emails, ${totalNotifications} notifications sent`)

    return successResponse({
      usersChecked: users.length,
      newEmails: totalNewEmails,
      notificationsSent: totalNotifications,
      timestamp: new Date().toISOString()
    }, 'Manual email check completed successfully')

  } catch (error) {
    console.error('❌ Manual email check failed:', error)
    return serverErrorResponse(`Manual email check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Also allow POST
export const POST = GET
