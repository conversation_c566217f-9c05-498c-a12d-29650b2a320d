import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

export async function GET() {
  try {
    console.log('📧 SENDING REAL TEST EMAIL IMMEDIATELY')

    // Send test email to both connected accounts
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>'
    ]

    const timestamp = new Date().toLocaleString()
    const testId = Math.random().toString(36).substring(7)

    const results = []

    for (const targetEmail of testEmails) {
      try {
        console.log(`📧 Sending test email to: ${targetEmail}`)

        // Use Gmail API to send email (more reliable than SMTP)
        const emailContent = `From: AI Reminder System <<EMAIL>>
To: ${targetEmail}
Subject: 🚨 URGENT: Telegram Bot Test Email - ${testId}
Content-Type: text/html; charset=utf-8

<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 2px solid #ff6b6b; border-radius: 10px; background-color: #fff5f5;">
  <h2 style="color: #ff6b6b; text-align: center;">🚨 URGENT TEST EMAIL 🚨</h2>
  
  <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff6b6b;">
    <h3 style="color: #333; margin-top: 0;">📱 Telegram Bot Test</h3>
    <p style="color: #666; font-size: 16px; line-height: 1.6;">
      This is a <strong>REAL TEST EMAIL</strong> sent to verify your Telegram bot email notification system.
    </p>
    
    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
      <p style="margin: 5px 0;"><strong>📧 Target Email:</strong> ${targetEmail}</p>
      <p style="margin: 5px 0;"><strong>⏰ Sent At:</strong> ${timestamp}</p>
      <p style="margin: 5px 0;"><strong>🔍 Test ID:</strong> ${testId}</p>
    </div>
    
    <h4 style="color: #ff6b6b;">🎯 Expected Behavior:</h4>
    <ul style="color: #666; line-height: 1.8;">
      <li>✅ You should receive an <strong>INSTANT Telegram notification</strong></li>
      <li>🔔 Continuous reminders every <strong>5 minutes</strong> until you respond</li>
      <li>💬 Interactive buttons to Reply, Mark as Read, Star, or Stop Reminders</li>
      <li>📱 Ability to respond directly from Telegram</li>
    </ul>
    
    <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
      <h4 style="color: #28a745; margin-top: 0;">✅ If you receive this in Telegram:</h4>
      <p style="color: #666; margin-bottom: 0;">Your email notification system is working perfectly! 🎉</p>
    </div>
    
    <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
      <h4 style="color: #856404; margin-top: 0;">⚠️ If you DON'T receive this in Telegram:</h4>
      <p style="color: #666; margin-bottom: 0;">There may be an issue with the email processing system that needs to be fixed.</p>
    </div>
  </div>
  
  <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
    <p style="color: #999; font-size: 14px;">
      This email was sent by your AI Reminder System for testing purposes.<br>
      Test ID: ${testId} | Timestamp: ${timestamp}
    </p>
  </div>
</div>`

        // Encode email content in base64
        const encodedEmail = Buffer.from(emailContent).toString('base64')
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=+$/, '')

        // Send via Gmail API using your SMTP credentials
        const gmailResponse = await fetch('https://gmail.googleapis.com/gmail/v1/users/me/messages/send', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.GMAIL_ACCESS_TOKEN}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            raw: encodedEmail
          })
        })

        if (gmailResponse.ok) {
          const gmailResult = await gmailResponse.json()
          results.push({
            email: targetEmail,
            success: true,
            messageId: gmailResult.id,
            testId
          })
          console.log(`✅ Test email sent successfully to ${targetEmail}`)
        } else {
          // Fallback: Use webhook to trigger email sending
          console.log(`⚠️ Gmail API failed, using fallback method for ${targetEmail}`)
          
          // Trigger email check after a short delay
          setTimeout(async () => {
            try {
              await fetch('https://aibasedreminder.vercel.app/api/telegram/check-emails-now')
              console.log(`🔄 Triggered email check for ${targetEmail}`)
            } catch (checkError) {
              console.error('❌ Failed to trigger email check:', checkError)
            }
          }, 5000)

          results.push({
            email: targetEmail,
            success: false,
            error: 'Gmail API not available, using fallback method',
            testId
          })
        }

        // Wait between emails
        await new Promise(resolve => setTimeout(resolve, 2000))

      } catch (emailError) {
        console.error(`❌ Failed to send test email to ${targetEmail}:`, emailError)
        results.push({
          email: targetEmail,
          success: false,
          error: emailError instanceof Error ? emailError.message : 'Unknown error',
          testId
        })
      }
    }

    // Trigger email check immediately after sending
    setTimeout(async () => {
      try {
        console.log('🔄 Triggering immediate email check...')
        await fetch('https://aibasedreminder.vercel.app/api/telegram/check-emails-now')
        
        // Trigger again after 30 seconds
        setTimeout(async () => {
          await fetch('https://aibasedreminder.vercel.app/api/telegram/check-emails-now')
        }, 30000)
        
      } catch (checkError) {
        console.error('❌ Failed to trigger email check:', checkError)
      }
    }, 3000)

    return successResponse({
      emailsSent: results.filter(r => r.success).length,
      totalEmails: testEmails.length,
      results,
      testId,
      timestamp,
      nextSteps: [
        "1. Check your Telegram bot for notifications within 30 seconds",
        "2. If no notification, the system will auto-check emails",
        "3. Look for continuous reminders every 5 minutes",
        "4. Test the interactive reply buttons"
      ]
    }, `Real test emails initiated for ${testEmails.length} accounts`)

  } catch (error) {
    console.error('❌ Failed to send real test emails:', error)
    return serverErrorResponse(`Failed to send real test emails: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
