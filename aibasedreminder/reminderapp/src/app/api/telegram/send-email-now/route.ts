import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

export async function GET() {
  try {
    console.log('📧 SENDING REAL TEST EMAIL USING EXTERNAL SERVICE')

    const timestamp = new Date().toLocaleString()
    const testId = Math.random().toString(36).substring(7)

    // Test emails to send to both connected accounts
    const targetEmails = [
      '<EMAIL>',
      '<EMAIL>'
    ]

    const results = []

    for (const targetEmail of targetEmails) {
      try {
        console.log(`📧 Sending test email to: ${targetEmail}`)

        // Use a simple email service (EmailJS or similar)
        const emailData = {
          to: targetEmail,
          subject: `🚨 URGENT: Telegram Bot Test - ${testId}`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 2px solid #ff6b6b; border-radius: 10px; background-color: #fff5f5;">
              <h2 style="color: #ff6b6b; text-align: center;">🚨 URGENT TEST EMAIL 🚨</h2>
              
              <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff6b6b;">
                <h3 style="color: #333; margin-top: 0;">📱 Telegram Bot Test</h3>
                <p style="color: #666; font-size: 16px; line-height: 1.6;">
                  This is a <strong>REAL TEST EMAIL</strong> sent to verify your Telegram bot email notification system.
                </p>
                
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                  <p style="margin: 5px 0;"><strong>📧 Target Email:</strong> ${targetEmail}</p>
                  <p style="margin: 5px 0;"><strong>⏰ Sent At:</strong> ${timestamp}</p>
                  <p style="margin: 5px 0;"><strong>🔍 Test ID:</strong> ${testId}</p>
                </div>
                
                <h4 style="color: #ff6b6b;">🎯 Expected Behavior:</h4>
                <ul style="color: #666; line-height: 1.8;">
                  <li>✅ You should receive an <strong>INSTANT Telegram notification</strong></li>
                  <li>🔔 Continuous reminders every <strong>5 minutes</strong> until you respond</li>
                  <li>💬 Interactive buttons to Reply, Mark as Read, Star, or Stop Reminders</li>
                  <li>📱 Ability to respond directly from Telegram</li>
                </ul>
                
                <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
                  <h4 style="color: #28a745; margin-top: 0;">✅ If you receive this in Telegram:</h4>
                  <p style="color: #666; margin-bottom: 0;">Your email notification system is working perfectly! 🎉</p>
                </div>
                
                <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
                  <h4 style="color: #856404; margin-top: 0;">⚠️ If you DON'T receive this in Telegram:</h4>
                  <p style="color: #666; margin-bottom: 0;">There may be an issue with the email processing system that needs to be fixed.</p>
                </div>
              </div>
              
              <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                <p style="color: #999; font-size: 14px;">
                  This email was sent by your AI Reminder System for testing purposes.<br>
                  Test ID: ${testId} | Timestamp: ${timestamp}
                </p>
              </div>
            </div>
          `
        }

        // Try multiple email services
        let emailSent = false

        // Method 1: Try using a webhook service
        try {
          const webhookResponse = await fetch('https://api.emailjs.com/api/v1.0/email/send', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              service_id: 'default_service',
              template_id: 'template_test',
              user_id: 'public_key',
              template_params: {
                to_email: targetEmail,
                subject: emailData.subject,
                message: emailData.html
              }
            })
          })

          if (webhookResponse.ok) {
            emailSent = true
            console.log(`✅ Email sent via webhook to ${targetEmail}`)
          }
        } catch (webhookError) {
          console.log(`⚠️ Webhook method failed for ${targetEmail}`)
        }

        // Method 2: Try using Formspree or similar
        if (!emailSent) {
          try {
            const formResponse = await fetch('https://formspree.io/f/test', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                email: targetEmail,
                subject: emailData.subject,
                message: `Test email for Telegram bot - ${testId}`
              })
            })

            if (formResponse.ok) {
              emailSent = true
              console.log(`✅ Email sent via form service to ${targetEmail}`)
            }
          } catch (formError) {
            console.log(`⚠️ Form service failed for ${targetEmail}`)
          }
        }

        results.push({
          email: targetEmail,
          success: emailSent,
          testId,
          method: emailSent ? 'external_service' : 'failed'
        })

        // Wait between emails
        await new Promise(resolve => setTimeout(resolve, 2000))

      } catch (emailError) {
        console.error(`❌ Failed to send test email to ${targetEmail}:`, emailError)
        results.push({
          email: targetEmail,
          success: false,
          error: emailError instanceof Error ? emailError.message : 'Unknown error',
          testId
        })
      }
    }

    // Trigger email checks after sending
    setTimeout(async () => {
      try {
        console.log('🔄 Triggering email checks after sending...')
        
        // Check multiple times
        for (let i = 0; i < 3; i++) {
          await new Promise(resolve => setTimeout(resolve, 10000)) // Wait 10 seconds
          await fetch('https://aibasedreminder.vercel.app/api/telegram/check-emails-now')
          console.log(`🔄 Email check ${i + 1} triggered`)
        }
        
      } catch (checkError) {
        console.error('❌ Failed to trigger email checks:', checkError)
      }
    }, 5000)

    return successResponse({
      emailsSent: results.filter(r => r.success).length,
      totalEmails: targetEmails.length,
      results,
      testId,
      timestamp,
      instructions: [
        "📧 Test emails have been sent to your connected accounts",
        "⏰ Check your Telegram bot within 30-60 seconds",
        "🔄 The system will automatically check for new emails",
        "📱 Look for notifications with interactive buttons",
        "🔔 Expect continuous reminders every 5 minutes"
      ]
    }, `Test email process initiated for ${targetEmails.length} accounts`)

  } catch (error) {
    console.error('❌ Failed to send test emails:', error)
    return serverErrorResponse(`Failed to send test emails: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
