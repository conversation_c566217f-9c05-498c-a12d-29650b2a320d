import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Continuous reminder system - sends reminders every 5 minutes for unread emails
export async function GET() {
  try {
    console.log('🔄 Running continuous reminder system...')

    // Get all unread emails that need reminders using raw SQL (bypasses schema issues)
    const unreadEmails = await prisma.$queryRaw`
      SELECT
        e.id, e."userId", e."gmailMessageId", e.subject, e."from", e.body, e."receivedAt", e."createdAt",
        u.email as user_email,
        tu."telegramId", tu."isActive" as telegram_active
      FROM emails e
      JOIN users u ON e."userId" = u.id
      JOIN "TelegramUser" tu ON u.id = tu."userId"
      WHERE e."isRead" = false
        AND e."createdAt" < NOW() - INTERVAL '5 minutes'
        AND tu."isActive" = true
      ORDER BY e."receivedAt" DESC
    ` as any[]

    console.log(`📧 Found ${unreadEmails.length} unread emails needing reminders`)

    let remindersSent = 0

    for (const email of unreadEmails) {
      try {
        // Check when last reminder was sent (use createdAt as reference)
        const lastReminderTime = new Date(email.createdAt)
        const timeSinceLastReminder = Date.now() - lastReminderTime.getTime()

        // Only send reminder if 5 minutes have passed since last reminder
        if (timeSinceLastReminder < 5 * 60 * 1000) {
          console.log(`⏰ Skipping email ${email.id} - last reminder too recent`)
          continue
        }

        // Send reminder to active Telegram user (from raw SQL result)
        if (email.telegram_active) {
          const reminderMessage = `🔔 EMAIL REMINDER!\n\n` +
            `📝 Subject: ${email.subject}\n` +
            `👤 From: ${email.from}\n` +
            `📅 Received: ${new Date(email.receivedAt).toLocaleString()}\n` +
            `⏰ This email needs your attention!\n\n` +
            `📄 Preview:\n${email.body.substring(0, 200)}${email.body.length > 200 ? '...' : ''}\n\n` +
            `💬 Please respond or mark as read to stop reminders!`

          // Create action buttons
          const keyboard = {
            inline_keyboard: [
              [
                { text: "💬 Reply to this Email", callback_data: `reply_to_${email.id}` },
                { text: "✅ Mark as Read", callback_data: `mark_read_${email.id}` }
              ],
              [
                { text: "⭐ Star Email", callback_data: `star_email_${email.id}` },
                { text: "🔕 Stop Reminders", callback_data: `stop_reminders_${email.id}` }
              ]
            ]
          }

          console.log(`🔔 Sending reminder to Telegram user ${email.telegramId}`)

          try {
            const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                chat_id: telegramUser.telegramId,
                text: reminderMessage,
                reply_markup: keyboard,
                parse_mode: 'HTML'
              })
            })

            const result = await response.json()
            
            if (result.ok) {
              remindersSent++
              console.log(`🔔 ✅ Reminder sent successfully to ${telegramUser.telegramId}`)
            } else {
              console.error(`🔔 ❌ Failed to send reminder:`, result)
              
              // Try fallback simple message
              const fallbackResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  chat_id: telegramUser.telegramId,
                  text: `🔔 REMINDER: Unread email from ${email.fromEmail}\nSubject: ${email.subject}\n\nUse /emails to view and respond.`
                })
              })
              
              const fallbackResult = await fallbackResponse.json()
              if (fallbackResult.ok) {
                remindersSent++
                console.log(`🔔 ✅ Fallback reminder sent to ${telegramUser.telegramId}`)
              }
            }
          } catch (notificationError) {
            console.error(`🔔 ❌ Network error sending reminder:`, notificationError)
          }
        }

        // Update email with reminder info (use updatedAt field)
        await prisma.email.update({
          where: { id: email.id },
          data: {
            updatedAt: new Date() // Use updatedAt to track last reminder
          }
        })

        console.log(`🔔 Updated reminder count for email ${email.id}`)

      } catch (emailError) {
        console.error(`❌ Error processing email ${email.id}:`, emailError)
      }
    }

    return successResponse({
      emailsChecked: unreadEmails.length,
      remindersSent,
      timestamp: new Date().toISOString()
    }, `Continuous reminders completed - sent ${remindersSent} reminders`)

  } catch (error) {
    console.error('❌ Continuous reminder system error:', error)
    return serverErrorResponse(`Continuous reminder system failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Test endpoint
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing continuous reminder system...')
    
    // Force send reminders for all unread emails
    const unreadEmails = await prisma.email.findMany({
      where: {
        isRead: false
      },
      include: {
        user: {
          include: {
            telegramUsers: true
          }
        }
      }
    })

    console.log(`🧪 Found ${unreadEmails.length} unread emails for testing`)

    let testRemindersSent = 0

    for (const email of unreadEmails) {
      for (const telegramUser of email.user.telegramUsers) {
        const testMessage = `🧪 TEST REMINDER!\n\n` +
          `📝 Subject: ${email.subject}\n` +
          `👤 From: ${email.fromEmail}\n` +
          `📅 Received: ${email.receivedAt.toLocaleString()}\n\n` +
          `This is a test reminder - system is working!`

        try {
          const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              chat_id: telegramUser.telegramId,
              text: testMessage
            })
          })

          const result = await response.json()
          if (result.ok) {
            testRemindersSent++
          }
        } catch (error) {
          console.error('Test reminder error:', error)
        }
      }
    }

    return successResponse({
      testRemindersSent,
      timestamp: new Date().toISOString()
    }, `Test reminders sent: ${testRemindersSent}`)

  } catch (error) {
    return serverErrorResponse(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
