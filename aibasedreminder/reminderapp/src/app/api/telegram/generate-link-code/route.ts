import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireAuth } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { prisma } from '@/lib/prisma'
import { randomBytes } from 'crypto'

export const POST = requireAuth(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    // Generate a unique 8-character link code
    const linkCode = randomBytes(4).toString('hex').toUpperCase()
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

    // Store the link code in database
    await prisma.telegramLinkCode.upsert({
      where: { userId: user.id },
      update: {
        code: linkCode,
        expiresAt,
        isUsed: false,
        updatedAt: new Date()
      },
      create: {
        userId: user.id,
        code: linkCode,
        expiresAt,
        isUsed: false
      }
    })

    return successResponse({
      linkCode,
      expiresAt: expiresAt.toISOString(),
      instructions: [
        '1. Open Telegram and find @ReminderAPPBot',
        '2. Send /start to the bot',
        `3. Send /link ${linkCode}`,
        '4. Your account will be linked automatically!'
      ]
    }, 'Link code generated successfully')

  } catch (error) {
    console.error('Generate Telegram link code error:', error)
    return serverErrorResponse('Failed to generate link code')
  }
})

export const GET = requireAuth(async (request: NextRequest, user) => {
  try {
    const linkCode = await prisma.telegramLinkCode.findFirst({
      where: {
        userId: user.id,
        expiresAt: { gt: new Date() },
        isUsed: false
      }
    })

    if (!linkCode) {
      return errorResponse('No active link code found', 404)
    }

    return successResponse({
      linkCode: linkCode.code,
      expiresAt: linkCode.expiresAt.toISOString(),
      timeRemaining: Math.max(0, linkCode.expiresAt.getTime() - Date.now())
    }, 'Active link code retrieved')

  } catch (error) {
    console.error('Get Telegram link code error:', error)
    return serverErrorResponse('Failed to get link code')
  }
})
