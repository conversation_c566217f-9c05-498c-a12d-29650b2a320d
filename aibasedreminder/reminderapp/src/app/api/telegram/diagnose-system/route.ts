import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'

export async function GET() {
  try {
    console.log('🔍 COMPREHENSIVE SYSTEM DIAGNOSIS')

    // Get all users with their connections
    const users = await prisma.user.findMany({
      include: {
        telegramUsers: true,
        oauthTokens: true,
        emails: {
          orderBy: { createdAt: 'desc' },
          take: 5
        }
      }
    })

    console.log(`👥 Found ${users.length} total users`)

    const diagnosis = {
      totalUsers: users.length,
      connectedUsers: [],
      systemStatus: {
        telegramConnections: 0,
        googleTokens: 0,
        activeUsers: 0,
        totalEmails: 0
      },
      emailChecks: []
    }

    for (const user of users) {
      const userInfo = {
        email: user.email,
        name: user.name,
        telegramConnected: user.telegramUsers.length > 0,
        telegramActive: user.telegramUsers.some(tu => tu.isActive),
        telegramIds: user.telegramUsers.map(tu => tu.telegramId),
        googleTokenValid: false,
        emailCount: user.emails.length,
        recentEmails: user.emails.map(e => ({
          subject: e.subject,
          from: e.from,
          isRead: e.isRead,
          createdAt: e.createdAt
        })),
        gmailCheck: null
      }

      // Check Google token validity
      const googleToken = user.oauthTokens.find(t => t.provider === 'google')
      if (googleToken && googleToken.expiresAt > new Date()) {
        userInfo.googleTokenValid = true
        diagnosis.systemStatus.googleTokens++

        // Try to check Gmail
        try {
          const { gmail } = await getGoogleServices(user.id)
          const unreadMessages = await gmail.getMessages('is:unread', 5)
          
          userInfo.gmailCheck = {
            success: true,
            unreadCount: unreadMessages.length,
            messages: unreadMessages.map(m => ({
              id: m.id,
              subject: m.payload?.headers?.find((h: any) => h.name === 'Subject')?.value || 'No Subject',
              from: m.payload?.headers?.find((h: any) => h.name === 'From')?.value || 'Unknown'
            }))
          }

          diagnosis.emailChecks.push({
            userEmail: user.email,
            unreadCount: unreadMessages.length,
            messages: userInfo.gmailCheck.messages
          })

        } catch (gmailError) {
          userInfo.gmailCheck = {
            success: false,
            error: gmailError instanceof Error ? gmailError.message : 'Unknown error'
          }
        }
      }

      if (user.telegramUsers.length > 0) {
        diagnosis.systemStatus.telegramConnections++
      }

      if (userInfo.telegramActive && userInfo.googleTokenValid) {
        diagnosis.systemStatus.activeUsers++
      }

      diagnosis.systemStatus.totalEmails += user.emails.length
      diagnosis.connectedUsers.push(userInfo)
    }

    // Test Telegram bot
    let telegramBotStatus = null
    try {
      const botResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getMe`)
      const botResult = await botResponse.json()
      telegramBotStatus = {
        working: botResult.ok,
        botInfo: botResult.result || null,
        error: botResult.ok ? null : botResult.description
      }
    } catch (telegramError) {
      telegramBotStatus = {
        working: false,
        error: telegramError instanceof Error ? telegramError.message : 'Unknown error'
      }
    }

    console.log('🔍 DIAGNOSIS COMPLETE')
    console.log(`📊 Active Users: ${diagnosis.systemStatus.activeUsers}`)
    console.log(`📧 Total Emails: ${diagnosis.systemStatus.totalEmails}`)
    console.log(`🤖 Telegram Bot Working: ${telegramBotStatus?.working}`)

    return successResponse({
      diagnosis,
      telegramBotStatus,
      timestamp: new Date().toISOString(),
      recommendations: generateRecommendations(diagnosis, telegramBotStatus)
    }, `System diagnosis complete - ${diagnosis.systemStatus.activeUsers} active users found`)

  } catch (error) {
    console.error('❌ System diagnosis failed:', error)
    return serverErrorResponse(`System diagnosis failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

function generateRecommendations(diagnosis: any, telegramStatus: any) {
  const recommendations = []

  if (diagnosis.systemStatus.activeUsers === 0) {
    recommendations.push("❌ No active users found - check Telegram connections and Google tokens")
  }

  if (!telegramStatus?.working) {
    recommendations.push("❌ Telegram bot not working - check TELEGRAM_BOT_TOKEN")
  }

  if (diagnosis.systemStatus.totalEmails === 0) {
    recommendations.push("⚠️ No emails in database - system may not be processing emails")
  }

  for (const user of diagnosis.connectedUsers) {
    if (user.telegramConnected && !user.telegramActive) {
      recommendations.push(`⚠️ User ${user.email} has inactive Telegram connection`)
    }
    
    if (!user.googleTokenValid) {
      recommendations.push(`⚠️ User ${user.email} needs to refresh Google OAuth token`)
    }

    if (user.gmailCheck && !user.gmailCheck.success) {
      recommendations.push(`❌ Gmail access failed for ${user.email}: ${user.gmailCheck.error}`)
    }
  }

  if (recommendations.length === 0) {
    recommendations.push("✅ System appears to be working correctly")
  }

  return recommendations
}

export const POST = GET
