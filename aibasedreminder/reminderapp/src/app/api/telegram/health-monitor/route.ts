import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'

// HEALTH MONITOR - Ensures bot stays active and responsive
let healthMonitorActive = false
let healthCheckInterval: NodeJS.Timeout | null = null

// Auto-start health monitor
setTimeout(async () => {
  if (!healthMonitorActive) {
    console.log('🏥 Starting health monitor...')
    await startHealthMonitor()
  }
}, 10000) // 10 seconds after startup

export async function GET(request: NextRequest) {
  try {
    console.log('🏥 Health monitor check...')

    if (!healthMonitorActive) {
      await startHealthMonitor()
    }

    // Check bot status
    const botStatus = await checkBotHealth()
    
    return successResponse({
      healthMonitor: true,
      botStatus,
      timestamp: new Date().toISOString()
    }, 'Health monitor active')

  } catch (error) {
    console.error('❌ Health monitor error:', error)
    return serverErrorResponse(`Health monitor failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function startHealthMonitor() {
  if (healthMonitorActive) {
    return
  }

  console.log('🏥 Starting health monitor system...')
  healthMonitorActive = true

  // Check bot health every 3 minutes
  healthCheckInterval = setInterval(async () => {
    try {
      console.log('🏥 Running health check...')
      const isHealthy = await checkBotHealth()
      
      if (!isHealthy) {
        console.log('🚨 Bot unhealthy - reactivating...')
        await reactivateBot()
      }
    } catch (error) {
      console.error('❌ Health check failed:', error)
      await reactivateBot()
    }
  }, 3 * 60 * 1000) // 3 minutes

  console.log('✅ Health monitor started!')
}

async function checkBotHealth() {
  try {
    // Check webhook status
    const webhookResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getWebhookInfo`)
    const webhookResult = await webhookResponse.json()
    
    const expectedUrl = `${process.env.NEXTAUTH_URL}/api/telegram/webhook`
    const webhookHealthy = webhookResult.result && webhookResult.result.url === expectedUrl
    
    // Check bot info
    const botResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getMe`)
    const botResult = await botResponse.json()
    const botHealthy = botResult.ok
    
    console.log('🏥 Health check results:', {
      webhook: webhookHealthy,
      bot: botHealthy,
      webhookUrl: webhookResult.result?.url
    })
    
    return webhookHealthy && botHealthy
    
  } catch (error) {
    console.error('❌ Health check error:', error)
    return false
  }
}

async function reactivateBot() {
  try {
    console.log('🔄 Reactivating bot...')
    
    // Call auto-activate endpoint
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/telegram/auto-activate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })
    
    if (response.ok) {
      console.log('✅ Bot reactivated successfully')
    } else {
      console.error('❌ Failed to reactivate bot')
    }
    
  } catch (error) {
    console.error('❌ Reactivation error:', error)
  }
}

// Manual reactivation endpoint
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Manual bot reactivation...')
    
    await reactivateBot()
    
    return successResponse({
      reactivated: true,
      timestamp: new Date().toISOString()
    }, 'Bot manually reactivated')
    
  } catch (error) {
    return serverErrorResponse(`Manual reactivation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
