import { NextRequest, NextResponse } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireAuth } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { prisma } from '@/lib/prisma'
import { sendMessage, handleTelegramUpdate } from '@/lib/telegram'

// Handle webhook requests (no auth required)
export async function POST(request: NextRequest) {
  try {
    // Check if this is a webhook request (has update_id)
    const body = await request.json()

    if (body.update_id && body.message) {
      // This is a Telegram webhook update
      console.log('📨 Telegram webhook received via link route')
      console.log('📋 Update data:', JSON.stringify(body, null, 2))

      await handleTelegramUpdate(body)

      return NextResponse.json({ ok: true, processed: true })
    }

    // This is a regular link request, require auth
    return await handleLinkRequest(request)

  } catch (error) {
    console.error('❌ POST request error:', error)
    return NextResponse.json(
      { error: 'Request processing failed' },
      { status: 500 }
    )
  }
}

// Original authenticated link handler
const handleLinkRequest = requireAuth(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const body = await request.json()
    const { telegramId, username, firstName, lastName } = body

    if (!telegramId || !firstName) {
      return errorResponse('Telegram ID and first name are required')
    }

    const existingLink = await prisma.telegramUser.findFirst({
      where: { telegramId, isActive: true }
    })

    if (existingLink && existingLink.userId !== user.id) {
      return errorResponse('This Telegram account is already linked to another user', 409)
    }

    const telegramUser = await prisma.telegramUser.upsert({
      where: { telegramId },
      update: {
        userId: user.id,
        username,
        firstName,
        lastName,
        isActive: true,
        updatedAt: new Date()
      },
      create: {
        userId: user.id,
        telegramId,
        username,
        firstName,
        lastName,
        isActive: true
      }
    })

    try {
      await sendMessage(
        telegramId,
        `🎉 Successfully linked to your AI Reminder account!\n\n` +
        `Account: ${user.email}\n` +
        `You'll now receive smart reminders and notifications here.\n\n` +
        `Use /help to see available commands.`
      )
    } catch (telegramError) {
      console.warn('Failed to send confirmation message:', telegramError)
    }

    return successResponse({
      linked: true,
      telegramUser: {
        id: telegramUser.id,
        telegramId: telegramUser.telegramId,
        username: telegramUser.username,
        firstName: telegramUser.firstName,
        lastName: telegramUser.lastName,
        linkedAt: telegramUser.createdAt
      }
    }, 'Telegram account linked successfully')

  } catch (error) {
    console.error('Link Telegram account error:', error)
    return serverErrorResponse('Failed to link Telegram account')
  }
})

export const DELETE = requireAuth(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const updatedCount = await prisma.telegramUser.updateMany({
      where: {
        userId: user.id,
        isActive: true
      },
      data: {
        isActive: false,
        updatedAt: new Date()
      }
    })

    if (updatedCount.count === 0) {
      return errorResponse('No active Telegram account found', 404)
    }

    return successResponse({
      unlinked: true,
      count: updatedCount.count
    }, 'Telegram account unlinked successfully')

  } catch (error) {
    console.error('Unlink Telegram account error:', error)
    return serverErrorResponse('Failed to unlink Telegram account')
  }
})

export const GET = requireAuth(async (request: NextRequest, user) => {
  try {
    const telegramUsers = await prisma.telegramUser.findMany({
      where: {
        userId: user.id,
        isActive: true
      },
      select: {
        id: true,
        telegramId: true,
        username: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        updatedAt: true
      }
    })

    return successResponse({
      telegramAccounts: telegramUsers,
      isLinked: telegramUsers.length > 0,
      totalAccounts: telegramUsers.length
    }, 'Telegram account status retrieved successfully')

  } catch (error) {
    console.error('Get Telegram status error:', error)
    return serverErrorResponse('Failed to get Telegram status')
  }
})
