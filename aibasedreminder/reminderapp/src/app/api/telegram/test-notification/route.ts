import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 TESTING TELEGRAM NOTIFICATION SYSTEM...')

    // Get users with active Telegram connections
    const users = await prisma.user.findMany({
      where: {
        telegramUsers: {
          some: {
            isActive: true
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with active Telegram connections`)

    let notificationsSent = 0

    for (const user of users) {
      for (const telegramUser of user.telegramUsers) {
        try {
          const testMessage = `🧪 TEST NOTIFICATION\n\n` +
            `📧 This is a test email notification!\n` +
            `👤 User: ${user.email}\n` +
            `📅 Time: ${new Date().toLocaleString()}\n\n` +
            `✅ If you see this, the notification system is working!\n\n` +
            `🔔 You should receive email reminders every 5 minutes for unread emails.`

          // Create test action buttons
          const keyboard = {
            inline_keyboard: [
              [
                { text: "✅ Test Successful", callback_data: `test_success` },
                { text: "❌ Report Issue", callback_data: `test_issue` }
              ]
            ]
          }

          console.log(`📱 Sending test notification to Telegram user ${telegramUser.telegramId}`)

          const response = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              chat_id: telegramUser.telegramId,
              text: testMessage,
              reply_markup: keyboard,
              parse_mode: 'HTML'
            })
          })

          const result = await response.json()
          
          if (result.ok) {
            notificationsSent++
            console.log(`📱 ✅ Test notification sent successfully to ${telegramUser.telegramId}`)
          } else {
            console.error(`📱 ❌ Failed to send test notification:`, result)
          }

        } catch (error) {
          console.error(`❌ Error sending test notification to ${telegramUser.telegramId}:`, error)
        }
      }
    }

    console.log(`🧪 TEST COMPLETE: ${notificationsSent} notifications sent`)

    return successResponse({
      usersChecked: users.length,
      notificationsSent,
      timestamp: new Date().toISOString(),
      message: 'Test notifications sent successfully!'
    }, 'Telegram notification test completed')

  } catch (error) {
    console.error('❌ Telegram notification test failed:', error)
    return serverErrorResponse(`Telegram notification test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Also allow POST
export const POST = GET
