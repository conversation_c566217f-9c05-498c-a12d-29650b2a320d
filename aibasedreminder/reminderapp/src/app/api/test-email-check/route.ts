import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'
import { sendMessage } from '@/lib/telegram'

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing email check system...')
    
    // Get user <NAME_EMAIL>
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        telegramUsers: true,
        oAuthTokens: {
          where: { provider: 'google' }
        }
      }
    })

    if (!user) {
      return successResponse({
        error: 'User not found',
        email: '<EMAIL>'
      }, 'User not found')
    }

    if (user.telegramUsers.length === 0) {
      return successResponse({
        error: 'No Telegram connection',
        user: user.email
      }, 'No Telegram connection')
    }

    if (user.oAuthTokens.length === 0) {
      return successResponse({
        error: 'No Google OAuth token',
        user: user.email
      }, 'No Google OAuth token')
    }

    console.log(`📧 Testing email check for user: ${user.email}`)
    
    // Test Gmail connection
    const { gmail } = await getGoogleServices(user.id)
    
    // Get recent unread emails
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    const query = `is:unread after:${Math.floor(fiveMinutesAgo.getTime() / 1000)}`
    
    const gmailMessages = await gmail.getMessages(query, 5)
    console.log(`📨 Found ${gmailMessages.length} recent unread emails`)
    
    let newEmailsProcessed = 0
    let notificationsSent = 0
    
    for (const gmailMessage of gmailMessages) {
      try {
        // Check if we already have this email
        const existingEmail = await prisma.email.findUnique({
          where: { gmailMessageId: gmailMessage.id }
        })
        
        if (!existingEmail) {
          // This is a new email - save it and send notification
          console.log(`✨ New email found: ${gmailMessage.subject}`)
          
          const newEmail = await prisma.email.create({
            data: {
              userId: user.id,
              gmailMessageId: gmailMessage.id,
              threadId: gmailMessage.threadId || gmailMessage.id,
              subject: gmailMessage.subject || 'No Subject',
              from: gmailMessage.from || 'Unknown Sender',
              to: gmailMessage.to || user.email,
              snippet: gmailMessage.snippet || '',
              receivedAt: new Date(gmailMessage.internalDate || Date.now()),
              isRead: false,
              category: 'inbox',
              labels: gmailMessage.labelIds || [],
              lastReminderSent: new Date()
            }
          })
          
          newEmailsProcessed++
          
          // Send notification to all user's Telegram accounts
          for (const telegramUser of user.telegramUsers) {
            const message = `📧 NEW EMAIL RECEIVED!\n\n📧 From: ${gmailMessage.from}\n📝 Subject: ${gmailMessage.subject}\n📄 Preview: ${gmailMessage.snippet}\n⏰ Received: ${new Date().toLocaleString()}\n\n💡 Reply with: reply:${newEmail.id}:Your message\n📱 Or I'll remind you every 5 minutes!`
            
            await sendMessage(telegramUser.telegramId, message)
            notificationsSent++
            console.log(`📱 Sent notification to Telegram user ${telegramUser.telegramId}`)
          }
        }
      } catch (emailError) {
        console.error(`❌ Error processing email ${gmailMessage.id}:`, emailError)
      }
    }
    
    return successResponse({
      user: user.email,
      telegramUsers: user.telegramUsers.length,
      gmailMessages: gmailMessages.length,
      newEmailsProcessed,
      notificationsSent,
      timestamp: new Date().toISOString()
    }, 'Email check test completed')
    
  } catch (error) {
    console.error('❌ Email check test failed:', error)
    return serverErrorResponse(`Email check test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const POST = GET
