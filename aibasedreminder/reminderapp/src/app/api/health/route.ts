import { NextRequest } from 'next/server'
import { successResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { checkTelegramConnection } from '@/lib/telegram'
import { checkOpenAIConnection } from '@/lib/openai'

export async function GET(request: NextRequest) {
  try {
    let databaseStatus = 'healthy'
    try {
      await prisma.$queryRaw`SELECT 1`
    } catch (error) {
      databaseStatus = 'unhealthy'
    }

    const telegramStatus = await checkTelegramConnection() ? 'healthy' : 'unhealthy'

    const openaiStatus = await checkOpenAIConnection() ? 'healthy' : 'unhealthy'

    const googleApiStatus = process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? 'configured' : 'not_configured'

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: databaseStatus,
        google_api: googleApiStatus,
        telegram_bot: telegramStatus,
        openai: openaiStatus
      }
    }

    return successResponse(healthData, 'Service is healthy')
  } catch (error) {
    return serverErrorResponse('Health check failed')
  }
}
