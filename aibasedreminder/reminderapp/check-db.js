#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('🔍 Checking database...\n');

    // Check users
    const users = await prisma.user.findMany({
      include: {
        telegramUsers: true,
        oauthTokens: true
      }
    });

    console.log(`👥 Total users: ${users.length}`);
    
    for (const user of users) {
      console.log(`\n📧 User: ${user.email}`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Telegram users: ${user.telegramUsers.length}`);
      
      for (const tgUser of user.telegramUsers) {
        console.log(`   - Telegram ID: ${tgUser.telegramId}, Active: ${tgUser.isActive}`);
      }
      
      console.log(`   OAuth tokens: ${user.oauthTokens.length}`);
      for (const token of user.oauthTokens) {
        const isExpired = token.expiresAt < new Date();
        console.log(`   - Provider: ${token.provider}, Expired: ${isExpired}`);
      }
    }

    // Check the specific query used by the scheduler
    console.log('\n🔍 Checking scheduler query...');
    
    const schedulerUsers = await prisma.user.findMany({
      where: {
        telegramUsers: {
          some: { isActive: true }
        },
        oauthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        }
      },
      include: {
        telegramUsers: {
          where: { isActive: true }
        },
        oauthTokens: {
          where: { provider: 'google' }
        }
      }
    });

    console.log(`📊 Users matching scheduler criteria: ${schedulerUsers.length}`);
    
    for (const user of schedulerUsers) {
      console.log(`✅ ${user.email} - Active Telegram: ${user.telegramUsers.length}, Valid Google tokens: ${user.oauthTokens.filter(t => t.expiresAt > new Date()).length}`);
    }

  } catch (error) {
    console.error('❌ Database error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
