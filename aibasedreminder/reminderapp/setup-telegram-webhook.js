#!/usr/bin/env node

/**
 * Telegram Webhook Setup Script
 * This script sets up the webhook for your Telegram bot
 */

const https = require('https');
const { URL } = require('url');

// Configuration from environment variables
const BOT_TOKEN = '7790135863:AAH9UZ-9_5TApGx8BdaFr_YhZIjmR7XCLC0';
const WEBHOOK_URL = 'https://e807-196-190-62-219.ngrok-free.app/api/telegram/webhook';

console.log('🤖 Setting up Telegram Bot Webhook...');
console.log(`Bot Token: ${BOT_TOKEN.substring(0, 10)}...`);
console.log(`Webhook URL: ${WEBHOOK_URL}`);

// Function to make HTTPS request
function makeRequest(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve(parsedData);
        } catch (error) {
          resolve(responseData);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Set up webhook
async function setupWebhook() {
  try {
    console.log('\n📡 Setting webhook...');
    
    const setWebhookUrl = `https://api.telegram.org/bot${BOT_TOKEN}/setWebhook`;
    const webhookData = {
      url: WEBHOOK_URL,
      allowed_updates: ['message', 'callback_query', 'inline_query']
    };

    const result = await makeRequest(setWebhookUrl, 'POST', webhookData);
    
    if (result.ok) {
      console.log('✅ Webhook set successfully!');
      console.log(`📍 Webhook URL: ${WEBHOOK_URL}`);
    } else {
      console.log('❌ Failed to set webhook:');
      console.log(result);
    }

    // Get webhook info to verify
    console.log('\n🔍 Verifying webhook...');
    const getWebhookUrl = `https://api.telegram.org/bot${BOT_TOKEN}/getWebhookInfo`;
    const webhookInfo = await makeRequest(getWebhookUrl);
    
    if (webhookInfo.ok) {
      console.log('✅ Webhook info retrieved:');
      console.log(`   URL: ${webhookInfo.result.url}`);
      console.log(`   Pending updates: ${webhookInfo.result.pending_update_count}`);
      console.log(`   Last error: ${webhookInfo.result.last_error_message || 'None'}`);
    }

    // Get bot info
    console.log('\n🤖 Bot information:');
    const getBotUrl = `https://api.telegram.org/bot${BOT_TOKEN}/getMe`;
    const botInfo = await makeRequest(getBotUrl);
    
    if (botInfo.ok) {
      console.log(`   Bot name: ${botInfo.result.first_name}`);
      console.log(`   Username: @${botInfo.result.username}`);
      console.log(`   Bot ID: ${botInfo.result.id}`);
    }

    console.log('\n🎉 Telegram bot setup complete!');
    console.log('\n📋 Next steps:');
    console.log('1. Make sure your backend server is running on the ngrok URL');
    console.log('2. Send /start to your bot to test the connection');
    console.log('3. Check the backend logs for incoming webhook requests');
    
  } catch (error) {
    console.error('❌ Error setting up webhook:', error.message);
  }
}

// Run the setup
setupWebhook();
