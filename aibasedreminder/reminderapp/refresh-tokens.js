#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const { google } = require('googleapis');

const prisma = new PrismaClient();

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const GOOGLE_REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI;

function createOAuth2Client() {
  return new google.auth.OAuth2(
    GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET,
    GOOGLE_REDIRECT_URI
  );
}

async function refreshAllTokens() {
  try {
    console.log('🔄 Refreshing all expired OAuth tokens...\n');

    // Find all expired Google OAuth tokens with refresh tokens
    const expiredTokens = await prisma.oAuthToken.findMany({
      where: {
        provider: 'google',
        expiresAt: {
          lt: new Date()
        },
        refreshToken: {
          not: null
        }
      },
      include: {
        user: true
      }
    });

    console.log(`Found ${expiredTokens.length} expired tokens to refresh\n`);

    for (const tokenRecord of expiredTokens) {
      try {
        console.log(`🔄 Refreshing token for ${tokenRecord.user.email}...`);

        const oauth2Client = createOAuth2Client();
        oauth2Client.setCredentials({
          access_token: tokenRecord.accessToken,
          refresh_token: tokenRecord.refreshToken,
          expiry_date: tokenRecord.expiresAt.getTime()
        });

        // Refresh the token
        const { credentials } = await oauth2Client.refreshAccessToken();
        
        // Update the database
        await prisma.oAuthToken.update({
          where: { id: tokenRecord.id },
          data: {
            accessToken: credentials.access_token,
            expiresAt: new Date(credentials.expiry_date || Date.now() + 3600000)
          }
        });

        console.log(`✅ Successfully refreshed token for ${tokenRecord.user.email}`);
        console.log(`   New expiry: ${new Date(credentials.expiry_date || Date.now() + 3600000).toISOString()}\n`);

      } catch (error) {
        console.error(`❌ Failed to refresh token for ${tokenRecord.user.email}:`, error.message);
        console.log(`   This user may need to re-authenticate\n`);
      }
    }

    // Check final status
    console.log('🔍 Final token status:');
    const allTokens = await prisma.oAuthToken.findMany({
      where: { provider: 'google' },
      include: { user: true }
    });

    for (const token of allTokens) {
      const isExpired = token.expiresAt < new Date();
      console.log(`📧 ${token.user.email}: ${isExpired ? '❌ Still Expired' : '✅ Valid'}`);
    }

  } catch (error) {
    console.error('❌ Error refreshing tokens:', error);
  } finally {
    await prisma.$disconnect();
  }
}

refreshAllTokens();
