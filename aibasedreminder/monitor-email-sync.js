#!/usr/bin/env node

// Monitor script to continuously check for new emails
const http = require('http');

console.log('🔍 Starting email monitoring...');
console.log('📧 This will check for new emails every 5 seconds');
console.log('🚨 When a new email arrives, it will be sent to Telegram immediately!');
console.log('⏹️  Press Ctrl+C to stop monitoring\n');

let checkCount = 0;

function checkEmails() {
  checkCount++;
  const timestamp = new Date().toLocaleTimeString();
  
  console.log(`[${timestamp}] 🔍 Check #${checkCount} - Looking for new emails...`);

  const postData = JSON.stringify({});

  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/emails/sync-immediate',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        
        if (result.success) {
          if (result.data.newEmailsFound > 0) {
            console.log(`🚨 [${timestamp}] FOUND ${result.data.newEmailsFound} NEW EMAIL(S)! Telegram notification sent! 🚨`);
          } else {
            console.log(`📭 [${timestamp}] No new emails found`);
          }
        } else {
          console.log(`❌ [${timestamp}] Error: ${result.error}`);
        }
      } catch (error) {
        console.log(`❌ [${timestamp}] Parse error:`, error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.error(`❌ [${timestamp}] Request error:`, error.message);
  });

  req.write(postData);
  req.end();
}

// Check immediately
checkEmails();

// Then check every 5 seconds
const interval = setInterval(checkEmails, 5000);

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping email monitoring...');
  clearInterval(interval);
  console.log('✅ Email monitoring stopped');
  process.exit(0);
});
