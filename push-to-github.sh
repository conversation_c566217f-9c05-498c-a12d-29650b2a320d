#!/bin/bash

# Script to push changes to GitHub HojiwakY branch

echo "🚀 Pushing Telegram bot fixes to GitHub..."

# Set working directory
cd /home/<USER>/Desktop/Reminder

# Check git status
echo "📋 Checking git status..."
git status

# Add all changes
echo "➕ Adding all changes..."
git add .

# Check if there are changes to commit
if git diff --staged --quiet; then
    echo "ℹ️ No changes to commit"
else
    echo "💾 Committing changes..."
    git commit -m "🤖 Fix Telegram bot independence and enhance email reply functionality

✅ Major improvements:
- Remove website refresh dependencies from Telegram bot
- Add interactive reply buttons for better UX  
- Implement continuous email monitoring system
- Create external monitoring setup for 5-minute email checks
- Enhance reply functionality with direct email selection
- Add proper email threading support
- Remove internal API call dependencies from webhook

🔧 Technical changes:
- Updated webhook handler to work independently
- Added reply_to callback handlers with interactive buttons
- Created continuous monitoring endpoint
- Set up external monitoring documentation
- Improved error handling and user experience
- Added proper CORS headers for external triggers

🚀 Bot now operates completely independently without website refresh!"
fi

# Push to HojiwakY branch
echo "🚀 Pushing to HojiwakY branch..."
git push -u origin HojiwakY

echo "✅ Push completed!"
